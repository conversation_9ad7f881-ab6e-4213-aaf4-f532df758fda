<?php

use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;

return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        web: __DIR__.'/../routes/web.php',
        commands: __DIR__.'/../routes/console.php',
        health: '/up',
    )
    ->withMiddleware(function (Middleware $middleware): void {
        // Add security headers middleware to web group
        $middleware->web(append: [
            \App\Http\Middleware\SecurityHeadersMiddleware::class,
        ]);
        
        // Configure throttling for API endpoints
        $middleware->throttleApi();
        
        // Add CSRF protection
        $middleware->validateCsrfTokens();
    })
    ->withExceptions(function (Exceptions $exceptions): void {
        //
    })->create();
