import defaultTheme from 'tailwindcss/defaultTheme';
import forms from '@tailwindcss/forms';
import typography from '@tailwindcss/typography';
import aspectRatio from '@tailwindcss/aspect-ratio';

/** @type {import('tailwindcss').Config} */
export default {
    content: [
        './vendor/laravel/framework/src/Illuminate/Pagination/resources/views/*.blade.php',
        './storage/framework/views/*.php',
        './resources/views/**/*.blade.php',
        './resources/js/**/*.js',
    ],

    theme: {
        extend: {
            colors: {
                // Nature-luxury safari palette
                safari: {
                    50: '#f7f7f4',
                    100: '#eeede6',
                    200: '#dddacc',
                    300: '#c8c2a8',
                    400: '#b1a682',
                    500: '#9f9066',
                    600: '#8b7d57',
                    700: '#736749',
                    800: '#5f553f',
                    900: '#514936',
                    950: '#2a251b',
                },
                forest: {
                    50: '#f0f9f0',
                    100: '#ddf2dd',
                    200: '#bde5be',
                    300: '#8fd191',
                    400: '#5ab65e',
                    500: '#369b3b',
                    600: '#277d2c',
                    700: '#226325',
                    800: '#204f23',
                    900: '#1c421f',
                    950: '#0c240e',
                },
                earth: {
                    50: '#faf8f3',
                    100: '#f4efe4',
                    200: '#e7dcc7',
                    300: '#d6c4a2',
                    400: '#c4a67d',
                    500: '#b69161',
                    600: '#a87f55',
                    700: '#8b6848',
                    800: '#71553f',
                    900: '#5c4735',
                    950: '#31241b',
                },
                stone: {
                    50: '#fafaf9',
                    100: '#f5f5f4',
                    200: '#e7e5e4',
                    300: '#d6d3d1',
                    400: '#a8a29e',
                    500: '#78716c',
                    600: '#57534e',
                    700: '#44403c',
                    800: '#292524',
                    900: '#1c1917',
                    950: '#0c0a09',
                },
                accent: {
                    amber: {
                        50: '#fffbeb',
                        100: '#fef3c7',
                        200: '#fde68a',
                        300: '#fcd34d',
                        400: '#fbbf24',
                        500: '#f59e0b',
                        600: '#d97706',
                        700: '#b45309',
                        800: '#92400e',
                        900: '#78350f',
                        950: '#451a03',
                    },
                },
                success: '#10b981',
                warning: '#f59e0b',
                error: '#ef4444',
            },
            fontFamily: {
                sans: ['Inter', 'ui-sans-serif', 'system-ui', ...defaultTheme.fontFamily.sans],
                serif: ['Crimson Text', 'ui-serif', 'Georgia', ...defaultTheme.fontFamily.serif],
                display: ['Playfair Display', 'ui-serif', 'Georgia', ...defaultTheme.fontFamily.serif],
            },
            fontSize: {
                // Fluid typography scale
                'xs': ['0.75rem', { lineHeight: '1rem' }],
                'sm': ['0.875rem', { lineHeight: '1.25rem' }],
                'base': ['1rem', { lineHeight: '1.5rem' }],
                'lg': ['1.125rem', { lineHeight: '1.75rem' }],
                'xl': ['1.25rem', { lineHeight: '1.75rem' }],
                '2xl': ['1.5rem', { lineHeight: '2rem' }],
                '3xl': ['1.875rem', { lineHeight: '2.25rem' }],
                '4xl': ['2.25rem', { lineHeight: '2.5rem' }],
                '5xl': ['3rem', { lineHeight: '1.16' }],
                '6xl': ['3.75rem', { lineHeight: '1.1' }],
                '7xl': ['4.5rem', { lineHeight: '1.05' }],
                '8xl': ['6rem', { lineHeight: '1' }],
                '9xl': ['8rem', { lineHeight: '1' }],
                // Responsive fluid text
                'fluid-sm': 'clamp(0.875rem, 0.8rem + 0.375vw, 1rem)',
                'fluid-base': 'clamp(1rem, 0.9rem + 0.5vw, 1.125rem)',
                'fluid-lg': 'clamp(1.125rem, 1rem + 0.625vw, 1.25rem)',
                'fluid-xl': 'clamp(1.25rem, 1.1rem + 0.75vw, 1.5rem)',
                'fluid-2xl': 'clamp(1.5rem, 1.3rem + 1vw, 2rem)',
                'fluid-3xl': 'clamp(1.875rem, 1.6rem + 1.375vw, 2.5rem)',
                'fluid-4xl': 'clamp(2.25rem, 1.9rem + 1.75vw, 3rem)',
                'fluid-5xl': 'clamp(3rem, 2.4rem + 3vw, 4rem)',
            },
            spacing: {
                '18': '4.5rem',
                '88': '22rem',
                '92': '23rem',
                '96': '24rem',
                '128': '32rem',
                '144': '36rem',
            },
            borderRadius: {
                '4xl': '2rem',
                '5xl': '2.5rem',
            },
            boxShadow: {
                'soft': '0 2px 15px -3px rgba(0, 0, 0, 0.07), 0 10px 20px -2px rgba(0, 0, 0, 0.04)',
                'medium': '0 4px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 30px -5px rgba(0, 0, 0, 0.05)',
                'hard': '0 8px 30px -12px rgba(0, 0, 0, 0.25)',
                'inner-soft': 'inset 0 2px 4px 0 rgba(0, 0, 0, 0.03)',
            },
            animation: {
                'fade-in': 'fadeIn 0.5s ease-in-out',
                'slide-up': 'slideUp 0.5s ease-out',
                'slide-down': 'slideDown 0.5s ease-out',
                'scale-in': 'scaleIn 0.3s ease-out',
                'float': 'float 6s ease-in-out infinite',
            },
            keyframes: {
                fadeIn: {
                    '0%': { opacity: '0' },
                    '100%': { opacity: '1' },
                },
                slideUp: {
                    '0%': { transform: 'translateY(10px)', opacity: '0' },
                    '100%': { transform: 'translateY(0)', opacity: '1' },
                },
                slideDown: {
                    '0%': { transform: 'translateY(-10px)', opacity: '0' },
                    '100%': { transform: 'translateY(0)', opacity: '1' },
                },
                scaleIn: {
                    '0%': { transform: 'scale(0.9)', opacity: '0' },
                    '100%': { transform: 'scale(1)', opacity: '1' },
                },
                float: {
                    '0%, 100%': { transform: 'translateY(0px)' },
                    '50%': { transform: 'translateY(-5px)' },
                },
            },
            backdropBlur: {
                xs: '2px',
            },
            maxWidth: {
                '8xl': '88rem',
                '9xl': '96rem',
            },
            zIndex: {
                '60': '60',
                '70': '70',
                '80': '80',
                '90': '90',
                '100': '100',
            },
        },
        container: {
            center: true,
            padding: {
                DEFAULT: '1rem',
                sm: '2rem',
                lg: '4rem',
                xl: '5rem',
                '2xl': '6rem',
            },
            screens: {
                sm: '640px',
                md: '768px',
                lg: '1024px',
                xl: '1280px',
                '2xl': '1400px',
            },
        },
    },

    plugins: [
        forms({
            strategy: 'class',
        }),
        typography({
            className: 'prose',
        }),
        aspectRatio,
    ],
};
