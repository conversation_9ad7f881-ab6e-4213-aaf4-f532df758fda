<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Performance Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration for performance optimization features including
    | caching, compression, and resource optimization.
    |
    */

    'cache' => [
        'default_ttl' => env('CACHE_DEFAULT_TTL', 3600), // 1 hour
        'long_ttl' => env('CACHE_LONG_TTL', 86400), // 24 hours
        'short_ttl' => env('CACHE_SHORT_TTL', 300), // 5 minutes
        
        'tags' => [
            'accommodations' => 'accommodations',
            'activities' => 'activities',
            'facilities' => 'facilities',
            'blog' => 'blog',
            'pages' => 'pages',
            'settings' => 'settings',
            'seo' => 'seo',
        ],
        
        'keys' => [
            'accommodation_list' => 'accommodations.list',
            'accommodation_featured' => 'accommodations.featured',
            'activity_list' => 'activities.list',
            'facility_list' => 'facilities.list',
            'navigation' => 'site.navigation',
            'settings' => 'site.settings',
        ],
    ],

    'compression' => [
        'enabled' => env('COMPRESSION_ENABLED', true),
        'level' => env('COMPRESSION_LEVEL', 6), // 1-9, higher = better compression
        'types' => [
            'text/html',
            'text/css',
            'text/javascript',
            'application/javascript',
            'application/json',
            'application/xml',
            'text/xml',
            'image/svg+xml',
        ],
    ],

    'images' => [
        'formats' => [
            'webp' => [
                'quality' => 85,
                'enabled' => true,
            ],
            'avif' => [
                'quality' => 80,
                'enabled' => function_exists('imageavif'),
            ],
            'jpeg' => [
                'quality' => 90,
                'progressive' => true,
            ],
            'png' => [
                'compression' => 6,
            ],
        ],
        
        'responsive' => [
            'breakpoints' => [320, 640, 768, 1024, 1280, 1536],
            'sizes' => [
                'thumbnail' => [150, 150],
                'small' => [400, 300],
                'medium' => [800, 600],
                'large' => [1200, 900],
                'hero' => [1920, 1080],
            ],
        ],
        
        'lazy_loading' => [
            'enabled' => true,
            'threshold' => '10px',
            'placeholder' => 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMSIgaGVpZ2h0PSIxIiB2aWV3Qm94PSIwIDAgMSAxIiBmaWxsPSJub25lIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPjxyZWN0IHdpZHRoPSIxIiBoZWlnaHQ9IjEiIGZpbGw9IiNmM2Y0ZjYiLz48L3N2Zz4=',
        ],
    ],

    'http_cache' => [
        'enabled' => env('HTTP_CACHE_ENABLED', true),
        'default_ttl' => 3600, // 1 hour
        
        'rules' => [
            'static_assets' => [
                'pattern' => '/\.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$/',
                'ttl' => 31536000, // 1 year
                'immutable' => true,
            ],
            'images' => [
                'pattern' => '/^\/storage\/.*\.(png|jpg|jpeg|gif|webp|avif)$/',
                'ttl' => 604800, // 1 week
                'immutable' => false,
            ],
            'pages' => [
                'pattern' => '/^\/(?!admin|api|livewire)/',
                'ttl' => 3600, // 1 hour
                'vary' => ['Accept-Encoding', 'User-Agent'],
            ],
        ],
        
        'security_headers' => [
            'X-Content-Type-Options' => 'nosniff',
            'X-Frame-Options' => 'SAMEORIGIN',
            'X-XSS-Protection' => '1; mode=block',
            'Referrer-Policy' => 'strict-origin-when-cross-origin',
            'Permissions-Policy' => 'camera=(), microphone=(), geolocation=()',
        ],
        
        'csp' => [
            'enabled' => env('CSP_ENABLED', true),
            'report_only' => env('CSP_REPORT_ONLY', false),
            'directives' => [
                'default-src' => "'self'",
                'script-src' => "'self' 'unsafe-inline' 'unsafe-eval' *.googletagmanager.com *.google-analytics.com *.facebook.net",
                'style-src' => "'self' 'unsafe-inline' fonts.googleapis.com",
                'font-src' => "'self' fonts.gstatic.com",
                'img-src' => "'self' data: *.googletagmanager.com *.google-analytics.com *.facebook.com",
                'connect-src' => "'self' *.google-analytics.com *.facebook.net",
                'frame-src' => "'self' *.youtube.com *.vimeo.com",
                'object-src' => "'none'",
                'base-uri' => "'self'",
                'form-action' => "'self'",
            ],
        ],
    ],

    'minification' => [
        'html' => [
            'enabled' => env('MINIFY_HTML', false), // Disabled by default for debugging
            'options' => [
                'removeComments' => true,
                'removeEmptyElements' => false,
                'removeOptionalTags' => false,
            ],
        ],
        
        'css' => [
            'enabled' => env('MINIFY_CSS', true),
            'combine' => env('COMBINE_CSS', false),
        ],
        
        'js' => [
            'enabled' => env('MINIFY_JS', true),
            'combine' => env('COMBINE_JS', false),
        ],
    ],

    'preloading' => [
        'dns_prefetch' => [
            'fonts.googleapis.com',
            'fonts.gstatic.com',
            'www.googletagmanager.com',
            'www.google-analytics.com',
        ],
        
        'preconnect' => [
            'https://fonts.googleapis.com',
            'https://fonts.gstatic.com',
        ],
        
        'critical_resources' => [
            'fonts' => [
                'Inter:wght@300;400;500;600;700',
                'Playfair+Display:wght@400;500;600;700',
            ],
        ],
    ],

    'monitoring' => [
        'performance_budget' => [
            'first_contentful_paint' => 1.5, // seconds
            'largest_contentful_paint' => 2.5, // seconds
            'cumulative_layout_shift' => 0.1,
            'first_input_delay' => 0.1, // seconds
            'total_blocking_time' => 0.3, // seconds
        ],
        
        'lighthouse_targets' => [
            'performance' => 90,
            'accessibility' => 90,
            'best_practices' => 90,
            'seo' => 90,
        ],
    ],
];
