<?php

declare(strict_types=1);

return [
    /*
    |--------------------------------------------------------------------------
    | Sitemap Configuration
    |--------------------------------------------------------------------------
    |
    | This configuration defines the site structure for Malombo Selous Forest Camp.
    | Used for generating sitemaps, navigation menus, and breadcrumbs.
    |
    */

    'public_routes' => [
        'home' => [
            'title' => 'Home',
            'description' => 'Welcome to Malombo Selous Forest Camp',
            'priority' => 1.0,
            'changefreq' => 'weekly',
        ],

        'lodge' => [
            'title' => 'The Lodge',
            'description' => 'About Malombo Selous Forest Camp and the safari experience',
            'priority' => 0.9,
            'changefreq' => 'monthly',
            'children' => [
                'lodge.about' => [
                    'title' => 'About Us',
                    'description' => 'Learn about our history and philosophy',
                    'priority' => 0.8,
                ],
                'lodge.experience' => [
                    'title' => 'Safari Experience',
                    'description' => 'Discover the unique safari experience we offer',
                    'priority' => 0.8,
                ],
                'lodge.history' => [
                    'title' => 'Our History',
                    'description' => 'The story behind Malombo Selous Forest Camp',
                    'priority' => 0.6,
                ],
            ],
        ],

        'accommodation' => [
            'title' => 'Accommodation',
            'description' => 'Luxury safari accommodation options',
            'priority' => 0.9,
            'changefreq' => 'monthly',
        ],

        'activities' => [
            'title' => 'Activities',
            'description' => 'Safari activities and adventures',
            'priority' => 0.8,
            'changefreq' => 'monthly',
        ],

        'facilities' => [
            'title' => 'Facilities & Amenities',
            'description' => 'Camp facilities and guest amenities',
            'priority' => 0.7,
            'changefreq' => 'monthly',
        ],

        'rates' => [
            'title' => 'Rates & Policies',
            'description' => 'Accommodation rates and booking policies',
            'priority' => 0.8,
            'changefreq' => 'monthly',
            'children' => [
                'rates.policies' => [
                    'title' => 'Booking Policies',
                    'description' => 'Terms and conditions for bookings',
                    'priority' => 0.6,
                ],
                'rates.seasons' => [
                    'title' => 'Seasonal Rates',
                    'description' => 'Rates by season and availability',
                    'priority' => 0.7,
                ],
            ],
        ],

        'gallery' => [
            'title' => 'Gallery',
            'description' => 'Photos of the camp, wildlife, and experiences',
            'priority' => 0.6,
            'changefreq' => 'weekly',
        ],

        'location' => [
            'title' => 'Location & Getting Here',
            'description' => 'How to reach Malombo Selous Forest Camp',
            'priority' => 0.7,
            'changefreq' => 'yearly',
            'children' => [
                'location.getting-here' => [
                    'title' => 'Getting Here',
                    'description' => 'Travel information and directions',
                    'priority' => 0.7,
                ],
                'location.nearby' => [
                    'title' => 'Nearby Attractions',
                    'description' => 'Points of interest near the camp',
                    'priority' => 0.5,
                ],
            ],
        ],

        'contact' => [
            'title' => 'Contact & Reservations',
            'description' => 'Get in touch and make a booking enquiry',
            'priority' => 0.9,
            'changefreq' => 'yearly',
            'children' => [
                'contact.reservations' => [
                    'title' => 'Reservations',
                    'description' => 'Make a booking enquiry',
                    'priority' => 0.9,
                ],
            ],
        ],

        'blog' => [
            'title' => 'Blog & News',
            'description' => 'Latest news and safari stories',
            'priority' => 0.6,
            'changefreq' => 'weekly',
        ],
    ],

    'admin_routes' => [
        'admin.dashboard' => [
            'title' => 'Dashboard',
            'icon' => 'dashboard',
            'permission' => null,
        ],

        'admin.pages' => [
            'title' => 'Pages',
            'icon' => 'document-text',
            'permission' => 'view content',
        ],

        'admin.accommodations' => [
            'title' => 'Accommodations',
            'icon' => 'home',
            'permission' => 'view content',
        ],

        'admin.activities' => [
            'title' => 'Activities',
            'icon' => 'map',
            'permission' => 'view content',
        ],

        'admin.facilities' => [
            'title' => 'Facilities',
            'icon' => 'building-office-2',
            'permission' => 'view content',
        ],

        'admin.media' => [
            'title' => 'Gallery & Media',
            'icon' => 'photo',
            'permission' => 'view media',
        ],

        'admin.rates' => [
            'title' => 'Rates & Seasons',
            'icon' => 'currency-dollar',
            'permission' => 'view content',
        ],

        'admin.policies' => [
            'title' => 'Policies',
            'icon' => 'document-check',
            'permission' => 'view content',
        ],

        'admin.faqs' => [
            'title' => 'FAQs',
            'icon' => 'question-mark-circle',
            'permission' => 'view content',
        ],

        'admin.blog' => [
            'title' => 'Blog',
            'icon' => 'newspaper',
            'permission' => 'view content',
        ],

        'admin.enquiries' => [
            'title' => 'Enquiries (Inbox)',
            'icon' => 'inbox',
            'permission' => 'view bookings',
        ],

        'admin.settings' => [
            'title' => 'Settings',
            'icon' => 'cog-6-tooth',
            'permission' => 'view settings',
            'children' => [
                'admin.settings.contacts' => [
                    'title' => 'Contact Information',
                    'permission' => 'view settings',
                ],
                'admin.settings.social' => [
                    'title' => 'Social Media',
                    'permission' => 'view settings',
                ],
                'admin.settings.seo' => [
                    'title' => 'SEO & Metadata',
                    'permission' => 'view settings',
                ],
                'admin.settings.general' => [
                    'title' => 'General Settings',
                    'permission' => 'view settings',
                ],
            ],
        ],
    ],

    'system_routes' => [
        'error.404' => [
            'title' => 'Page Not Found',
            'exclude_from_sitemap' => true,
        ],
        'error.500' => [
            'title' => 'Server Error',
            'exclude_from_sitemap' => true,
        ],
        'error.503' => [
            'title' => 'Site Maintenance',
            'exclude_from_sitemap' => true,
        ],
        'legal.privacy' => [
            'title' => 'Privacy Policy',
            'priority' => 0.3,
            'changefreq' => 'yearly',
        ],
        'legal.cookies' => [
            'title' => 'Cookie Policy',
            'priority' => 0.3,
            'changefreq' => 'yearly',
        ],
        'legal.terms' => [
            'title' => 'Terms of Service',
            'priority' => 0.3,
            'changefreq' => 'yearly',
        ],
        'legal.disclaimer' => [
            'title' => 'Disclaimer',
            'priority' => 0.2,
            'changefreq' => 'yearly',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Navigation Menus
    |--------------------------------------------------------------------------
    |
    | Define the structure for different navigation menus.
    |
    */

    'main_navigation' => [
        'home',
        'lodge',
        'accommodation',
        'activities',
        'facilities',
        'gallery',
        'blog',
        'contact',
    ],

    'footer_navigation' => [
        'accommodation',
        'activities',
        'facilities',
        'rates',
        'location',
        'contact',
        'legal.privacy',
        'legal.terms',
    ],

    'admin_navigation' => [
        'admin.dashboard',
        'admin.pages',
        'admin.accommodations',
        'admin.activities',
        'admin.facilities',
        'admin.media',
        'admin.rates',
        'admin.policies',
        'admin.faqs',
        'admin.blog',
        'admin.enquiries',
        'admin.settings',
    ],

    /*
    |--------------------------------------------------------------------------
    | Route Patterns
    |--------------------------------------------------------------------------
    |
    | Common route parameter patterns for consistency.
    |
    */

    'route_patterns' => [
        'slug' => '[a-z0-9]+(?:-[a-z0-9]+)*',
        'id' => '[0-9]+',
        'uuid' => '[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}',
    ],
];
