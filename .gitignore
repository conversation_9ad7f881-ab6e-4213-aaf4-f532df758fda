*.log
.DS_Store
.env
.env.backup
.env.production
.env.staging
.phpactor.json
.phpunit.result.cache
/.fleet
/.idea
/.nova
/.phpunit.cache
/.vscode
/.zed
/auth.json
/node_modules
/public/build
/public/hot
/public/storage
/storage/*.key
/storage/pail
/vendor
Homestead.json
Homestead.yaml
Thumbs.db

# IDE Helper files
_ide_helper.php
_ide_helper_models.php
.phpstorm.meta.php

# Laravel specific
/bootstrap/cache/*.php
/storage/framework/cache/data/*
/storage/framework/sessions/*
/storage/framework/views/*
/storage/logs/*.log

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Media files (will be stored in S3 in production)
/storage/app/public/media/*
!/storage/app/public/media/.gitkeep
