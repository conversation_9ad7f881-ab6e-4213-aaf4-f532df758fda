# Phase 3 - Design System (UI/UX) - COMPLETE

## Overview

The Malombo Selous Forest Camp design system implements a **nature-luxury safari vibe** with:

- **Aesthetic**: Warm earth tones, forest greens, lots of white space, soft rounded corners, subtle shadows
- **Typography**: Large, legible typography with fluid responsive scaling
- **Photography**: Immersive, high-quality images with proper aspect ratios
- **Accessibility**: WCAG 2.2 AA compliant with proper contrast, focus states, keyboard navigation

## 🎨 Design Tokens

### Color Palette

```css
/* Primary Nature Colors */
safari.600    /* #8b7d57 - Warm khaki for primary actions */
forest.600    /* #277d2c - Deep forest green for CTAs */
earth.600     /* #a87f55 - Rich earth brown for accents */
stone.50-950  /* #fafaf9 to #0c0a09 - Neutral grays */

/* Accent Colors */
accent.amber.400  /* #fbbf24 - Highlight color for special elements */

/* Semantic Colors */
success: #10b981   /* Forest green variant */
warning: #f59e0b   /* Amber for warnings */
error: #ef4444     /* Red for errors */
```

### Typography Scale

```css
/* Fluid Typography - Responsive between screen sizes */
fluid-sm:   clamp(0.875rem, 0.8rem + 0.375vw, 1rem)
fluid-base: clamp(1rem, 0.9rem + 0.5vw, 1.125rem)
fluid-lg:   clamp(1.125rem, 1rem + 0.625vw, 1.25rem)
fluid-xl:   clamp(1.25rem, 1.1rem + 0.75vw, 1.5rem)
fluid-2xl:  clamp(1.5rem, 1.3rem + 1vw, 2rem)
fluid-3xl:  clamp(1.875rem, 1.6rem + 1.375vw, 2.5rem)
fluid-4xl:  clamp(2.25rem, 1.9rem + 1.75vw, 3rem)
fluid-5xl:  clamp(3rem, 2.4rem + 3vw, 4rem)

/* Font Families */
font-sans:    'Inter' (body text)
font-serif:   'Crimson Text' (reading content)
font-display: 'Playfair Display' (headings)
```

### Shadows & Effects

```css
shadow-soft:   /* Subtle elevation */
shadow-medium: /* Standard cards */
shadow-hard:   /* Prominent elements */
shadow-inner-soft: /* Inset elements */

/* Custom safari-themed shadows */
shadow-safari:    /* Safari-tinted soft shadow */
shadow-safari-lg: /* Safari-tinted large shadow */
```

### Spacing & Layout

```css
/* Container Widths */
max-w-7xl: 80rem (1280px) - Standard content
max-w-8xl: 88rem (1408px) - Wide layouts
max-w-9xl: 96rem (1536px) - Extra wide

/* Custom Spacing */
py-16 lg:py-24 - Section spacing
py-8 lg:py-12  - Small section spacing
```

## 🧩 Component Library

### 1. Navigation (`components/navbar.blade.php`)

**Features:**
- Sticky header with scroll-based styling
- Transparent overlay option for hero sections
- Responsive mobile menu with smooth transitions
- Accessibility: ARIA labels, keyboard navigation
- Logo + tagline combination
- Dropdown menus with hover/focus states

**Props:**
- `transparent` (boolean) - Transparent background
- `fixed` (boolean) - Fixed positioning

**Usage:**
```blade
<x-navbar :transparent="true" />
<x-navbar :fixed="false" />
```

### 2. Footer (`components/footer.blade.php`)

**Features:**
- Comprehensive site links and contact information
- Newsletter subscription form
- Social media links
- Legal links (privacy, terms)
- Responsive grid layout
- Dark theme with forest branding

**Props:**
- `variant` - 'default' or 'minimal'

### 3. Hero Section (`components/hero.blade.php`)

**Features:**
- Full-screen or custom height options
- Background image with overlay controls
- Animated text entrance effects
- Responsive typography scaling
- CTA button with hover effects
- Scroll indicator for full-screen heroes

**Props:**
- `backgroundImage` - Image URL
- `title` - Main heading (supports HTML)
- `subtitle` - Supporting text
- `tagline` - Small text above title
- `ctaText` / `ctaUrl` - Call-to-action button
- `height` - 'screen', 'lg', 'md', 'sm'
- `overlay` - 'dark', 'light', 'none'
- `textAlign` - 'center', 'left', 'right'

**Usage:**
```blade
<x-hero 
    backgroundImage="/images/safari-hero.jpg"
    title="Experience the<br><span class=\"text-accent-amber-300\">Ultimate Safari</span>"
    subtitle="Your dream African adventure awaits"
    tagline="It's really where safari begins"
    ctaText="Book Now"
    ctaUrl="{{ route('booking') }}"
    height="screen"
    overlay="dark"
/>
```

### 4. Card Component (`components/card.blade.php`)

**Features:**
- Flexible content display for accommodations/activities
- Price display with formatting
- Feature lists with checkmarks
- Hover animations and focus states
- Horizontal/vertical orientations
- Featured variant with special styling

**Props:**
- `title` - Card title
- `description` - Main content
- `image` / `imageAlt` - Card image
- `url` - Link destination
- `price` / `priceLabel` / `currency` - Pricing info
- `features` - Array of feature text
- `variant` - 'default', 'featured', 'compact'
- `orientation` - 'vertical', 'horizontal'

### 5. Feature List (`components/feature-list.blade.php`)

**Features:**
- Grid, list, or inline layouts
- Customizable icons (check, arrow, star, custom)
- Responsive column counts
- Rich feature objects or simple text
- Multiple size variants

**Props:**
- `features` - Array of features
- `title` / `description` - Section headers
- `layout` - 'grid', 'list', 'inline'
- `columns` - '2', '3', '4'
- `iconStyle` - 'check', 'arrow', 'star', 'custom'
- `size` - 'small', 'default', 'large'

### 6. Testimonial (`components/testimonial.blade.php`)

**Features:**
- Star ratings display
- Author information with avatars
- Multiple layout options
- Date formatting
- Generated avatars for missing images

**Props:**
- `quote` - Testimonial text
- `author` / `title` / `location` - Author details
- `avatar` - Profile image URL
- `rating` - 1-5 star rating
- `date` - Testimonial date
- `variant` - 'default', 'featured', 'compact'
- `layout` - 'card', 'quote', 'minimal'

### 7. Image Gallery (`components/image-gallery.blade.php`)

**Features:**
- Masonry, grid, or carousel layouts
- Lightbox modal with navigation
- Lazy loading for performance
- Responsive column counts
- Image captions and titles
- Keyboard navigation in lightbox

**Props:**
- `images` - Array of image objects
- `title` / `description` - Section headers
- `layout` - 'masonry', 'grid', 'carousel'
- `columns` - '2', '3', '4'
- `lightbox` - Enable/disable lightbox
- `lazy` - Enable lazy loading

### 8. CTA Banner (`components/cta-banner.blade.php`)

**Features:**
- Multiple visual variants
- Primary and secondary actions
- Background image support
- Responsive sizing
- Text alignment options

**Props:**
- `title` / `subtitle` - Content
- `ctaText` / `ctaUrl` - Primary action
- `secondaryCtaText` / `secondaryCtaUrl` - Secondary action
- `backgroundImage` - Background image
- `variant` - 'default', 'gradient', 'image', 'minimal'
- `size` - 'small', 'default', 'large'
- `alignment` - 'left', 'center', 'right'

### 9. Contact Form (`components/contact-form.blade.php`)

**Features:**
- Configurable field types and validation
- AJAX form submission
- Loading states and feedback
- Accessibility-compliant labels and ARIA
- Custom field definitions
- Success/error messaging

**Props:**
- `title` / `description` - Form headers
- `action` / `method` - Form submission
- `fields` - Custom field configuration
- `showDefaultFields` - Include standard fields
- `variant` - 'default', 'minimal', 'inline'
- `size` - 'small', 'default', 'large'

### 10. Map Section (`components/map-section.blade.php`)

**Features:**
- Interactive map with Leaflet.js
- Contact information sidebar
- Satellite/roadmap toggle
- Directions integration
- Responsive layout
- Accessible map controls

**Props:**
- `title` / `description` - Section headers
- `latitude` / `longitude` - Map coordinates
- `zoom` - Map zoom level
- `marker` - Show location marker
- `height` - Map container height
- `showDirections` / `showSatellite` - Control buttons
- `contactInfo` - Show contact sidebar

### 11. Accordion (`components/accordion.blade.php`)

**Features:**
- FAQ-style expandable sections
- Single or multiple open items
- Smooth animations
- Action links within content
- Accessibility with ARIA attributes
- Keyboard navigation

**Props:**
- `items` - Array of accordion items
- `title` / `description` - Section headers
- `variant` - 'default', 'minimal', 'bordered'
- `allowMultiple` - Multiple items open
- `size` - 'small', 'default', 'large'

### 12. Toast Notifications (`components/toast.blade.php`)

**Features:**
- Success, error, warning, info types
- Auto-dismiss with progress bar
- Manual close option
- Positioning options
- Global JavaScript function
- Accessibility announcements

**Props:**
- `type` - 'success', 'error', 'warning', 'info'
- `title` / `message` - Notification content
- `duration` - Auto-dismiss time (ms)
- `closable` - Show close button
- `position` - Toast positioning

**JavaScript Usage:**
```javascript
showToast('success', 'Booking confirmed!', 'Success', 5000);
showToast('error', 'Please check your information');
```

## 📱 Responsive Design

### Breakpoints
```css
sm:  640px  - Mobile landscape
md:  768px  - Tablet portrait
lg:  1024px - Tablet landscape / Desktop
xl:  1280px - Large desktop
2xl: 1400px - Extra large screens
```

### Container Behavior
- **Mobile**: Full-width with 1rem padding
- **Tablet**: Centered with 2rem padding
- **Desktop**: Centered with 4-6rem padding
- **Max-width**: 1400px (2xl breakpoint)

### Typography Scaling
- Fluid typography automatically scales between breakpoints
- Mobile-first approach with progressive enhancement
- Reduced font sizes on small screens for readability

## ♿ Accessibility Features

### WCAG 2.2 AA Compliance
- **Color Contrast**: All text meets 4.5:1 minimum ratio
- **Focus Management**: Visible focus indicators on all interactive elements
- **Keyboard Navigation**: Full keyboard support for all components
- **Screen Readers**: Proper ARIA labels and semantic HTML
- **Skip Links**: "Skip to main content" for keyboard users

### Implementation
- `aria-label` on navigation and controls
- `role` attributes for complex widgets
- `aria-expanded` for dropdowns and accordions
- `aria-live` regions for dynamic content
- Semantic HTML structure with proper headings

### Testing
```bash
# Install accessibility testing tools
npm install -D @axe-core/playwright
```

## 🚀 Performance Optimizations

### Images
- Lazy loading with `loading="lazy"`
- Responsive images with `srcset`
- WebP format with fallbacks
- Optimized file sizes

### CSS
- Tailwind CSS purging removes unused styles
- Critical CSS inlined for above-the-fold content
- Component-based architecture reduces bloat

### JavaScript
- Alpine.js for reactive components (lightweight)
- Minimal JavaScript footprint
- Progressive enhancement

## 📄 Example Pages

### Homepage (`resources/views/public/home.blade.php`)
**Demonstrates:**
- Hero section with background video/image
- Feature grid with icons
- Accommodation cards with pricing
- Activity cards in horizontal layout
- Image gallery with lightbox
- Testimonials with ratings
- CTA banners

### Contact Page (`resources/views/public/contact.blade.php`)
**Demonstrates:**
- Contact information cards
- Full-featured contact form
- Interactive map with sidebar
- FAQ accordion
- Response time information

## 🛠 Usage Guidelines

### Component Patterns
1. **Always use semantic HTML** - Headers, main, section, article
2. **Include alt text** for all images
3. **Use proper heading hierarchy** - h1 → h2 → h3
4. **Implement loading states** for async content
5. **Test keyboard navigation** on all interactive elements

### Responsive Images
```blade
<!-- Standard responsive image -->
<img 
    src="/images/photo.jpg" 
    alt="Descriptive text"
    class="w-full h-64 object-cover rounded-xl"
    loading="lazy"
>

<!-- With aspect ratio -->
<div class="aspect-landscape overflow-hidden rounded-xl">
    <img src="/images/photo.jpg" alt="Description" class="img-cover">
</div>
```

### Animation Guidelines
- Use `transform` over position changes
- Prefer `opacity` and `scale` for performance
- Respect `prefers-reduced-motion`
- Keep animations under 300ms for interactions

## 📱 Mobile-First Examples

### Responsive Grid
```blade
<!-- 1 column mobile, 2 tablet, 3 desktop -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
    <!-- Content -->
</div>
```

### Responsive Typography
```blade
<!-- Fluid text that scales smoothly -->
<h1 class="text-fluid-4xl font-display font-bold text-stone-900">
    Responsive Heading
</h1>
```

### Mobile Navigation
- Hamburger menu with smooth slide animations
- Full-screen overlay on mobile
- Touch-friendly button sizes (44px minimum)
- Swipe gestures for carousels

## ✅ Design System Checklist

### Phase 3 Deliverables - COMPLETE ✅

- [x] **Tailwind Configuration**
  - [x] Custom color palette (safari, forest, earth, stone)
  - [x] Fluid typography scale with clamp()
  - [x] Container widths and responsive breakpoints
  - [x] Custom shadows, spacing, and animations
  - [x] Additional plugins (typography, aspect-ratio)

- [x] **Base Layout System**
  - [x] Public layout with SEO optimization
  - [x] Accessibility features (skip links, ARIA)
  - [x] Font loading with preconnect
  - [x] Analytics integration ready
  - [x] Toast notification container

- [x] **Component Library (12 components)**
  - [x] Navbar (sticky, responsive, dropdowns)
  - [x] Footer (comprehensive links, newsletter)
  - [x] Hero (multiple variants, animations)
  - [x] Card (accommodations, activities, pricing)
  - [x] Feature List (grid/list layouts, icons)
  - [x] Testimonial (ratings, avatars, variants)
  - [x] Image Gallery (masonry, lightbox, lazy loading)
  - [x] CTA Banner (multiple variants, backgrounds)
  - [x] Contact Form (AJAX, validation, accessibility)
  - [x] Map Section (interactive, directions, info)
  - [x] Accordion (FAQs, smooth animations)
  - [x] Toast (notifications, auto-dismiss, global function)

- [x] **Accessibility Implementation**
  - [x] WCAG 2.2 AA contrast ratios
  - [x] Focus states and keyboard navigation
  - [x] Skip links and proper heading hierarchy
  - [x] ARIA labels and semantic HTML
  - [x] Screen reader compatible

- [x] **Example Screens**
  - [x] Desktop homepage with all components
  - [x] Mobile-responsive contact page
  - [x] Component demonstrations
  - [x] Responsive behavior testing

- [x] **Performance & Optimization**
  - [x] Lazy loading images
  - [x] Compressed CSS/JS assets
  - [x] Progressive enhancement
  - [x] Critical CSS optimization

## 🎯 Next Steps

**Ready for Phase 4:** Database Models & Content Management
- Models for accommodations, activities, bookings, testimonials
- Admin interface for content management
- Image/media handling with Spatie Media Library
- Form processing and email notifications
