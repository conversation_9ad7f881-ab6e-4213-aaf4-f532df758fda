# Malombo Selous Forest Camp - Routing Architecture

## Overview
This document outlines the complete routing structure for the Malombo Selous Forest Camp website, including public marketing pages, admin CMS, and system pages.

## Public Routes (Marketing Website)

### Home
- `GET /` → `home` → `Public\HomeController@index`

### The Lodge (About + Experience)
- `GET /lodge` → `lodge.index` → `Public\LodgeController@index`
- `GET /lodge/about` → `lodge.about` → `Public\LodgeController@about`
- `GET /lodge/experience` → `lodge.experience` → `Public\LodgeController@experience`
- `GET /lodge/history` → `lodge.history` → `Public\LodgeController@history`

### Accommodation
- `GET /accommodation` → `accommodation.index` → `Public\AccommodationController@index`
- `GET /accommodation/{slug}` → `accommodation.show` → `Public\AccommodationController@show`

### Activities
- `GET /activities` → `activities.index` → `Public\ActivityController@index`
- `GET /activities/{slug}` → `activities.show` → `Public\ActivityController@show`

### Facilities & Amenities
- `GET /facilities` → `facilities.index` → `Public\FacilityController@index`
- `GET /facilities/{slug}` → `facilities.show` → `Public\FacilityController@show`

### Rates & Policies
- `GET /rates` → `rates.index` → `Public\RateController@index`
- `GET /rates/policies` → `rates.policies` → `Public\RateController@policies`
- `GET /rates/seasons` → `rates.seasons` → `Public\RateController@seasons`

### Gallery
- `GET /gallery` → `gallery.index` → `Public\GalleryController@index`
- `GET /gallery/{category}` → `gallery.category` → `Public\GalleryController@category`

### Location & Getting Here
- `GET /location` → `location.index` → `Public\LocationController@index`
- `GET /location/getting-here` → `location.getting-here` → `Public\LocationController@gettingHere`
- `GET /location/nearby` → `location.nearby` → `Public\LocationController@nearby`

### Contact & Reservations
- `GET /contact` → `contact.index` → `Public\ContactController@index`
- `POST /contact/enquiry` → `contact.enquiry.submit` → `Public\ContactController@submitEnquiry`
- `GET /contact/reservations` → `contact.reservations` → `Public\ContactController@reservations`
- `POST /contact/reservations` → `contact.reservations.submit` → `Public\ContactController@submitReservation`

### Blog/News
- `GET /blog` → `blog.index` → `Public\BlogController@index`
- `GET /blog/{slug}` → `blog.show` → `Public\BlogController@show`
- `GET /blog/category/{slug}` → `blog.category` → `Public\BlogController@category`
- `GET /blog/tag/{slug}` → `blog.tag` → `Public\BlogController@tag`

## System Routes

### Error Pages
- `GET /error/404` → `error.404` → `System\ErrorController@notFound`
- `GET /error/500` → `error.500` → `System\ErrorController@serverError`
- `GET /error/503` → `error.503` → `System\ErrorController@maintenance`

### Legal Pages
- `GET /legal/privacy` → `legal.privacy` → `System\LegalController@privacy`
- `GET /legal/cookies` → `legal.cookies` → `System\LegalController@cookies`
- `GET /legal/terms` → `legal.terms` → `System\LegalController@terms`
- `GET /legal/disclaimer` → `legal.disclaimer` → `System\LegalController@disclaimer`

## Authentication Routes
- Laravel Breeze authentication routes (`/login`, `/register`, `/password/reset`, etc.)
- `GET /profile` → `profile.edit` → `ProfileController@edit`
- `PATCH /profile` → `profile.update` → `ProfileController@update`
- `DELETE /profile` → `profile.destroy` → `ProfileController@destroy`

## Admin Routes (CMS)
All admin routes are prefixed with `/admin` and require authentication (`auth`, `verified` middleware).

### Dashboard
- `GET /admin` → `admin.dashboard` → `Admin\DashboardController@index`
- `GET /admin/dashboard` → `admin.dashboard.index` → `Admin\DashboardController@index`

### Pages Management
**Middleware:** `can:view content`
- `GET /admin/pages` → `admin.pages.index` → `Admin\PageController@index`
- `GET /admin/pages/create` → `admin.pages.create` → `Admin\PageController@create` (requires `can:create content`)
- `POST /admin/pages` → `admin.pages.store` → `Admin\PageController@store` (requires `can:create content`)
- `GET /admin/pages/{page}` → `admin.pages.show` → `Admin\PageController@show`
- `GET /admin/pages/{page}/edit` → `admin.pages.edit` → `Admin\PageController@edit` (requires `can:edit content`)
- `PATCH /admin/pages/{page}` → `admin.pages.update` → `Admin\PageController@update` (requires `can:edit content`)
- `DELETE /admin/pages/{page}` → `admin.pages.destroy` → `Admin\PageController@destroy` (requires `can:delete content`)
- `PATCH /admin/pages/{page}/publish` → `admin.pages.publish` → `Admin\PageController@publish` (requires `can:publish content`)

### Accommodations Management
**Middleware:** `can:view content`
- `GET /admin/accommodations` → `admin.accommodations.index` → `Admin\AccommodationController@index`
- `GET /admin/accommodations/create` → `admin.accommodations.create` → `Admin\AccommodationController@create` (requires `can:create content`)
- `POST /admin/accommodations` → `admin.accommodations.store` → `Admin\AccommodationController@store` (requires `can:create content`)
- `GET /admin/accommodations/{accommodation}` → `admin.accommodations.show` → `Admin\AccommodationController@show`
- `GET /admin/accommodations/{accommodation}/edit` → `admin.accommodations.edit` → `Admin\AccommodationController@edit` (requires `can:edit content`)
- `PATCH /admin/accommodations/{accommodation}` → `admin.accommodations.update` → `Admin\AccommodationController@update` (requires `can:edit content`)
- `DELETE /admin/accommodations/{accommodation}` → `admin.accommodations.destroy` → `Admin\AccommodationController@destroy` (requires `can:delete content`)

### Activities Management
**Middleware:** `can:view content`
- Similar structure to accommodations with full CRUD operations

### Facilities Management
**Middleware:** `can:view content`
- Similar structure to accommodations with full CRUD operations

### Gallery & Media Management
**Middleware:** `can:view media`
- `GET /admin/media` → `admin.media.index` → `Admin\MediaController@index`
- `GET /admin/media/gallery` → `admin.media.gallery` → `Admin\MediaController@gallery`
- `POST /admin/media/upload` → `admin.media.upload` → `Admin\MediaController@upload` (requires `can:upload media`)
- `GET /admin/media/{media}` → `admin.media.show` → `Admin\MediaController@show`
- `PATCH /admin/media/{media}` → `admin.media.update` → `Admin\MediaController@update` (requires `can:edit media`)
- `DELETE /admin/media/{media}` → `admin.media.destroy` → `Admin\MediaController@destroy` (requires `can:delete media`)
- `POST /admin/media/bulk-upload` → `admin.media.bulk-upload` → `Admin\MediaController@bulkUpload` (requires `can:upload media`)

### Rates & Seasons Management
**Middleware:** `can:view content`
- `GET /admin/rates` → `admin.rates.index` → `Admin\RateController@index`
- `GET /admin/rates/seasons` → `admin.rates.seasons` → `Admin\RateController@seasons`
- `POST /admin/rates/seasons` → `admin.rates.seasons.store` → `Admin\RateController@storeSeason` (requires `can:edit content`)
- `PATCH /admin/rates/seasons/{season}` → `admin.rates.seasons.update` → `Admin\RateController@updateSeason` (requires `can:edit content`)
- `DELETE /admin/rates/seasons/{season}` → `admin.rates.seasons.destroy` → `Admin\RateController@destroySeason` (requires `can:delete content`)

### Policies Management
**Middleware:** `can:view content`
- `GET /admin/policies` → `admin.policies.index` → `Admin\PolicyController@index`
- `GET /admin/policies/{policy}/edit` → `admin.policies.edit` → `Admin\PolicyController@edit` (requires `can:edit content`)
- `PATCH /admin/policies/{policy}` → `admin.policies.update` → `Admin\PolicyController@update` (requires `can:edit content`)

### FAQs Management
**Middleware:** `can:view content`
- `GET /admin/faqs` → `admin.faqs.index` → `Admin\FaqController@index`
- `GET /admin/faqs/create` → `admin.faqs.create` → `Admin\FaqController@create` (requires `can:create content`)
- `POST /admin/faqs` → `admin.faqs.store` → `Admin\FaqController@store` (requires `can:create content`)
- `GET /admin/faqs/{faq}/edit` → `admin.faqs.edit` → `Admin\FaqController@edit` (requires `can:edit content`)
- `PATCH /admin/faqs/{faq}` → `admin.faqs.update` → `Admin\FaqController@update` (requires `can:edit content`)
- `DELETE /admin/faqs/{faq}` → `admin.faqs.destroy` → `Admin\FaqController@destroy` (requires `can:delete content`)

### Blog Management
**Middleware:** `can:view content`
- `GET /admin/blog` → `admin.blog.index` → `Admin\BlogController@index`
- `GET /admin/blog/create` → `admin.blog.create` → `Admin\BlogController@create` (requires `can:create content`)
- `POST /admin/blog` → `admin.blog.store` → `Admin\BlogController@store` (requires `can:create content`)
- `GET /admin/blog/{post}` → `admin.blog.show` → `Admin\BlogController@show`
- `GET /admin/blog/{post}/edit` → `admin.blog.edit` → `Admin\BlogController@edit` (requires `can:edit content`)
- `PATCH /admin/blog/{post}` → `admin.blog.update` → `Admin\BlogController@update` (requires `can:edit content`)
- `DELETE /admin/blog/{post}` → `admin.blog.destroy` → `Admin\BlogController@destroy` (requires `can:delete content`)
- `PATCH /admin/blog/{post}/publish` → `admin.blog.publish` → `Admin\BlogController@publish` (requires `can:publish content`)

### Enquiries (Inbox)
**Middleware:** `can:view bookings`
- `GET /admin/enquiries` → `admin.enquiries.index` → `Admin\EnquiryController@index`
- `GET /admin/enquiries/{enquiry}` → `admin.enquiries.show` → `Admin\EnquiryController@show`
- `PATCH /admin/enquiries/{enquiry}/status` → `admin.enquiries.status` → `Admin\EnquiryController@updateStatus` (requires `can:edit bookings`)
- `POST /admin/enquiries/{enquiry}/reply` → `admin.enquiries.reply` → `Admin\EnquiryController@reply` (requires `can:create bookings`)
- `DELETE /admin/enquiries/{enquiry}` → `admin.enquiries.destroy` → `Admin\EnquiryController@destroy` (requires `can:delete bookings`)
- `GET /admin/enquiries/export/csv` → `admin.enquiries.export.csv` → `Admin\EnquiryController@exportCsv` (requires `can:export bookings`)

### Settings (contacts, social, metadata)
**Middleware:** `can:view settings`
- `GET /admin/settings` → `admin.settings.index` → `Admin\SettingController@index`
- `GET /admin/settings/contacts` → `admin.settings.contacts` → `Admin\SettingController@contacts`
- `PATCH /admin/settings/contacts` → `admin.settings.contacts.update` → `Admin\SettingController@updateContacts` (requires `can:edit settings`)
- `GET /admin/settings/social` → `admin.settings.social` → `Admin\SettingController@social`
- `PATCH /admin/settings/social` → `admin.settings.social.update` → `Admin\SettingController@updateSocial` (requires `can:edit settings`)
- `GET /admin/settings/seo` → `admin.settings.seo` → `Admin\SettingController@seo`
- `PATCH /admin/settings/seo` → `admin.settings.seo.update` → `Admin\SettingController@updateSeo` (requires `can:edit settings`)
- `GET /admin/settings/general` → `admin.settings.general` → `Admin\SettingController@general`
- `PATCH /admin/settings/general` → `admin.settings.general.update` → `Admin\SettingController@updateGeneral` (requires `can:edit settings`)

## Route Patterns
- `slug`: `[a-z0-9]+(?:-[a-z0-9]+)*` (lowercase alphanumeric with hyphens)
- `id`: `[0-9]+` (numeric IDs)
- `uuid`: `[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}` (UUID format)

## Permission System
The admin routes use Laravel Permissions with the following roles:
- **Admin**: Full access to all features
- **Editor**: Content and booking management
- **Viewer**: Read-only access

## Navigation Service
The `NavigationService` class provides methods to:
- `getMainNavigation()`: Get public website navigation
- `getAdminNavigation()`: Get admin CMS navigation (filtered by permissions)
- `getFooterNavigation()`: Get footer navigation
- `getBreadcrumbs()`: Get breadcrumb navigation for current route
- `getRouteTitle()`: Get human-readable title for any route

## Configuration Files
- `config/sitemap.php`: Site structure and navigation configuration
- `config/malombo.php`: Camp-specific settings and content
- `routes/web.php`: All route definitions

This routing structure provides a clear separation between public marketing content, admin functionality, and system pages, with proper permission controls and SEO-friendly URLs.
