<?php

namespace App\Rules;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class SecureFileUpload implements ValidationRule
{
    protected array $allowedMimes;
    protected int $maxSize;
    protected array $dangerousExtensions;

    public function __construct(array $allowedMimes = null, int $maxSize = null)
    {
        $this->allowedMimes = $allowedMimes ?? ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
        $this->maxSize = $maxSize ?? 5120; // 5MB in KB
        $this->dangerousExtensions = [
            'php', 'php3', 'php4', 'php5', 'phtml', 'exe', 'bat', 'cmd', 'com', 'scr',
            'vbs', 'js', 'jar', 'asp', 'aspx', 'jsp', 'py', 'pl', 'rb', 'sh'
        ];
    }

    /**
     * Run the validation rule.
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        if (! $value instanceof \Illuminate\Http\UploadedFile) {
            $fail('The :attribute must be a valid file.');
            return;
        }

        // Check file size
        if ($value->getSize() > $this->maxSize * 1024) {
            $fail('The :attribute may not be greater than ' . $this->maxSize . 'KB.');
            return;
        }

        // Check MIME type
        if (! in_array($value->getMimeType(), $this->allowedMimes)) {
            $fail('The :attribute must be a file of type: ' . implode(', ', $this->allowedMimes) . '.');
            return;
        }

        // Check file extension
        $extension = strtolower($value->getClientOriginalExtension());
        if (in_array($extension, $this->dangerousExtensions)) {
            $fail('The :attribute has a dangerous file extension.');
            return;
        }

        // Check for double extensions (file.jpg.php)
        $filename = $value->getClientOriginalName();
        $extensionCount = substr_count($filename, '.');
        if ($extensionCount > 1) {
            $fail('The :attribute filename contains multiple extensions.');
            return;
        }

        // Validate image files
        if (strpos($value->getMimeType(), 'image/') === 0) {
            $imageInfo = @getimagesize($value->getPathname());
            if ($imageInfo === false) {
                $fail('The :attribute is not a valid image file.');
                return;
            }

            // Check for reasonable image dimensions
            [$width, $height] = $imageInfo;
            if ($width > 4000 || $height > 4000) {
                $fail('The :attribute dimensions are too large (max 4000x4000).');
                return;
            }

            if ($width < 10 || $height < 10) {
                $fail('The :attribute dimensions are too small (min 10x10).');
                return;
            }
        }

        // Check file content for embedded scripts
        $content = file_get_contents($value->getPathname());
        $maliciousPatterns = [
            '/<\?php/i',
            '/<script/i',
            '/<%/i',
            '/javascript:/i',
            '/vbscript:/i'
        ];

        foreach ($maliciousPatterns as $pattern) {
            if (preg_match($pattern, $content)) {
                $fail('The :attribute contains potentially malicious content.');
                return;
            }
        }
    }
}
