<?php

namespace App\Rules;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class NoMaliciousContent implements ValidationRule
{
    /**
     * Run the validation rule.
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        if (! is_string($value)) {
            return;
        }

        $maliciousPatterns = [
            // XSS patterns
            '/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/mi',
            '/javascript:/i',
            '/vbscript:/i',
            '/on\w+\s*=/i',
            
            // SQL injection patterns
            '/(\s|^)(union|select|insert|update|delete|drop|create|alter|exec|execute)\s/i',
            '/(\s|^)(or|and)\s+\d+\s*=\s*\d+/i',
            '/(\s|^)1\s*=\s*1/i',
            '/(\s|^)1\s*;\s*(drop|delete|truncate)/i',
            
            // Directory traversal
            '/\.\.\//',
            '/\.\.\\\\/',
            
            // Command injection
            '/(\s|^)(`|;|\||&)/i',
            '/(\s|^)(cat|ls|dir|type|copy|del|rm|mv|cp)\s/i',
        ];

        foreach ($maliciousPatterns as $pattern) {
            if (preg_match($pattern, $value)) {
                $fail('The :attribute contains potentially malicious content.');
                return;
            }
        }

        // Check for excessive special characters (possible encoding attack)
        $specialCharCount = preg_match_all('/[<>"\'\(\);{}]/', $value);
        if ($specialCharCount > strlen($value) * 0.1) { // More than 10% special chars
            $fail('The :attribute contains too many special characters.');
        }
    }
}
