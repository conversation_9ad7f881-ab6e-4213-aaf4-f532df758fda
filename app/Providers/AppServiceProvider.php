<?php

declare(strict_types=1);

namespace App\Providers;

use App\Services\NavigationService;
use App\Services\SEOService;
use App\Services\CacheService;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\ServiceProvider;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        // Register core services
        $this->app->singleton(NavigationService::class);
        $this->app->singleton(SEOService::class);
        $this->app->singleton(CacheService::class);
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        // Set up route patterns
        $this->configureRoutePatterns();
    }

    /**
     * Configure route parameter patterns.
     */
    protected function configureRoutePatterns(): void
    {
        $patterns = config('sitemap.route_patterns', []);

        foreach ($patterns as $key => $pattern) {
            Route::pattern($key, $pattern);
        }
    }
}
