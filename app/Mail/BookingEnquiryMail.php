<?php

namespace App\Mail;

use App\Models\Enquiry;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class BookingEnquiryMail extends Mailable
{
    use Queueable, SerializesModels;

    public function __construct(
        public Enquiry $enquiry,
        public array $formData
    ) {}

    public function envelope(): Envelope
    {
        return new Envelope(
            subject: 'New Booking Enquiry - ' . $this->enquiry->name,
            replyTo: [$this->enquiry->email => $this->enquiry->name]
        );
    }

    public function content(): Content
    {
        return new Content(
            markdown: 'emails.booking-enquiry',
            with: [
                'enquiry' => $this->enquiry,
                'formData' => $this->formData,
                'nights' => $this->enquiry->check_out 
                    ? \Carbon\Carbon::parse($this->enquiry->check_out)->diffInDays(\Carbon\Carbon::parse($this->enquiry->check_in))
                    : null,
                'activityNames' => !empty($this->formData['activity_interests']) 
                    ? \App\Models\Activity::whereIn('id', $this->formData['activity_interests'])->pluck('title')->join(', ')
                    : null,
            ]
        );
    }
}
