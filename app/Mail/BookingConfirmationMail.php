<?php

namespace App\Mail;

use App\Models\Enquiry;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class BookingConfirmationMail extends Mailable
{
    use Queueable, SerializesModels;

    public function __construct(
        public Enquiry $enquiry,
        public array $formData
    ) {}

    public function envelope(): Envelope
    {
        return new Envelope(
            subject: 'Your Safari Booking Enquiry Received - Malombo Private Game Reserve'
        );
    }

    public function content(): Content
    {
        return new Content(
            markdown: 'emails.booking-confirmation',
            with: [
                'enquiry' => $this->enquiry,
                'formData' => $this->formData,
                'nights' => $this->enquiry->check_out 
                    ? \Carbon\Carbon::parse($this->enquiry->check_out)->diffInDays(\Carbon\Carbon::parse($this->enquiry->check_in))
                    : null,
                'activityNames' => !empty($this->formData['activity_interests']) 
                    ? \App\Models\Activity::whereIn('id', $this->formData['activity_interests'])->pluck('title')->join(', ')
                    : null,
                'whatsappNumber' => setting('whatsapp_number', '+27123456789'),
                'whatsappMessage' => urlencode("Hi! I've just submitted a booking enquiry for {$this->enquiry->name}. Reference: ENQ-{$this->enquiry->id}"),
            ]
        );
    }
}
