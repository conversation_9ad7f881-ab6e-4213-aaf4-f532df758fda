<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Cache;
use App\Services\CacheService;
use App\Services\SEOService;

class OptimizePerformance extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'performance:optimize 
                            {--clear-cache : Clear all caches before optimization}
                            {--warm-cache : Warm up critical caches}
                            {--generate-sitemap : Generate fresh sitemap}
                            {--validate-images : Validate image optimization}';

    /**
     * The console command description.
     */
    protected $description = 'Optimize application performance for production';

    protected $cacheService;
    protected $seoService;

    public function __construct(CacheService $cacheService, SEOService $seoService)
    {
        parent::__construct();
        $this->cacheService = $cacheService;
        $this->seoService = $seoService;
    }

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $this->info('🚀 Starting performance optimization...');

        if ($this->option('clear-cache')) {
            $this->clearCaches();
        }

        if ($this->option('warm-cache')) {
            $this->warmCaches();
        }

        if ($this->option('generate-sitemap')) {
            $this->generateSitemap();
        }

        if ($this->option('validate-images')) {
            $this->validateImages();
        }

        $this->optimizeConfiguration();
        $this->runGeneralOptimizations();

        $this->info('✅ Performance optimization completed!');
        $this->displayOptimizationSummary();

        return Command::SUCCESS;
    }

    /**
     * Clear all application caches
     */
    protected function clearCaches(): void
    {
        $this->info('🧹 Clearing caches...');

        // Clear application cache
        $this->call('cache:clear');
        
        // Clear configuration cache
        $this->call('config:clear');
        
        // Clear route cache
        $this->call('route:clear');
        
        // Clear view cache
        $this->call('view:clear');

        $this->line('   ✓ All caches cleared');
    }

    /**
     * Warm up critical caches
     */
    protected function warmCaches(): void
    {
        $this->info('🔥 Warming up caches...');

        // Warm up configuration cache
        $this->call('config:cache');
        
        // Warm up route cache
        $this->call('route:cache');

        // Warm up application-specific caches
        $this->cacheService->warmUpAccommodationCache();
        $this->cacheService->warmUpActivityCache();
        $this->cacheService->warmUpFacilityCache();

        $this->line('   ✓ Critical caches warmed up');
    }

    /**
     * Generate fresh sitemap
     */
    protected function generateSitemap(): void
    {
        $this->info('🗺️  Generating sitemap...');

        try {
            $sitemap = $this->seoService->generateSitemap();
            file_put_contents(public_path('sitemap.xml'), $sitemap);
            $this->line('   ✓ Sitemap generated successfully');
        } catch (\Exception $e) {
            $this->error('   ✗ Sitemap generation failed: ' . $e->getMessage());
        }
    }

    /**
     * Validate image optimization
     */
    protected function validateImages(): void
    {
        $this->info('🖼️  Validating image optimization...');

        $imageTypes = ['jpg', 'jpeg', 'png', 'webp', 'avif'];
        $totalImages = 0;
        $optimizedImages = 0;

        foreach ($imageTypes as $type) {
            $images = glob(public_path("storage/**/*.{$type}"));
            $totalImages += count($images);
            
            foreach ($images as $image) {
                $size = filesize($image);
                if ($size < 500000) { // Less than 500KB considered optimized
                    $optimizedImages++;
                }
            }
        }

        $optimizationRatio = $totalImages > 0 ? ($optimizedImages / $totalImages) * 100 : 100;
        
        if ($optimizationRatio >= 80) {
            $this->line("   ✓ Images well optimized ({$optimizationRatio}%)");
        } else {
            $this->warn("   ⚠ Consider optimizing images ({$optimizationRatio}% optimized)");
        }
    }

    /**
     * Optimize application configuration
     */
    protected function optimizeConfiguration(): void
    {
        $this->info('⚙️  Optimizing configuration...');

        // Check if OPcache is enabled
        if (function_exists('opcache_get_status')) {
            $opcacheStatus = opcache_get_status();
            if ($opcacheStatus && $opcacheStatus['opcache_enabled']) {
                $this->line('   ✓ OPcache is enabled');
            } else {
                $this->warn('   ⚠ Consider enabling OPcache for better performance');
            }
        } else {
            $this->warn('   ⚠ OPcache not available');
        }

        // Check Redis/Memcached
        $cacheDriver = config('cache.default');
        if (in_array($cacheDriver, ['redis', 'memcached'])) {
            $this->line("   ✓ Using {$cacheDriver} for caching");
        } else {
            $this->warn('   ⚠ Consider using Redis or Memcached for better cache performance');
        }

        $this->line('   ✓ Configuration optimized');
    }

    /**
     * Run general optimizations
     */
    protected function runGeneralOptimizations(): void
    {
        $this->info('🔧 Running general optimizations...');

        // Optimize Composer autoloader
        $this->call('optimize');
        
        // Clear compiled views and recompile
        $this->call('view:cache');

        $this->line('   ✓ General optimizations completed');
    }

    /**
     * Display optimization summary
     */
    protected function displayOptimizationSummary(): void
    {
        $this->info('📊 Optimization Summary:');
        
        $recommendations = [
            '1. Enable GZIP compression in web server',
            '2. Set up CDN for static assets',
            '3. Monitor Core Web Vitals with Lighthouse CI',
            '4. Enable browser caching headers',
            '5. Optimize database queries with indexes',
            '6. Consider lazy loading for images',
            '7. Minify CSS/JS in production',
            '8. Use WebP/AVIF image formats',
        ];

        foreach ($recommendations as $recommendation) {
            $this->line("   • {$recommendation}");
        }

        $this->info('🎯 Target Lighthouse Scores: Performance 90+, Accessibility 90+, Best Practices 90+, SEO 90+');
    }
}
