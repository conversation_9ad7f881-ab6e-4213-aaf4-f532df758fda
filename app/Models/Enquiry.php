<?php

declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;

class Enquiry extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'name',
        'email',
        'phone',
        'adults',
        'children',
        'check_in',
        'check_out',
        'accommodation_type',
        'message',
        'source',
        'status',
        'internal_notes',
        'contacted_at',
        'assigned_to',
    ];

    /**
     * The attributes that should be cast.
     */
    protected $casts = [
        'check_in' => 'date',
        'check_out' => 'date',
        'contacted_at' => 'datetime',
    ];

    /**
     * Enquiry status enum values.
     */
    public const STATUSES = [
        'new' => 'New',
        'contacted' => 'Contacted',
        'quoted' => 'Quoted',
        'booked' => 'Booked',
        'cancelled' => 'Cancelled',
        'closed' => 'Closed',
    ];

    /**
     * Source enum values.
     */
    public const SOURCES = [
        'website' => 'Website',
        'phone' => 'Phone',
        'email' => 'Email',
        'referral' => 'Referral',
        'social_media' => 'Social Media',
        'walk_in' => 'Walk-in',
    ];

    /**
     * Get the user assigned to this enquiry.
     */
    public function assignedUser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'assigned_to');
    }

    /**
     * Scope to filter by status.
     */
    public function scopeWithStatus(Builder $query, string $status): Builder
    {
        return $query->where('status', $status);
    }

    /**
     * Scope to get new enquiries.
     */
    public function scopeNew(Builder $query): Builder
    {
        return $query->where('status', 'new');
    }

    /**
     * Scope to get recent enquiries.
     */
    public function scopeRecent(Builder $query, int $days = 30): Builder
    {
        return $query->where('created_at', '>=', now()->subDays($days));
    }

    /**
     * Scope to order by priority (new first, then by created_at desc).
     */
    public function scopeByPriority(Builder $query): Builder
    {
        return $query->orderByRaw("CASE WHEN status = 'new' THEN 0 ELSE 1 END")
                    ->orderBy('created_at', 'desc');
    }

    /**
     * Get the display status.
     */
    public function getDisplayStatusAttribute(): string
    {
        return self::STATUSES[$this->status] ?? $this->status;
    }

    /**
     * Get the display source.
     */
    public function getDisplaySourceAttribute(): string
    {
        return self::SOURCES[$this->source] ?? $this->source;
    }

    /**
     * Get total guests.
     */
    public function getTotalGuestsAttribute(): int
    {
        return $this->adults + $this->children;
    }

    /**
     * Get stay duration in nights.
     */
    public function getStayDurationAttribute(): ?int
    {
        if (!$this->check_in || !$this->check_out) {
            return null;
        }
        
        return $this->check_in->diffInDays($this->check_out);
    }

    /**
     * Mark enquiry as contacted.
     */
    public function markAsContacted(?User $user = null): void
    {
        $this->update([
            'status' => 'contacted',
            'contacted_at' => now(),
            'assigned_to' => $user?->id ?? auth()->id(),
        ]);
    }
}
