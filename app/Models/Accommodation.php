<?php

declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Spa<PERSON>\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\MediaLibrary\MediaCollections\Models\Media;
use Carbon\Carbon;

class Accommodation extends Model implements HasMedia
{
    use HasFactory, InteractsWithMedia;

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'title',
        'slug',
        'type',
        'short_intro',
        'description',
        'features',
        'wheelchair_access',
        'has_balcony',
        'has_heated_shower',
        'has_mini_bar',
        'max_occupancy',
        'priority',
        'sort_order',
        'is_active',
        'is_featured',
        'published_at',
        'meta_title',
        'meta_description',
        'meta_keywords',
        'og_image',
    ];

    /**
     * The attributes that should be cast.
     */
    protected $casts = [
        'features' => 'array',
        'wheelchair_access' => 'boolean',
        'has_balcony' => 'boolean',
        'has_heated_shower' => 'boolean',
        'has_mini_bar' => 'boolean',
        'is_active' => 'boolean',
        'is_featured' => 'boolean',
        'published_at' => 'datetime',
    ];

    /**
     * Accommodation types enum values.
     */
    public const TYPES = [
        'banda' => 'Banda',
        'room' => 'Room',
        'tent' => 'Tent',
        'tree_house' => 'Tree House',
        'inter_leading' => 'Inter-leading Room',
        'campsite' => 'Campsite',
    ];

    /**
     * Scope to get only published accommodations.
     */
    public function scopePublished(Builder $query): Builder
    {
        return $query->whereNotNull('published_at')
                    ->where('published_at', '<=', now());
    }

    /**
     * Scope to get only active accommodations.
     */
    public function scopeActive(Builder $query): Builder
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to get featured accommodations.
     */
    public function scopeFeatured(Builder $query): Builder
    {
        return $query->where('is_featured', true);
    }

    /**
     * Scope to order by sort order.
     */
    public function scopeOrdered(Builder $query): Builder
    {
        return $query->orderBy('sort_order')
                    ->orderBy('name');
    }

    /**
     * Scope to filter by type.
     */
    public function scopeOfType(Builder $query, string $type): Builder
    {
        return $query->where('type', $type);
    }

    /**
     * Scope to order by priority and sort order.
     */
    public function scopeByPriority(Builder $query): Builder
    {
        return $query->orderBy('sort_order', 'asc')
                    ->orderBy('priority', 'desc')
                    ->orderBy('title', 'asc');
    }

    /**
     * Get the accommodation's display type.
     */
    protected function displayType(): Attribute
    {
        return Attribute::make(
            get: fn (mixed $value, array $attributes) => self::TYPES[$attributes['type']] ?? $attributes['type']
        );
    }

    /**
     * Check if accommodation is published.
     */
    public function isPublished(): bool
    {
        return $this->published_at !== null && $this->published_at->lte(now());
    }

    /**
     * Register media collections.
     */
    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('gallery')
              ->acceptsMimeTypes(['image/jpeg', 'image/png', 'image/webp']);

        $this->addMediaCollection('featured_image')
              ->singleFile()
              ->acceptsMimeTypes(['image/jpeg', 'image/png', 'image/webp']);
    }

    /**
     * Register media conversions.
     */
    public function registerMediaConversions(?Media $media = null): void
    {
        $this->addMediaConversion('thumb')
              ->width(300)
              ->height(200)
              ->sharpen(10)
              ->nonQueued();

        $this->addMediaConversion('large')
              ->width(1200)
              ->height(800)
              ->sharpen(10)
              ->nonQueued();
    }
}
