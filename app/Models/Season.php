<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Season extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'description',
        'start_month',
        'end_month',
        'peak_season',
        'weather_description',
        'wildlife_highlights',
        'pricing_multiplier',
        'is_active',
        'sort_order'
    ];

    protected $casts = [
        'peak_season' => 'boolean',
        'is_active' => 'boolean',
        'pricing_multiplier' => 'decimal:2',
        'wildlife_highlights' => 'array'
    ];

    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order', 'asc')->orderBy('start_month', 'asc');
    }

    public function getMonthRangeAttribute()
    {
        $months = [
            1 => 'January', 2 => 'February', 3 => 'March', 4 => 'April',
            5 => 'May', 6 => 'June', 7 => 'July', 8 => 'August',
            9 => 'September', 10 => 'October', 11 => 'November', 12 => 'December'
        ];

        if ($this->start_month === $this->end_month) {
            return $months[$this->start_month];
        }

        return $months[$this->start_month] . ' - ' . $months[$this->end_month];
    }

    public function isCurrentSeason()
    {
        $currentMonth = now()->month;
        
        if ($this->start_month <= $this->end_month) {
            return $currentMonth >= $this->start_month && $currentMonth <= $this->end_month;
        } else {
            // Handle seasons that span across year (e.g., Dec-Feb)
            return $currentMonth >= $this->start_month || $currentMonth <= $this->end_month;
        }
    }
}
