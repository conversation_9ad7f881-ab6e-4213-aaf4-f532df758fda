<?php

declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Spa<PERSON>\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

class Activity extends Model implements HasMedia
{
    use HasFactory, InteractsWithMedia;

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'title',
        'slug',
        'category',
        'description',
        'tips',
        'duration',
        'includes',
        'is_active',
        'is_featured',
        'sort_order',
        'published_at',
        'meta_title',
        'meta_description',
        'meta_keywords',
        'og_image',
    ];

    /**
     * The attributes that should be cast.
     */
    protected $casts = [
        'includes' => 'array',
        'is_active' => 'boolean',
        'is_featured' => 'boolean',
        'published_at' => 'datetime',
    ];

    /**
     * Activity categories enum values.
     */
    public const CATEGORIES = [
        'game_drive' => 'Game Drive',
        'walk_safari' => 'Walking Safari',
        'boat_safari' => 'Boat Safari',
        'village_tour' => 'Village Tour',
        'cultural_tour' => 'Cultural Tour',
        'fishing' => 'Fishing',
        'night_viewing' => 'Night Viewing',
        'social_drive' => 'Social Drive',
    ];

    /**
     * Scope to filter active activities.
     */
    public function scopeActive(Builder $query): Builder
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to filter featured activities.
     */
    public function scopeFeatured(Builder $query): Builder
    {
        return $query->where('is_featured', true);
    }

    /**
     * Scope to order by sort order.
     */
    public function scopeOrdered(Builder $query): Builder
    {
        return $query->orderBy('sort_order')
                    ->orderBy('title');
    }

    /**
     * Scope to get only published activities.
     */
    public function scopePublished(Builder $query): Builder
    {
        return $query->whereNotNull('published_at')
                    ->where('published_at', '<=', now());
    }

    /**
     * Scope to filter by category.
     */
    public function scopeInCategory(Builder $query, string $category): Builder
    {
        return $query->where('category', $category);
    }

    /**
     * Get the activity's display category.
     */
    protected function displayCategory(): Attribute
    {
        return Attribute::make(
            get: fn (mixed $value, array $attributes) => self::CATEGORIES[$attributes['category']] ?? $attributes['category']
        );
    }

    /**
     * Check if activity is published.
     */
    public function isPublished(): bool
    {
        return $this->published_at !== null && $this->published_at->lte(now());
    }

    /**
     * Register media collections.
     */
    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('gallery')
              ->acceptsMimeTypes(['image/jpeg', 'image/png', 'image/webp']);

        $this->addMediaCollection('featured_image')
              ->singleFile()
              ->acceptsMimeTypes(['image/jpeg', 'image/png', 'image/webp']);
    }

    /**
     * Register media conversions.
     */
    public function registerMediaConversions(?Media $media = null): void
    {
        $this->addMediaConversion('thumb')
              ->width(300)
              ->height(200)
              ->sharpen(10)
              ->nonQueued();

        $this->addMediaConversion('large')
              ->width(1200)
              ->height(800)
              ->sharpen(10)
              ->nonQueued();
    }
}
