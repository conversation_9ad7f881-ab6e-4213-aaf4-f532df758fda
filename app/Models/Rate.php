<?php

declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Builder;
use Carbon\Carbon;

class Rate extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'season',
        'from_date',
        'to_date',
        'per_person_min',
        'per_person_max',
        'notes',
        'is_active',
    ];

    /**
     * The attributes that should be cast.
     */
    protected $casts = [
        'from_date' => 'date',
        'to_date' => 'date',
        'per_person_min' => 'decimal:2',
        'per_person_max' => 'decimal:2',
        'is_active' => 'boolean',
    ];

    /**
     * Scope to get only active rates.
     */
    public function scopeActive(Builder $query): Builder
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to get current rates.
     */
    public function scopeCurrent(Builder $query, ?Carbon $date = null): Builder
    {
        $date = $date ?? now();
        
        return $query->where('from_date', '<=', $date)
                    ->where('to_date', '>=', $date);
    }

    /**
     * Scope to order by date.
     */
    public function scopeByDate(Builder $query): Builder
    {
        return $query->orderBy('from_date', 'asc');
    }

    /**
     * Get formatted price range.
     */
    public function getPriceRangeAttribute(): string
    {
        if ($this->per_person_min == $this->per_person_max) {
            return '$' . number_format($this->per_person_min, 0);
        }
        
        return '$' . number_format($this->per_person_min, 0) . ' - $' . number_format($this->per_person_max, 0);
    }
}
