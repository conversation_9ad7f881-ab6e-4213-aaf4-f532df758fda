<?php

declare(strict_types=1);

namespace App\Livewire;

use App\Models\Accommodation;
use App\Models\Activity;
use App\Models\Enquiry;
use App\Mail\BookingEnquiryMail;
use App\Mail\BookingConfirmationMail;
use App\Http\Requests\BookingEnquiryRequest;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Support\Facades\Validator;
use Livewire\Component;
use Carbon\Carbon;

class BookingEnquiry extends Component
{
    // Multi-step form state
    public int $currentStep = 1;
    public int $totalSteps = 5;

    // Step 1: Guest Information
    public string $name = '';
    public string $email = '';
    public string $phone = '';
    public string $country = '';

    // Step 2: Travel Dates
    public string $arrival_date = '';
    public string $departure_date = '';

    // Step 3: Guest Details
    public int $adults = 1;
    public int $children = 0;
    public string $special_requests = '';
    public string $dietary_requirements = '';

    // Step 4: Preferences
    public string $accommodation_preference = '';
    public array $activity_interests = [];
    public string $budget_range = '';

    // Step 5: Additional Information
    public string $message = '';
    public string $how_did_you_hear = '';
    public bool $marketing_consent = false;
    public bool $privacy_accepted = false;
    
    // Honeypot field (should remain empty)
    public string $website = '';

    // Available options
    public $accommodations;
    public $activities;
    
    public function mount()
    {
        $this->accommodations = Accommodation::active()->ordered()->get();
        $this->activities = Activity::active()->ordered()->get();
        
        // Set default arrival date to tomorrow
        $this->arrival_date = Carbon::tomorrow()->format('Y-m-d');
        $this->departure_date = Carbon::tomorrow()->addDays(3)->format('Y-m-d');
    }

    public function nextStep()
    {
        $this->validateCurrentStep();
        
        if ($this->currentStep < $this->totalSteps) {
            $this->currentStep++;
        }
    }

    public function previousStep()
    {
        if ($this->currentStep > 1) {
            $this->currentStep--;
        }
    }

    public function goToStep($step)
    {
        if ($step >= 1 && $step <= $this->totalSteps && $step <= $this->currentStep + 1) {
            $this->currentStep = $step;
        }
    }

    private function validateCurrentStep()
    {
        $request = new BookingEnquiryRequest();
        $rules = $request->rules();
        
        switch ($this->currentStep) {
            case 1:
                $this->validate([
                    'name' => $rules['name'],
                    'email' => $rules['email'],
                    'phone' => $rules['phone'],
                    'country' => $rules['country'] ?? 'nullable|string|max:100',
                ]);
                break;
            case 2:
                $this->validate([
                    'arrival_date' => $rules['arrival_date'],
                    'departure_date' => $rules['departure_date'],
                ]);
                break;
            case 3:
                $this->validate([
                    'adults' => $rules['adults'],
                    'children' => $rules['children'],
                    'special_requests' => $rules['special_requests'] ?? 'nullable|string|max:500',
                    'dietary_requirements' => $rules['dietary_requirements'] ?? 'nullable|string|max:500',
                ]);
                break;
            case 4:
                $this->validate([
                    'accommodation_preference' => 'nullable|string',
                    'activity_interests' => 'nullable|array',
                    'budget_range' => 'nullable|string|in:budget,mid-range,luxury,ultra-luxury',
                ]);
                break;
            case 5:
                $this->validate([
                    'privacy_accepted' => $rules['privacy_accepted'],
                    'website' => $rules['website'],
                ]);
                break;
        }
    }

    public function submit()
    {
        // Rate limiting
        $key = 'booking-enquiry:' . request()->ip();
        if (RateLimiter::tooManyAttempts($key, 3)) {
            $this->addError('general', 'Too many submission attempts. Please try again later.');
            return;
        }

        // Honeypot validation
        if (!empty($this->website)) {
            RateLimiter::hit($key, 3600); // Block for 1 hour
            return;
        }

        // Use the comprehensive form request validation
        $request = new BookingEnquiryRequest();
        $validator = Validator::make($this->getAllFormData(), $request->rules(), $request->messages());
        
        if ($validator->fails()) {
            foreach ($validator->errors()->all() as $error) {
                $this->addError('general', $error);
            }
            return;
        }

        try {
            // Create enquiry record
            $enquiry = Enquiry::create([
                'name' => $this->name,
                'email' => $this->email,
                'phone' => $this->phone,
                'adults' => $this->adults,
                'children' => $this->children,
                'check_in' => $this->arrival_date,
                'check_out' => $this->departure_date,
                'accommodation_type' => $this->accommodation_preference,
                'message' => $this->buildMessage(),
                'source' => 'website',
                'status' => 'new',
            ]);

            // Send emails
            Mail::to(setting('contact_email', '<EMAIL>'))
                ->send(new BookingEnquiryMail($enquiry, $this->getAllFormData()));

            Mail::to($this->email)
                ->send(new BookingConfirmationMail($enquiry, $this->getAllFormData()));

            // Reset rate limiter on success
            RateLimiter::clear($key);

            // Redirect to success page
            return redirect()->route('booking.success', ['reference' => 'ENQ-' . $enquiry->id]);

        } catch (\Exception $e) {
            $this->addError('general', 'Something went wrong. Please try again or contact us directly.');
            RateLimiter::hit($key);
        }
    }

    private function buildMessage(): string
    {
        $message = [];
        
        if ($this->country) {
            $message[] = "Country: {$this->country}";
        }
        
        if ($this->accommodation_preference) {
            $message[] = "Accommodation Preference: {$this->accommodation_preference}";
        }
        
        if (!empty($this->activity_interests)) {
            $activities = collect($this->activity_interests)
                ->map(fn($id) => $this->activities->find($id)?->title)
                ->filter()
                ->join(', ');
            $message[] = "Activity Interests: {$activities}";
        }
        
        if ($this->budget_range) {
            $message[] = "Budget Range: " . ucwords(str_replace('-', ' ', $this->budget_range));
        }
        
        if ($this->special_requests) {
            $message[] = "Special Requirements: {$this->special_requests}";
        }
        
        if ($this->dietary_requirements) {
            $message[] = "Dietary Requirements: {$this->dietary_requirements}";
        }
        
        if ($this->how_did_you_hear) {
            $message[] = "How did you hear about us: " . ucwords(str_replace('_', ' ', $this->how_did_you_hear));
        }
        
        if ($this->message) {
            $message[] = "Additional Message: {$this->message}";
        }
        
        return implode("\n\n", $message);
    }

    private function getAllFormData(): array
    {
        return [
            'name' => $this->name,
            'email' => $this->email,
            'phone' => $this->phone,
            'country' => $this->country,
            'arrival_date' => $this->arrival_date,
            'departure_date' => $this->departure_date,
            'adults' => $this->adults,
            'children' => $this->children,
            'accommodation_preference' => $this->accommodation_preference,
            'activity_interests' => $this->activity_interests,
            'budget_range' => $this->budget_range,
            'special_requests' => $this->special_requests,
            'dietary_requirements' => $this->dietary_requirements,
            'message' => $this->message,
            'how_did_you_hear' => $this->how_did_you_hear,
            'marketing_consent' => $this->marketing_consent,
            'privacy_accepted' => $this->privacy_accepted,
            'website' => $this->website,
        ];
    }

    public function render()
    {
        return view('livewire.booking-enquiry');
    }
}
