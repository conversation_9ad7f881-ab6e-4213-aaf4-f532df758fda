<?php

namespace App\Filament\Resources\Activities\Schemas;

use Filament\Forms\Components\DateTimePicker;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\RichEditor;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\SpatieMediaLibraryFileUpload;
use Filament\Schemas\Schema;

class ActivityForm
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([
                Section::make('Basic Information')
                    ->schema([
                        TextInput::make('title')
                            ->required()
                            ->live(onBlur: true)
                            ->afterStateUpdated(fn (string $context, $state, callable $set) => 
                                $context === 'create' ? $set('slug', \Str::slug($state)) : null
                            ),
                        
                        TextInput::make('slug')
                            ->required()
                            ->unique(ignoreRecord: true),
                        
                        Select::make('category')
                            ->options([
                                'game_drive' => 'Game drive',
                                'walk_safari' => 'Walk safari',
                                'boat_safari' => 'Boat safari',
                                'village_tour' => 'Village tour',
                                'cultural_tour' => 'Cultural tour',
                                'fishing' => 'Fishing',
                                'night_viewing' => 'Night viewing',
                                'social_drive' => 'Social drive',
                            ])
                            ->required(),
                        
                        TextInput::make('duration')
                            ->label('Duration')
                            ->hint('e.g., "3 hours", "Full day", "2 days"'),
                    ])
                    ->columns(2),

                Section::make('Content')
                    ->schema([
                        RichEditor::make('description')
                            ->required()
                            ->toolbarButtons([
                                'bold',
                                'italic',
                                'link',
                                'bulletList',
                                'orderedList',
                                'h2',
                                'h3',
                            ])
                            ->columnSpanFull(),
                        
                        RichEditor::make('tips')
                            ->label('Tips & Recommendations')
                            ->toolbarButtons([
                                'bold',
                                'italic',
                                'link',
                                'bulletList',
                                'orderedList',
                            ])
                            ->columnSpanFull(),
                        
                        RichEditor::make('includes')
                            ->label('What\'s Included')
                            ->toolbarButtons([
                                'bold',
                                'italic',
                                'link',
                                'bulletList',
                                'orderedList',
                            ])
                            ->columnSpanFull(),
                    ]),

                Section::make('Media')
                    ->schema([
                        SpatieMediaLibraryFileUpload::make('featured_image')
                            ->label('Featured Image')
                            ->collection('featured_image')
                            ->image()
                            ->imageEditor()
                            ->imageEditorAspectRatios([
                                '16:9',
                                '4:3',
                                '1:1',
                            ])
                            ->maxSize(5120)
                            ->hint('Main image for this activity (recommended: 1200x800px)')
                            ->columnSpanFull(),
                        
                        SpatieMediaLibraryFileUpload::make('gallery')
                            ->label('Photo Gallery')
                            ->collection('gallery')
                            ->image()
                            ->imageEditor()
                            ->multiple()
                            ->reorderable()
                            ->maxFiles(8)
                            ->maxSize(5120)
                            ->hint('Additional photos (max 8 images, up to 5MB each)')
                            ->columnSpanFull(),
                    ]),

                Section::make('Publishing')
                    ->schema([
                        DateTimePicker::make('published_at')
                            ->label('Publish Date & Time')
                            ->hint('Leave empty to save as draft'),
                    ]),

                Section::make('SEO & Meta Data')
                    ->schema([
                        TextInput::make('meta_title')
                            ->label('Meta Title')
                            ->maxLength(60)
                            ->hint('Recommended: 50-60 characters'),
                        
                        Textarea::make('meta_description')
                            ->label('Meta Description')
                            ->maxLength(160)
                            ->rows(3)
                            ->hint('Recommended: 150-160 characters')
                            ->columnSpanFull(),
                        
                        TextInput::make('meta_keywords')
                            ->label('Meta Keywords')
                            ->hint('Comma-separated keywords for SEO'),
                        
                        FileUpload::make('og_image')
                            ->label('Social Media Image')
                            ->image()
                            ->directory('og-images')
                            ->hint('Recommended size: 1200x630px for social media sharing'),
                    ])
                    ->columns(2)
                    ->collapsed(),
            ]);
    }
}
