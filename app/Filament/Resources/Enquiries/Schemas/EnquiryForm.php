<?php

namespace App\Filament\Resources\Enquiries\Schemas;

use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\DateTimePicker;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Textarea;
use Filament\Schemas\Schema;

class EnquiryForm
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([
                TextInput::make('name')
                    ->required(),
                TextInput::make('email')
                    ->label('Email address')
                    ->email()
                    ->required(),
                TextInput::make('phone')
                    ->tel()
                    ->default(null),
                TextInput::make('adults')
                    ->required()
                    ->numeric()
                    ->default(2),
                TextInput::make('children')
                    ->required()
                    ->numeric()
                    ->default(0),
                DatePicker::make('check_in'),
                DatePicker::make('check_out'),
                TextInput::make('accommodation_type')
                    ->default(null),
                Textarea::make('message')
                    ->required()
                    ->columnSpanFull(),
                TextInput::make('source')
                    ->required()
                    ->default('website'),
                Select::make('status')
                    ->options([
            'new' => 'New',
            'contacted' => 'Contacted',
            'quoted' => 'Quoted',
            'booked' => 'Booked',
            'cancelled' => 'Cancelled',
            'closed' => 'Closed',
        ])
                    ->default('new')
                    ->required(),
                Textarea::make('internal_notes')
                    ->default(null)
                    ->columnSpanFull(),
                DateTimePicker::make('contacted_at'),
                TextInput::make('assigned_to')
                    ->numeric()
                    ->default(null),
            ]);
    }
}
