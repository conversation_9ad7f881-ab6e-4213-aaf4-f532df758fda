<?php

namespace App\Filament\Resources\NearbyPoints;

use App\Filament\Resources\NearbyPoints\Pages\CreateNearbyPoint;
use App\Filament\Resources\NearbyPoints\Pages\EditNearbyPoint;
use App\Filament\Resources\NearbyPoints\Pages\ListNearbyPoints;
use App\Filament\Resources\NearbyPoints\Schemas\NearbyPointForm;
use App\Filament\Resources\NearbyPoints\Tables\NearbyPointsTable;
use App\Models\NearbyPoint;
use BackedEnum;
use Filament\Resources\Resource;
use Filament\Schemas\Schema;
use Filament\Support\Icons\Heroicon;
use Filament\Tables\Table;

class NearbyPointResource extends Resource
{
    protected static ?string $model = NearbyPoint::class;

    protected static string|BackedEnum|null $navigationIcon = Heroicon::OutlinedRectangleStack;

    protected static ?string $recordTitleAttribute = 'NearbyPoints';

    public static function form(Schema $schema): Schema
    {
        return NearbyPointForm::configure($schema);
    }

    public static function table(Table $table): Table
    {
        return NearbyPointsTable::configure($table);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => ListNearbyPoints::route('/'),
            'create' => CreateNearbyPoint::route('/create'),
            'edit' => EditNearbyPoint::route('/{record}/edit'),
        ];
    }
}
