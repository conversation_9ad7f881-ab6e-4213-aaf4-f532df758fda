<?php

namespace App\Filament\Resources\NearbyPoints\Pages;

use App\Filament\Resources\NearbyPoints\NearbyPointResource;
use Filament\Actions\CreateAction;
use Filament\Resources\Pages\ListRecords;

class ListNearbyPoints extends ListRecords
{
    protected static string $resource = NearbyPointResource::class;

    protected function getHeaderActions(): array
    {
        return [
            CreateAction::make(),
        ];
    }
}
