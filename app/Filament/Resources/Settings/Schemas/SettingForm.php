<?php

namespace App\Filament\Resources\Settings\Schemas;

use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Grid;
use Filament\Forms\Get;
use Filament\Schemas\Schema;

class SettingForm
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([
                Section::make('Setting Information')
                    ->schema([
                        Grid::make(2)
                            ->schema([
                                TextInput::make('key')
                                    ->required()
                                    ->unique(ignoreRecord: true)
                                    ->regex('/^[a-z0-9_]+$/')
                                    ->hint('Use lowercase letters, numbers, and underscores only'),
                                
                                TextInput::make('label')
                                    ->required()
                                    ->hint('Human-readable label for this setting'),
                            ]),
                        
                        Select::make('type')
                            ->required()
                            ->options([
                                'text' => 'Text',
                                'textarea' => 'Textarea',
                                'email' => 'Email',
                                'url' => 'URL',
                                'image' => 'Image',
                                'json' => 'JSON',
                                'boolean' => 'Boolean',
                                'integer' => 'Integer',
                                'float' => 'Float',
                            ])
                            ->live()
                            ->default('text'),
                        
                        Select::make('group')
                            ->options([
                                'general' => 'General',
                                'contact' => 'Contact',
                                'social' => 'Social Media',
                                'seo' => 'SEO',
                                'analytics' => 'Analytics',
                                'booking' => 'Booking',
                            ])
                            ->hint('Group settings together for better organization'),
                        
                        Textarea::make('description')
                            ->hint('Optional description of what this setting controls')
                            ->columnSpanFull(),
                    ]),

                Section::make('Setting Value')
                    ->schema([
                        // Text input for most types
                        TextInput::make('value')
                            ->label('Value')
                            ->visible(fn (Get $get) => in_array($get('type'), ['text', 'email', 'url', 'integer', 'float']))
                            ->email(fn (Get $get) => $get('type') === 'email')
                            ->url(fn (Get $get) => $get('type') === 'url')
                            ->numeric(fn (Get $get) => in_array($get('type'), ['integer', 'float']))
                            ->hint(fn (Get $get) => match($get('type')) {
                                'email' => 'Enter a valid email address',
                                'url' => 'Enter a valid URL starting with http:// or https://',
                                'integer' => 'Enter a whole number',
                                'float' => 'Enter a decimal number',
                                default => 'Enter the setting value'
                            }),
                        
                        // Textarea for longer text
                        Textarea::make('value')
                            ->label('Value')
                            ->visible(fn (Get $get) => in_array($get('type'), ['textarea', 'json']))
                            ->rows(fn (Get $get) => $get('type') === 'json' ? 6 : 4)
                            ->hint(fn (Get $get) => $get('type') === 'json' 
                                ? 'Enter valid JSON data' 
                                : 'Enter multi-line text content'
                            ),
                        
                        // Toggle for boolean
                        Toggle::make('value')
                            ->label('Enabled')
                            ->visible(fn (Get $get) => $get('type') === 'boolean'),
                        
                        // File upload for images
                        FileUpload::make('value')
                            ->label('Image')
                            ->image()
                            ->directory('settings')
                            ->visible(fn (Get $get) => $get('type') === 'image')
                            ->hint('Upload an image file'),
                    ]),

                Section::make('Settings')
                    ->schema([
                        Grid::make(3)
                            ->schema([
                                TextInput::make('sort_order')
                                    ->label('Display Order')
                                    ->numeric()
                                    ->default(0)
                                    ->hint('Lower numbers appear first'),
                                
                                Toggle::make('is_public')
                                    ->label('Public Setting')
                                    ->hint('Can be accessed by frontend'),
                                
                                Toggle::make('is_system')
                                    ->label('System Setting')
                                    ->hint('Cannot be deleted')
                                    ->disabled()
                                    ->default(false),
                            ]),
                    ]),
            ]);
    }
}
