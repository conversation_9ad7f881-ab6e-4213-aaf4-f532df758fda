<?php

namespace App\Filament\Resources\Accommodations\Schemas;

use Filament\Forms\Components\DateTimePicker;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Components\RichEditor;
use Filament\Forms\Components\TagsInput;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\SpatieMediaLibraryFileUpload;
use Filament\Schemas\Schema;

class AccommodationForm
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([
                Section::make('Basic Information')
                    ->schema([
                        Grid::make(2)
                            ->schema([
                                TextInput::make('title')
                                    ->required()
                                    ->live(onBlur: true)
                                    ->afterStateUpdated(fn (string $context, $state, callable $set) => 
                                        $context === 'create' ? $set('slug', \Str::slug($state)) : null
                                    ),
                                TextInput::make('slug')
                                    ->required()
                                    ->unique(ignoreRecord: true),
                            ]),
                        
                        Select::make('type')
                            ->options([
                                'banda' => 'Luxury Banda',
                                'room' => 'Safari Room',
                                'tent' => 'Safari Tent',
                                'tree_house' => 'Tree House',
                                'inter_leading' => 'Inter-leading Rooms',
                                'campsite' => 'Campsite',
                            ])
                            ->required(),
                        
                        Textarea::make('short_intro')
                            ->label('Short Introduction')
                            ->required()
                            ->maxLength(255)
                            ->hint('Brief description for listings (max 255 characters)')
                            ->columnSpanFull(),
                    ]),

                Section::make('Content')
                    ->schema([
                        RichEditor::make('description')
                            ->required()
                            ->toolbarButtons([
                                'bold',
                                'italic',
                                'link',
                                'bulletList',
                                'orderedList',
                                'h2',
                                'h3',
                            ])
                            ->columnSpanFull(),
                        
                        TagsInput::make('features')
                            ->label('Features & Amenities')
                            ->hint('Add features one by one (press Enter after each)')
                            ->columnSpanFull(),
                    ]),

                Section::make('Media')
                    ->schema([
                        SpatieMediaLibraryFileUpload::make('featured_image')
                            ->label('Featured Image')
                            ->collection('featured_image')
                            ->image()
                            ->imageEditor()
                            ->imageEditorAspectRatios([
                                '16:9',
                                '4:3',
                                '1:1',
                            ])
                            ->maxSize(5120)
                            ->hint('Main image for this accommodation (recommended: 1200x800px)')
                            ->columnSpanFull(),
                        
                        SpatieMediaLibraryFileUpload::make('gallery')
                            ->label('Photo Gallery')
                            ->collection('gallery')
                            ->image()
                            ->imageEditor()
                            ->multiple()
                            ->reorderable()
                            ->maxFiles(10)
                            ->maxSize(5120)
                            ->hint('Additional photos (max 10 images, up to 5MB each)')
                            ->columnSpanFull(),
                    ]),

                Section::make('Details')
                    ->schema([
                        Grid::make(3)
                            ->schema([
                                TextInput::make('max_occupancy')
                                    ->label('Maximum Occupancy')
                                    ->required()
                                    ->numeric()
                                    ->minValue(1)
                                    ->maxValue(10),
                                
                                TextInput::make('priority')
                                    ->label('Display Order')
                                    ->numeric()
                                    ->default(0)
                                    ->hint('Lower numbers appear first'),
                                
                                Toggle::make('is_featured')
                                    ->label('Featured Accommodation'),
                            ]),
                        
                        Grid::make(4)
                            ->schema([
                                Toggle::make('wheelchair_access')
                                    ->label('Wheelchair Accessible'),
                                
                                Toggle::make('has_balcony')
                                    ->label('Has Balcony/Veranda'),
                                
                                Toggle::make('has_heated_shower')
                                    ->label('Hot Water'),
                                
                                Toggle::make('has_mini_bar')
                                    ->label('Mini Bar/Refreshments'),
                            ]),
                    ]),

                Section::make('Publishing')
                    ->schema([
                        DateTimePicker::make('published_at')
                            ->label('Publish Date & Time')
                            ->hint('Leave empty to save as draft'),
                    ]),

                Section::make('SEO & Meta Data')
                    ->schema([
                        Grid::make(2)
                            ->schema([
                                TextInput::make('meta_title')
                                    ->label('Meta Title')
                                    ->maxLength(60)
                                    ->hint('Recommended: 50-60 characters'),
                                
                                TextInput::make('meta_keywords')
                                    ->label('Meta Keywords')
                                    ->hint('Comma-separated keywords for SEO'),
                            ]),
                        
                        Textarea::make('meta_description')
                            ->label('Meta Description')
                            ->maxLength(160)
                            ->rows(3)
                            ->hint('Recommended: 150-160 characters')
                            ->columnSpanFull(),
                        
                        FileUpload::make('og_image')
                            ->label('Social Media Image')
                            ->image()
                            ->directory('og-images')
                            ->hint('Recommended size: 1200x630px for social media sharing'),
                    ])
                    ->collapsed(),
            ]);
    }
}
