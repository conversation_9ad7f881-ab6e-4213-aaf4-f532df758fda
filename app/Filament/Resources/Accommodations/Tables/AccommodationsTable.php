<?php

namespace App\Filament\Resources\Accommodations\Tables;

use Filament\Actions\BulkActionGroup;
use Filament\Actions\DeleteBulkAction;
use Filament\Actions\EditAction;
use Filament\Actions\ViewAction;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Columns\ImageColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\BadgeColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Filters\TernaryFilter;
use Filament\Tables\Table;

class AccommodationsTable
{
    public static function configure(Table $table): Table
    {
        return $table
            ->columns([
                ImageColumn::make('og_image')
                    ->label('Image')
                    ->size(60)
                    ->defaultImageUrl('/images/malombo_logo.jpg'),
                
                TextColumn::make('title')
                    ->searchable()
                    ->sortable()
                    ->weight('medium'),
                
                BadgeColumn::make('type')
                    ->colors([
                        'primary' => 'banda',
                        'success' => 'room',
                        'warning' => 'tent',
                        'danger' => 'tree_house',
                        'info' => 'inter_leading',
                        'gray' => 'campsite',
                    ])
                    ->icons([
                        'heroicon-o-building-office' => 'banda',
                        'heroicon-o-home' => 'room',
                        'heroicon-o-home-modern' => 'tent',
                        'heroicon-o-building-library' => 'tree_house',
                        'heroicon-o-rectangle-group' => 'inter_leading',
                        'heroicon-o-map' => 'campsite',
                    ]),
                
                TextColumn::make('max_occupancy')
                    ->label('Max Guests')
                    ->numeric()
                    ->sortable()
                    ->badge()
                    ->color('success'),
                
                IconColumn::make('is_featured')
                    ->label('Featured')
                    ->boolean()
                    ->trueIcon('heroicon-o-star')
                    ->falseIcon('heroicon-o-star')
                    ->trueColor('warning')
                    ->falseColor('gray'),
                
                BadgeColumn::make('published_at')
                    ->label('Status')
                    ->formatStateUsing(fn ($state) => $state ? 'Published' : 'Draft')
                    ->colors([
                        'success' => fn ($state) => $state !== null,
                        'gray' => fn ($state) => $state === null,
                    ]),
                
                TextColumn::make('priority')
                    ->label('Order')
                    ->numeric()
                    ->sortable()
                    ->toggleable(),
                
                IconColumn::make('wheelchair_access')
                    ->label('Accessible')
                    ->boolean()
                    ->toggleable(),
                
                IconColumn::make('has_balcony')
                    ->label('Balcony')
                    ->boolean()
                    ->toggleable(),
                
                IconColumn::make('has_heated_shower')
                    ->label('Hot Water')
                    ->boolean()
                    ->toggleable(),
                
                IconColumn::make('has_mini_bar')
                    ->label('Mini Bar')
                    ->boolean()
                    ->toggleable(),
                
                TextColumn::make('created_at')
                    ->label('Created')
                    ->dateTime('M j, Y')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                
                TextColumn::make('updated_at')
                    ->label('Updated')
                    ->dateTime('M j, Y')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                SelectFilter::make('type')
                    ->options([
                        'banda' => 'Luxury Banda',
                        'room' => 'Safari Room',
                        'tent' => 'Safari Tent',
                        'tree_house' => 'Tree House',
                        'inter_leading' => 'Inter-leading Rooms',
                        'campsite' => 'Campsite',
                    ]),
                
                TernaryFilter::make('is_featured')
                    ->label('Featured')
                    ->boolean()
                    ->trueLabel('Featured only')
                    ->falseLabel('Non-featured only')
                    ->native(false),
                
                TernaryFilter::make('published_at')
                    ->label('Published')
                    ->nullable()
                    ->trueLabel('Published only')
                    ->falseLabel('Drafts only')
                    ->native(false),
                
                TernaryFilter::make('wheelchair_access')
                    ->label('Wheelchair Accessible')
                    ->boolean()
                    ->trueLabel('Accessible only')
                    ->falseLabel('Non-accessible only')
                    ->native(false),
            ])
            ->recordActions([
                ViewAction::make(),
                EditAction::make(),
            ])
            ->toolbarActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('priority', 'asc');
    }
}
