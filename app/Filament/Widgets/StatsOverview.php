<?php

namespace App\Filament\Widgets;

use App\Models\Accommodation;
use App\Models\Activity;
use App\Models\Enquiry;
use App\Models\Setting;
use Filament\Widgets\StatsOverviewWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;

class StatsOverview extends StatsOverviewWidget
{
    protected function getStats(): array
    {
        return [
            Stat::make('Total Accommodations', Accommodation::count())
                ->description('Published accommodation types')
                ->descriptionIcon('heroicon-m-building-office')
                ->color('success'),

            Stat::make('Total Activities', Activity::count())
                ->description('Available safari activities')
                ->descriptionIcon('heroicon-m-camera')
                ->color('info'),

            Stat::make('New Enquiries', Enquiry::where('status', 'new')->count())
                ->description('Pending customer enquiries')
                ->descriptionIcon('heroicon-m-chat-bubble-left-right')
                ->color('warning'),

            Stat::make('Total Enquiries', Enquiry::count())
                ->description('All customer enquiries')
                ->descriptionIcon('heroicon-m-inbox')
                ->color('primary'),

            Stat::make('Booked Enquiries', Enquiry::where('status', 'booked')->count())
                ->description('Confirmed bookings')
                ->descriptionIcon('heroicon-m-check-circle')
                ->color('success'),

            Stat::make('Settings Configured', Setting::count())
                ->description('System settings available')
                ->descriptionIcon('heroicon-m-cog-6-tooth')
                ->color('gray'),
        ];
    }
}
