<?php

if (!function_exists('setting')) {
    /**
     * Get a setting value from the database or return default.
     *
     * @param string $key
     * @param mixed $default
     * @return mixed
     */
    function setting(string $key, $default = null)
    {
        if (class_exists(\App\Models\Setting::class)) {
            return \App\Models\Setting::get($key, $default);
        }
        
        return $default;
    }
}
