<?php

namespace App\Services;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use App\Models\Accommodation;
use App\Models\Activity;
use App\Models\BlogPost;

class SEOService
{
    /**
     * Generate structured data for hotel/lodge
     */
    public function generateHotelStructuredData(): array
    {
        return [
            '@context' => 'https://schema.org',
            '@type' => 'LodgingBusiness',
            'name' => setting('site_name', 'Malombo Selous Forest Camp'),
            'description' => setting('seo_meta_description', 'Experience luxury eco-lodge accommodation in Nyerere National Park (formerly Selous Game Reserve), Tanzania. Wildlife safaris, boat excursions, and authentic African wilderness adventures.'),
            'url' => url('/'),
            'telephone' => setting('contact_phone'),
            'email' => setting('contact_email'),
            'address' => [
                '@type' => 'PostalAddress',
                'addressCountry' => 'TZ',
                'addressLocality' => 'Nyerere National Park',
                'addressRegion' => 'Morogoro',
                'streetAddress' => setting('contact_address', 'Nyerere National Park (Selous), Tanzania')
            ],
            'geo' => [
                '@type' => 'GeoCoordinates',
                // Note: Using placeholder coordinates - replace with actual coordinates
                'latitude' => setting('location_latitude', '-8.0'),
                'longitude' => setting('location_longitude', '36.5')
            ],
            'image' => [
                asset(setting('seo_og_image', 'images/malombo_logo.jpg')),
                asset('images/lodge-aerial-view.jpg'),
                asset('images/accommodation-exterior.jpg')
            ],
            'amenityFeature' => [
                [
                    '@type' => 'LocationFeatureSpecification',
                    'name' => 'Restaurant',
                    'value' => true
                ],
                [
                    '@type' => 'LocationFeatureSpecification',
                    'name' => 'Bar',
                    'value' => true
                ],
                [
                    '@type' => 'LocationFeatureSpecification',
                    'name' => 'WiFi',
                    'value' => true
                ],
                [
                    '@type' => 'LocationFeatureSpecification',
                    'name' => 'Safari Activities',
                    'value' => true
                ],
                [
                    '@type' => 'LocationFeatureSpecification',
                    'name' => 'Boat Excursions',
                    'value' => true
                ]
            ],
            'starRating' => [
                '@type' => 'Rating',
                'ratingValue' => '5'
            ],
            'priceRange' => '$$$$',
            'currenciesAccepted' => 'USD',
            'paymentAccepted' => 'Cash, Credit Card, Bank Transfer',
            'openingHours' => '24/7',
            'checkInTime' => '14:00',
            'checkOutTime' => '10:00',
            'petsAllowed' => false,
            'smokingAllowed' => false
        ];
    }

    /**
     * Generate structured data for accommodation
     */
    public function generateAccommodationStructuredData(Accommodation $accommodation): array
    {
        $structuredData = [
            '@context' => 'https://schema.org',
            '@type' => 'Hotel',
            'name' => $accommodation->name,
            'description' => $accommodation->description,
            'url' => route('accommodation.show', $accommodation->slug),
            'image' => $accommodation->featured_image ? asset($accommodation->featured_image) : null,
            'numberOfRooms' => 1,
            'maximumAttendeeCapacity' => $accommodation->capacity,
            'amenityFeature' => []
        ];

        // Add amenities if available
        if ($accommodation->amenities) {
            foreach ($accommodation->amenities as $amenity) {
                $structuredData['amenityFeature'][] = [
                    '@type' => 'LocationFeatureSpecification',
                    'name' => $amenity,
                    'value' => true
                ];
            }
        }

        // Add offers/pricing if available
        if ($accommodation->price) {
            $structuredData['offers'] = [
                '@type' => 'Offer',
                'price' => $accommodation->price,
                'priceCurrency' => 'USD',
                'availability' => 'https://schema.org/InStock'
            ];
        }

        return $structuredData;
    }

    /**
     * Generate structured data for activities
     */
    public function generateActivityStructuredData(Activity $activity): array
    {
        return [
            '@context' => 'https://schema.org',
            '@type' => 'TouristAttraction',
            'name' => $activity->name,
            'description' => $activity->description,
            'url' => route('activities.show', $activity->slug),
            'image' => $activity->featured_image ? asset($activity->featured_image) : null,
            'touristType' => 'Safari Enthusiasts',
            'availableLanguage' => ['English', 'Swahili'],
            'isAccessibleForFree' => false,
            'location' => [
                '@type' => 'Place',
                'name' => 'Nyerere National Park',
                'address' => [
                    '@type' => 'PostalAddress',
                    'addressCountry' => 'TZ',
                    'addressLocality' => 'Nyerere National Park',
                    'addressRegion' => 'Morogoro'
                ]
            ]
        ];
    }

    /**
     * Generate structured data for blog posts
     */
    public function generateBlogPostStructuredData(BlogPost $post): array
    {
        return [
            '@context' => 'https://schema.org',
            '@type' => 'BlogPosting',
            'headline' => $post->title,
            'description' => $post->excerpt,
            'image' => $post->featured_image ? asset($post->featured_image) : null,
            'author' => [
                '@type' => 'Person',
                'name' => $post->author->name ?? setting('site_name', 'Malombo Selous Forest Camp')
            ],
            'publisher' => [
                '@type' => 'Organization',
                'name' => setting('site_name', 'Malombo Selous Forest Camp'),
                'logo' => [
                    '@type' => 'ImageObject',
                    'url' => asset(setting('site_logo', 'images/malombo_logo.jpg'))
                ]
            ],
            'datePublished' => $post->published_at->toISOString(),
            'dateModified' => $post->updated_at->toISOString(),
            'mainEntityOfPage' => [
                '@type' => 'WebPage',
                '@id' => route('blog.show', $post->slug)
            ]
        ];
    }

    /**
     * Generate breadcrumb structured data
     */
    public function generateBreadcrumbStructuredData(array $breadcrumbs): array
    {
        $itemListElement = [];
        
        foreach ($breadcrumbs as $index => $breadcrumb) {
            $itemListElement[] = [
                '@type' => 'ListItem',
                'position' => $index + 1,
                'name' => $breadcrumb['name'],
                'item' => $breadcrumb['url']
            ];
        }

        return [
            '@context' => 'https://schema.org',
            '@type' => 'BreadcrumbList',
            'itemListElement' => $itemListElement
        ];
    }

    /**
     * Generate organization structured data
     */
    public function generateOrganizationStructuredData(): array
    {
        return [
            '@context' => 'https://schema.org',
            '@type' => 'Organization',
            'name' => setting('site_name', 'Malombo Selous Forest Camp'),
            'url' => url('/'),
            'logo' => asset(setting('site_logo', 'images/malombo_logo.jpg')),
            'description' => setting('seo_meta_description', 'Luxury eco-lodge in Nyerere National Park, Tanzania'),
            'telephone' => setting('contact_phone'),
            'email' => setting('contact_email'),
            'address' => [
                '@type' => 'PostalAddress',
                'addressCountry' => 'TZ',
                'addressLocality' => 'Nyerere National Park',
                'addressRegion' => 'Morogoro',
                'streetAddress' => setting('contact_address', 'Nyerere National Park (Selous), Tanzania')
            ],
            'sameAs' => array_filter([
                setting('social_facebook'),
                setting('social_instagram'),
                setting('social_twitter'),
                setting('social_youtube'),
                setting('social_tripadvisor')
            ])
        ];
    }

    /**
     * Get meta tags for a specific page
     */
    public function getMetaTags(Request $request, array $overrides = []): array
    {
        $defaults = [
            'title' => setting('site_name', 'Malombo Selous Forest Camp'),
            'description' => setting('seo_meta_description', 'Experience luxury eco-lodge accommodation in Nyerere National Park'),
            'keywords' => setting('seo_keywords', 'Selous, Nyerere, Tanzania, Safari, Lodge'),
            'og_image' => asset(setting('seo_og_image', 'images/malombo_logo.jpg')),
            'canonical' => $request->url(),
            'robots' => 'index,follow'
        ];

        return array_merge($defaults, $overrides);
    }

    /**
     * Generate XML sitemap
     */
    public function generateSitemap(): string
    {
        $urls = collect();

        // Static pages
        $staticPages = [
            ['url' => route('home'), 'priority' => 1.0, 'changefreq' => 'weekly'],
            ['url' => route('lodge.index'), 'priority' => 0.9, 'changefreq' => 'monthly'],
            ['url' => route('accommodation.index'), 'priority' => 0.9, 'changefreq' => 'weekly'],
            ['url' => route('activities.index'), 'priority' => 0.8, 'changefreq' => 'weekly'],
            ['url' => route('facilities.index'), 'priority' => 0.7, 'changefreq' => 'monthly'],
            ['url' => route('rates.index'), 'priority' => 0.8, 'changefreq' => 'monthly'],
            ['url' => route('gallery.index'), 'priority' => 0.6, 'changefreq' => 'weekly'],
            ['url' => route('location.index'), 'priority' => 0.7, 'changefreq' => 'monthly'],
            ['url' => route('contact.index'), 'priority' => 0.8, 'changefreq' => 'monthly'],
            ['url' => route('blog.index'), 'priority' => 0.7, 'changefreq' => 'daily']
        ];

        foreach ($staticPages as $page) {
            $urls->push($page);
        }

        // Dynamic content - accommodations
        Accommodation::published()->get()->each(function ($accommodation) use ($urls) {
            $urls->push([
                'url' => route('accommodation.show', $accommodation->slug),
                'priority' => 0.8,
                'changefreq' => 'weekly',
                'lastmod' => $accommodation->updated_at->toAtomString()
            ]);
        });

        // Dynamic content - activities
        Activity::published()->get()->each(function ($activity) use ($urls) {
            $urls->push([
                'url' => route('activities.show', $activity->slug),
                'priority' => 0.7,
                'changefreq' => 'weekly',
                'lastmod' => $activity->updated_at->toAtomString()
            ]);
        });

        // Dynamic content - blog posts
        if (class_exists(BlogPost::class)) {
            BlogPost::published()->get()->each(function ($post) use ($urls) {
                $urls->push([
                    'url' => route('blog.show', $post->slug),
                    'priority' => 0.6,
                    'changefreq' => 'monthly',
                    'lastmod' => $post->updated_at->toAtomString()
                ]);
            });
        }

        // Generate XML
        $xml = '<?xml version="1.0" encoding="UTF-8"?>' . "\n";
        $xml .= '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">' . "\n";

        foreach ($urls as $url) {
            $xml .= '  <url>' . "\n";
            $xml .= '    <loc>' . htmlspecialchars($url['url']) . '</loc>' . "\n";
            $xml .= '    <priority>' . $url['priority'] . '</priority>' . "\n";
            $xml .= '    <changefreq>' . $url['changefreq'] . '</changefreq>' . "\n";
            
            if (isset($url['lastmod'])) {
                $xml .= '    <lastmod>' . $url['lastmod'] . '</lastmod>' . "\n";
            }
            
            $xml .= '  </url>' . "\n";
        }

        $xml .= '</urlset>';

        return $xml;
    }

    /**
     * Cache and return structured data
     */
    public function getCachedStructuredData(string $key, callable $generator): array
    {
        return Cache::remember("seo.structured_data.{$key}", 3600, $generator);
    }
}
