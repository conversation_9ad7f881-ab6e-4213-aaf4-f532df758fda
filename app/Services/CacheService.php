<?php

namespace App\Services;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Database\Eloquent\Model;

class CacheService
{
    /**
     * Cache tags for different content types
     */
    const TAGS = [
        'pages' => 'pages',
        'accommodations' => 'accommodations',
        'activities' => 'activities',
        'facilities' => 'facilities',
        'blog' => 'blog',
        'settings' => 'settings',
        'navigation' => 'navigation',
        'seo' => 'seo',
    ];

    /**
     * Cache TTL in seconds (1 hour default)
     */
    const DEFAULT_TTL = 3600;
    const LONG_TTL = 86400; // 24 hours
    const SHORT_TTL = 900;  // 15 minutes

    /**
     * Cache a model with tags
     */
    public static function cacheModel(Model $model, string $key = null, int $ttl = self::DEFAULT_TTL): mixed
    {
        $cacheKey = $key ?: self::getModelCacheKey($model);
        $tags = self::getModelTags($model);

        return Cache::tags($tags)->remember($cacheKey, $ttl, function () use ($model) {
            return $model;
        });
    }

    /**
     * Cache a collection with tags
     */
    public static function cacheCollection(string $model, callable $callback, string $key, array $tags = [], int $ttl = self::DEFAULT_TTL): mixed
    {
        $allTags = array_merge($tags, [self::getModelTag($model)]);

        return Cache::tags($allTags)->remember($key, $ttl, $callback);
    }

    /**
     * Cache page content
     */
    public static function cachePage(string $route, callable $callback, int $ttl = self::DEFAULT_TTL): mixed
    {
        $key = "page:{$route}";
        $tags = [self::TAGS['pages'], self::TAGS['navigation']];

        return Cache::tags($tags)->remember($key, $ttl, $callback);
    }

    /**
     * Cache navigation
     */
    public static function cacheNavigation(string $key, callable $callback, int $ttl = self::LONG_TTL): mixed
    {
        return Cache::tags([self::TAGS['navigation']])->remember($key, $ttl, $callback);
    }

    /**
     * Cache settings
     */
    public static function cacheSetting(string $key, callable $callback, int $ttl = self::LONG_TTL): mixed
    {
        $cacheKey = "setting:{$key}";
        return Cache::tags([self::TAGS['settings']])->remember($cacheKey, $ttl, $callback);
    }

    /**
     * Cache SEO data
     */
    public static function cacheSeo(string $route, callable $callback, int $ttl = self::LONG_TTL): mixed
    {
        $key = "seo:{$route}";
        return Cache::tags([self::TAGS['seo']])->remember($key, $ttl, $callback);
    }

    /**
     * Invalidate cache by tags
     */
    public static function invalidate(array $tags): void
    {
        try {
            Cache::tags($tags)->flush();
            Log::info('Cache invalidated', ['tags' => $tags]);
        } catch (\Exception $e) {
            Log::error('Cache invalidation failed', [
                'tags' => $tags,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Invalidate model cache
     */
    public static function invalidateModel(Model $model): void
    {
        $tags = self::getModelTags($model);
        self::invalidate($tags);
    }

    /**
     * Get model cache key
     */
    private static function getModelCacheKey(Model $model): string
    {
        return sprintf(
            '%s:%s:%s',
            strtolower(class_basename($model)),
            $model->getKey(),
            $model->updated_at?->timestamp ?? time()
        );
    }

    /**
     * Get cache tags for a model
     */
    private static function getModelTags(Model $model): array
    {
        $baseTag = self::getModelTag(get_class($model));
        return [$baseTag, 'all'];
    }

    /**
     * Get cache tag for model class
     */
    private static function getModelTag(string $modelClass): string
    {
        $className = strtolower(class_basename($modelClass));
        
        return match ($className) {
            'accommodation' => self::TAGS['accommodations'],
            'activity' => self::TAGS['activities'],
            'facility' => self::TAGS['facilities'],
            'blogpost', 'post' => self::TAGS['blog'],
            'page' => self::TAGS['pages'],
            'setting' => self::TAGS['settings'],
            default => $className,
        };
    }

    /**
     * Warm up essential caches
     */
    public static function warmUp(): void
    {
        Log::info('Starting cache warm-up');

        try {
            // Warm navigation cache
            app(\App\Services\NavigationService::class)->getMainNavigation();
            
            // Warm settings cache
            collect(['site_name', 'contact_email', 'whatsapp_number'])->each(function ($key) {
                setting($key);
            });

            Log::info('Cache warm-up completed');
        } catch (\Exception $e) {
            Log::error('Cache warm-up failed', ['error' => $e->getMessage()]);
        }
    }

    /**
     * Get cache statistics
     */
    public static function getStats(): array
    {
        return [
            'driver' => config('cache.default'),
            'tags_supported' => Cache::supportsTags(),
            'memory_usage' => memory_get_usage(true),
            'peak_memory' => memory_get_peak_usage(true),
        ];
    }
}
