<?php

declare(strict_types=1);

namespace App\Services;

use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Route;

class NavigationService
{
    /**
     * Get the main navigation items for the public website.
     */
    public static function getMainNavigation(): Collection
    {
        $navigation = config('sitemap.main_navigation', []);
        $routes = config('sitemap.public_routes', []);

        return collect($navigation)->map(function ($routeName) use ($routes) {
            $route = $routes[$routeName] ?? [];

            return [
                'route' => $routeName,
                'label' => $route['title'] ?? '',
                'url' => self::getRouteUrl($routeName),
                'active' => self::isRouteActive($routeName),
                'children' => self::getChildRoutes($routeName, $routes),
            ];
        });
    }

    /**
     * Get the admin navigation items for authenticated users.
     */
    public static function getAdminNavigation(): Collection
    {
        $navigation = config('sitemap.admin_navigation', []);
        $routes = config('sitemap.admin_routes', []);

        return collect($navigation)->map(function ($routeName) use ($routes) {
            $route = $routes[$routeName] ?? [];

            return [
                'route' => $routeName,
                'label' => $route['title'] ?? '',
                'icon' => $route['icon'] ?? 'document',
                'url' => self::getRouteUrl($routeName),
                'active' => self::isRouteActive($routeName),
                'permission' => $route['permission'] ?? null,
                'children' => self::getChildRoutes($routeName, $routes),
            ];
        })->filter(function ($item) {
            // Filter out items the user doesn't have permission to see
            if ($item['permission'] && auth()->check()) {
                return auth()->user()->can($item['permission']);
            }

            return true;
        });
    }

    /**
     * Get the footer navigation items.
     */
    public static function getFooterNavigation(): Collection
    {
        $navigation = config('sitemap.footer_navigation', []);
        $publicRoutes = config('sitemap.public_routes', []);
        $systemRoutes = config('sitemap.system_routes', []);
        $allRoutes = array_merge($publicRoutes, $systemRoutes);

        return collect($navigation)->map(function ($routeName) use ($allRoutes) {
            $route = $allRoutes[$routeName] ?? [];

            return [
                'route' => $routeName,
                'label' => $route['title'] ?? '',
                'url' => self::getRouteUrl($routeName),
            ];
        });
    }

    /**
     * Generate breadcrumbs for the current route.
     */
    public static function getBreadcrumbs(): Collection
    {
        $currentRoute = Route::currentRouteName();
        $breadcrumbs = collect();

        if (! $currentRoute) {
            return $breadcrumbs;
        }

        // Add home
        $breadcrumbs->push([
            'title' => 'Home',
            'url' => route('home'),
            'active' => false,
        ]);

        // Parse the route to build breadcrumbs
        $routeParts = explode('.', $currentRoute);
        $currentPath = '';

        foreach ($routeParts as $index => $part) {
            $currentPath .= ($currentPath ? '.' : '').$part;

            if ($index === count($routeParts) - 1) {
                // Last item is active
                $breadcrumbs->push([
                    'title' => self::getRouteTitle($currentPath),
                    'url' => null,
                    'active' => true,
                ]);
            } else {
                $breadcrumbs->push([
                    'title' => self::getRouteTitle($currentPath),
                    'url' => self::getRouteUrl($currentPath),
                    'active' => false,
                ]);
            }
        }

        return $breadcrumbs;
    }

    /**
     * Get a human-readable title for a route.
     */
    public static function getRouteTitle(string $routeName): string
    {
        $publicRoutes = config('sitemap.public_routes', []);
        $adminRoutes = config('sitemap.admin_routes', []);
        $systemRoutes = config('sitemap.system_routes', []);

        $allRoutes = array_merge($publicRoutes, $adminRoutes, $systemRoutes);

        return $allRoutes[$routeName]['title'] ?? ucwords(str_replace(['.', '-'], ' ', $routeName));
    }

    /**
     * Get the URL for a given route name.
     */
    protected static function getRouteUrl(string $routeName): ?string
    {
        try {
            return route($routeName);
        } catch (\Exception $e) {
            return null;
        }
    }

    /**
     * Check if a route is currently active.
     */
    protected static function isRouteActive(string $routeName): bool
    {
        $currentRoute = Route::currentRouteName();

        if (! $currentRoute) {
            return false;
        }

        // Exact match
        if ($currentRoute === $routeName) {
            return true;
        }

        // Check if current route is a child of this route
        return str_starts_with($currentRoute, $routeName.'.');
    }

    /**
     * Get child routes for a parent route.
     */
    protected static function getChildRoutes(string $parentRoute, array $routes): Collection
    {
        $route = $routes[$parentRoute] ?? [];
        $children = $route['children'] ?? [];

        return collect($children)->map(function ($child, $routeName) {
            return [
                'route' => $routeName,
                'label' => $child['title'] ?? '',
                'url' => self::getRouteUrl($routeName),
                'active' => self::isRouteActive($routeName),
                'permission' => $child['permission'] ?? null,
            ];
        })->filter(function ($item) {
            // Filter out items the user doesn't have permission to see
            if ($item['permission'] && auth()->check()) {
                return auth()->user()->can($item['permission']);
            }

            return true;
        });
    }
}
