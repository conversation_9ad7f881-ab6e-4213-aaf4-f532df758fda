<?php

namespace App\Http\Controllers\Public;

use App\Http\Controllers\Controller;

class BlogController extends Controller
{
    public function index()
    {
        // Sample blog posts data - in a real application, this would come from a database
        $blogPosts = collect([
            [
                'id' => 1,
                'title' => 'The Big Five: What Makes Them Special',
                'slug' => 'big-five-what-makes-them-special',
                'excerpt' => 'Discover the fascinating stories behind Africa\'s most iconic wildlife and what makes each of the Big Five so remarkable.',
                'content' => 'Lorem ipsum dolor sit amet...',
                'featured_image' => '/images/blog/big-five.jpg',
                'author' => '<PERSON>',
                'published_at' => now()->subDays(3),
                'category' => 'Wildlife',
                'reading_time' => 8,
                'tags' => ['Wildlife', 'Big Five', 'Safari']
            ],
            [
                'id' => 2,
                'title' => 'Best Time to Visit Kruger National Park',
                'slug' => 'best-time-visit-kruger-national-park',
                'excerpt' => 'Planning your safari? Learn about the best seasons for game viewing, weather patterns, and what to expect throughout the year.',
                'content' => 'Lorem ipsum dolor sit amet...',
                'featured_image' => '/images/blog/kruger-seasons.jpg',
                'author' => 'Mike Thompson',
                'published_at' => now()->subWeek(),
                'category' => 'Travel Tips',
                'reading_time' => 6,
                'tags' => ['Travel', 'Kruger', 'Seasons']
            ],
            [
                'id' => 3,
                'title' => 'Conservation Efforts at Malombo',
                'slug' => 'conservation-efforts-malombo',
                'excerpt' => 'Learn about our ongoing conservation initiatives and how your visit contributes to wildlife protection and community development.',
                'content' => 'Lorem ipsum dolor sit amet...',
                'featured_image' => '/images/blog/conservation.jpg',
                'author' => 'Dr. Emma Clarke',
                'published_at' => now()->subDays(10),
                'category' => 'Conservation',
                'reading_time' => 12,
                'tags' => ['Conservation', 'Wildlife Protection', 'Community']
            ],
            [
                'id' => 4,
                'title' => 'Photography Tips for Safari',
                'slug' => 'photography-tips-safari',
                'excerpt' => 'Capture stunning wildlife photos on your safari with these expert tips from professional wildlife photographers.',
                'content' => 'Lorem ipsum dolor sit amet...',
                'featured_image' => '/images/blog/photography.jpg',
                'author' => 'James Wilson',
                'published_at' => now()->subDays(14),
                'category' => 'Photography',
                'reading_time' => 10,
                'tags' => ['Photography', 'Safari', 'Wildlife']
            ],
            [
                'id' => 5,
                'title' => 'Local Communities and Tourism',
                'slug' => 'local-communities-tourism',
                'excerpt' => 'Discover how responsible tourism creates opportunities for local communities around Kruger National Park.',
                'content' => 'Lorem ipsum dolor sit amet...',
                'featured_image' => '/images/blog/community.jpg',
                'author' => 'Lisa Mbeki',
                'published_at' => now()->subDays(18),
                'category' => 'Community',
                'reading_time' => 7,
                'tags' => ['Community', 'Responsible Tourism', 'Culture']
            ],
            [
                'id' => 6,
                'title' => 'What to Pack for Your Safari',
                'slug' => 'what-to-pack-safari',
                'excerpt' => 'Essential packing guide for your African safari adventure, including clothing, equipment, and personal items.',
                'content' => 'Lorem ipsum dolor sit amet...',
                'featured_image' => '/images/blog/packing.jpg',
                'author' => 'Tom Anderson',
                'published_at' => now()->subDays(21),
                'category' => 'Travel Tips',
                'reading_time' => 5,
                'tags' => ['Packing', 'Travel Tips', 'Safari']
            ]
        ]);

        $categories = [
            'All' => $blogPosts->count(),
            'Wildlife' => $blogPosts->where('category', 'Wildlife')->count(),
            'Travel Tips' => $blogPosts->where('category', 'Travel Tips')->count(),
            'Conservation' => $blogPosts->where('category', 'Conservation')->count(),
            'Photography' => $blogPosts->where('category', 'Photography')->count(),
            'Community' => $blogPosts->where('category', 'Community')->count()
        ];

        $featuredPost = $blogPosts->first();
        $recentPosts = $blogPosts->skip(1)->take(5);

        return view('public.blog.index', compact('blogPosts', 'categories', 'featuredPost', 'recentPosts'));
    }

    public function show($slug)
    {
        // In a real application, this would fetch from database
        $post = [
            'id' => 1,
            'title' => 'The Big Five: What Makes Them Special',
            'slug' => 'big-five-what-makes-them-special',
            'excerpt' => 'Discover the fascinating stories behind Africa\'s most iconic wildlife and what makes each of the Big Five so remarkable.',
            'content' => '<p>The term "Big Five" was originally coined by big-game hunters to refer to the five most difficult and dangerous animals to hunt on foot in Africa. Today, the Big Five - lion, leopard, rhinoceros, elephant, and Cape buffalo - represent the most sought-after animals for wildlife photographers and safari enthusiasts.</p><p>Each of these magnificent creatures has evolved unique characteristics that have allowed them to thrive in Africa\'s diverse ecosystems...</p>',
            'featured_image' => '/images/blog/big-five.jpg',
            'author' => 'Sarah Johnson',
            'published_at' => now()->subDays(3),
            'category' => 'Wildlife',
            'reading_time' => 8,
            'tags' => ['Wildlife', 'Big Five', 'Safari']
        ];

        return view('public.blog.show', compact('post'));
    }
}
