<?php

namespace App\Http\Controllers\Public;

use App\Http\Controllers\Controller;
use App\Models\Rate;
use App\Models\Season;
use Illuminate\Http\Request;
use Illuminate\View\View;

class RateController extends Controller
{
    /**
     * Display the rates page.
     */
    public function index(Request $request): View
    {
        $seoData = [
            'title' => 'Rates & Pricing - '.config('malombo.name'),
            'description' => 'View our competitive safari rates and pricing for accommodations at Malombo Safari Lodge. Transparent pricing with no hidden costs.',
            'canonical' => route('rates.index'),
        ];

        // Get seasons or create sample data if none exist
        $seasons = Season::active()->ordered()->get();
        
        if ($seasons->isEmpty()) {
            $seasons = collect([
                (object)[
                    'name' => 'High Season',
                    'description' => 'Peak wildlife viewing period',
                    'start_month' => 5,
                    'end_month' => 9,
                    'peak_season' => true,
                    'pricing_multiplier' => 1.50,
                    'month_range' => 'May - September'
                ],
                (object)[
                    'name' => 'Low Season',
                    'description' => 'Green season with great birding',
                    'start_month' => 11,
                    'end_month' => 3,
                    'peak_season' => false,
                    'pricing_multiplier' => 0.80,
                    'month_range' => 'November - March'
                ]
            ]);
        }

        // Sample rates data - in production this would come from database
        $accommodationRates = [
            [
                'name' => 'Luxury Safari Suite',
                'high_season' => 8500,
                'low_season' => 6800,
                'includes' => ['All meals', 'Game drives', 'Airport transfers']
            ],
            [
                'name' => 'Premium Tent',
                'high_season' => 6500,
                'low_season' => 5200,
                'includes' => ['All meals', 'Game drives', 'Airport transfers']
            ],
            [
                'name' => 'Standard Safari Tent',
                'high_season' => 4500,
                'low_season' => 3600,
                'includes' => ['All meals', 'Game drives']
            ]
        ];

        return view('public.rates.index', compact('seoData', 'seasons', 'accommodationRates'));
    }

    /**
     * Display the policies page.
     */
    public function policies(Request $request): View
    {
        $seoData = [
            'title' => 'Booking Policies - '.config('malombo.name'),
            'description' => 'Read our booking policies, terms and conditions for your stay at Malombo Selous Forest Camp.',
            'canonical' => route('rates.policies'),
        ];

        return view('public.rates.policies', compact('seoData'));
    }

    /**
     * Display the seasons page.
     */
    public function seasons(Request $request): View
    {
        $seoData = [
            'title' => 'Safari Seasons - '.config('malombo.name'),
            'description' => 'Understand the different safari seasons at Kruger National Park and plan your perfect wildlife experience.',
            'canonical' => route('rates.seasons'),
        ];

        // Get seasons or create sample data if none exist
        $seasons = Season::active()->ordered()->get();
        
        if ($seasons->isEmpty()) {
            $seasons = collect([
                (object)[
                    'name' => 'Dry Season (High Season)',
                    'description' => 'Best time for game viewing with sparse vegetation and animals gathering around water sources.',
                    'start_month' => 5,
                    'end_month' => 9,
                    'peak_season' => true,
                    'weather_description' => 'Clear skies, minimal rainfall, warm days and cool nights.',
                    'wildlife_highlights' => ['Excellent game viewing', 'Animals gather at water sources', 'Clear photography conditions'],
                    'pricing_multiplier' => 1.50,
                    'month_range' => 'May - September'
                ],
                (object)[
                    'name' => 'Green Season (Low Season)',
                    'description' => 'Lush landscapes, newborn animals, and excellent birding opportunities.',
                    'start_month' => 11,
                    'end_month' => 3,
                    'peak_season' => false,
                    'weather_description' => 'Hot and humid with afternoon thunderstorms.',
                    'wildlife_highlights' => ['Newborn animals', 'Spectacular birding', 'Lush green scenery'],
                    'pricing_multiplier' => 0.80,
                    'month_range' => 'November - March'
                ],
                (object)[
                    'name' => 'Shoulder Season',
                    'description' => 'Perfect balance of good weather and wildlife viewing.',
                    'start_month' => 4,
                    'end_month' => 4,
                    'peak_season' => false,
                    'weather_description' => 'Pleasant temperatures with occasional light showers.',
                    'wildlife_highlights' => ['Good game viewing', 'Pleasant weather', 'Value for money'],
                    'pricing_multiplier' => 1.20,
                    'month_range' => 'April'
                ],
                (object)[
                    'name' => 'Transition Season',
                    'description' => 'Transition period with unique wildlife behavior.',
                    'start_month' => 10,
                    'end_month' => 10,
                    'peak_season' => false,
                    'weather_description' => 'Variable weather with increasing temperatures.',
                    'wildlife_highlights' => ['Changing landscapes', 'Animal behavior shifts', 'Unique experiences'],
                    'pricing_multiplier' => 1.00,
                    'month_range' => 'October'
                ]
            ]);
        }

        return view('public.rates.seasons', compact('seoData', 'seasons'));
    }
}
