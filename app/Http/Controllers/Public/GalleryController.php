<?php

namespace App\Http\Controllers\Public;

use App\Http\Controllers\Controller;
use App\Models\Accommodation;
use App\Models\Activity;
use App\Models\Facility;

class GalleryController extends Controller
{
    public function index()
    {
        $accommodationImages = Accommodation::active()
            ->with('media')
            ->get()
            ->flatMap(function ($accommodation) {
                return $accommodation->getMedia()->map(function ($media) use ($accommodation) {
                    return [
                        'url' => $media->getUrl(),
                        'thumbnail' => $media->getUrl('thumb'),
                        'title' => $accommodation->name,
                        'description' => $accommodation->description,
                        'category' => 'Accommodations',
                        'type' => 'accommodation'
                    ];
                });
            });

        $activityImages = Activity::active()
            ->with('media')
            ->get()
            ->flatMap(function ($activity) {
                return $activity->getMedia()->map(function ($media) use ($activity) {
                    return [
                        'url' => $media->getUrl(),
                        'thumbnail' => $media->getUrl('thumb'),
                        'title' => $activity->name,
                        'description' => $activity->description,
                        'category' => 'Activities',
                        'type' => 'activity'
                    ];
                });
            });

        $facilityImages = Facility::active()
            ->with('media')
            ->get()
            ->flatMap(function ($facility) {
                return $facility->getMedia()->map(function ($media) use ($facility) {
                    return [
                        'url' => $media->getUrl(),
                        'thumbnail' => $media->getUrl('thumb'),
                        'title' => $facility->name,
                        'description' => $facility->description,
                        'category' => 'Facilities',
                        'type' => 'facility'
                    ];
                });
            });

        $galleryImages = collect()
            ->merge($accommodationImages)
            ->merge($activityImages)
            ->merge($facilityImages)
            ->shuffle();

        $categories = [
            'All' => $galleryImages->count(),
            'Accommodations' => $accommodationImages->count(),
            'Activities' => $activityImages->count(),
            'Facilities' => $facilityImages->count()
        ];

        return view('public.gallery.index', compact('galleryImages', 'categories'));
    }
}
