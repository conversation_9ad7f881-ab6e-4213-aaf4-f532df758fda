<?php

namespace App\Http\Controllers\Public;

use App\Http\Controllers\Controller;

class LocationController extends Controller
{
    public function index()
    {
        $coordinates = [
            'latitude' => -24.4166, // Sample coordinates for Kruger area
            'longitude' => 31.5000
        ];

        $nearbyAirports = [
            [
                'name' => 'Kruger Mpumalanga International Airport',
                'code' => 'MQP',
                'distance' => '45 km',
                'drive_time' => '45 minutes'
            ],
            [
                'name' => 'O.R. Tambo International Airport',
                'code' => 'JNB',
                'distance' => '420 km',
                'drive_time' => '4.5 hours'
            ]
        ];

        $transportOptions = [
            [
                'type' => 'Self Drive',
                'description' => 'Drive your own vehicle to the lodge',
                'duration' => '4.5 hours from Johannesburg',
                'cost' => 'Fuel costs only'
            ],
            [
                'type' => 'Shuttle Service',
                'description' => 'Shared shuttle from airports',
                'duration' => '1 hour from MQP Airport',
                'cost' => 'R850 per person'
            ],
            [
                'type' => 'Private Transfer',
                'description' => 'Private vehicle transfer',
                'duration' => '45 minutes from MQP Airport',
                'cost' => 'R2500 per vehicle'
            ]
        ];

        return view('public.location.index', compact('coordinates', 'nearbyAirports', 'transportOptions'));
    }

    public function gettingHere()
    {
        return view('public.location.getting-here');
    }
}
