<?php

namespace App\Http\Controllers\Public;

use App\Http\Controllers\Controller;
use App\Models\Enquiry;

class BookingController extends Controller
{
    public function index()
    {
        return view('public.booking.index');
    }
    
    public function success($reference = null)
    {
        $enquiry = null;
        
        if ($reference) {
            $enquiry = Enquiry::where('id', substr($reference, 4))->first(); // Remove 'ENQ-' prefix
        }
        
        return view('public.booking.success', compact('enquiry'));
    }
}
