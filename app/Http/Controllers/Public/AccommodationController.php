<?php

declare(strict_types=1);

namespace App\Http\Controllers\Public;

use App\Http\Controllers\Controller;
use App\Models\Accommodation;
use App\Services\SEOService;
use Illuminate\Http\Request;
use Illuminate\View\View;

class AccommodationController extends Controller
{
    protected $seoService;

    public function __construct(SEOService $seoService)
    {
        $this->seoService = $seoService;
    }

    public function index(Request $request): View
    {
        $accommodations = Accommodation::published()
            ->byPriority()
            ->get();

        // SEO data
        $seoData = $this->seoService->getMetaTags($request, [
            'title' => 'Luxury Safari Accommodation | ' . setting('site_name', 'Malombo Selous Forest Camp'),
            'description' => 'Discover our exclusive safari accommodation in Nyerere National Park. Luxury tented camps and eco-friendly lodges with stunning wilderness views.',
            'keywords' => 'safari accommodation, luxury tents, eco lodge, Nyerere National Park, Selous, Tanzania'
        ]);

        // Structured data
        $structuredData = [
            'breadcrumbs' => $this->seoService->generateBreadcrumbStructuredData([
                ['name' => 'Home', 'url' => route('home')],
                ['name' => 'Accommodation', 'url' => route('accommodation.index')]
            ])
        ];

        return view('public.accommodation.index', compact('accommodations', 'seoData', 'structuredData'));
    }

    public function show(Request $request, string $slug): View
    {
        $accommodation = Accommodation::published()
            ->where('slug', $slug)
            ->firstOrFail();

        // SEO data
        $seoData = $this->seoService->getMetaTags($request, [
            'title' => $accommodation->name . ' | Accommodation | ' . setting('site_name', 'Malombo Selous Forest Camp'),
            'description' => $accommodation->excerpt ?: substr(strip_tags($accommodation->description), 0, 160),
            'keywords' => 'safari accommodation, ' . $accommodation->name . ', luxury tent, Nyerere National Park',
            'og_image' => $accommodation->featured_image ? asset($accommodation->featured_image) : null
        ]);

        // Structured data
        $structuredData = [
            'accommodation' => $this->seoService->generateAccommodationStructuredData($accommodation),
            'breadcrumbs' => $this->seoService->generateBreadcrumbStructuredData([
                ['name' => 'Home', 'url' => route('home')],
                ['name' => 'Accommodation', 'url' => route('accommodation.index')],
                ['name' => $accommodation->name, 'url' => route('accommodation.show', $accommodation->slug)]
            ])
        ];

        return view('public.accommodation.show', compact('accommodation', 'seoData', 'structuredData'));
    }
}
