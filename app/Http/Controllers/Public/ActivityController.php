<?php

namespace App\Http\Controllers\Public;

use App\Http\Controllers\Controller;
use App\Models\Activity;
use Illuminate\Http\Request;
use Illuminate\View\View;

class ActivityController extends Controller
{
    /**
     * Display a listing of activities.
     */
    public function index(Request $request): View
    {
        $seoData = [
            'title' => 'Safari Activities - '.config('malombo.name'),
            'description' => 'Discover amazing safari activities at Malombo Selous Forest Camp. Game drives, walking safaris, boat safaris and more.',
            'canonical' => route('activities.index'),
        ];

        $activities = Activity::where('is_active', true)
            ->where('published_at', '<=', now())
            ->orderBy('sort_order')
            ->orderBy('title')
            ->get();

        return view('public.activities.index', compact('seoData', 'activities'));
    }

    /**
     * Display the specified activity.
     */
    public function show(Request $request, string $slug): View
    {
        $activity = Activity::where('slug', $slug)
            ->where('is_active', true)
            ->where('published_at', '<=', now())
            ->firstOrFail();

        $seoData = [
            'title' => $activity->meta_title ?? $activity->title.' - '.config('malombo.name'),
            'description' => $activity->meta_description ?? $activity->description,
            'canonical' => route('activities.show', $activity->slug),
        ];

        // Get related activities
        $relatedActivities = Activity::where('id', '!=', $activity->id)
            ->where('is_active', true)
            ->where('published_at', '<=', now())
            ->when($activity->category, function ($query, $category) {
                return $query->where('category', $category);
            })
            ->orderBy('sort_order')
            ->take(3)
            ->get();

        return view('public.activities.show', compact('seoData', 'activity', 'relatedActivities'));
    }
}
