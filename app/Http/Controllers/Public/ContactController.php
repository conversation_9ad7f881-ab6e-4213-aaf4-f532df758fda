<?php

declare(strict_types=1);

namespace App\Http\Controllers\Public;

use App\Http\Controllers\Controller;
use App\Models\Enquiry;
use App\Services\NavigationService;
use Illuminate\Http\Request;
use Illuminate\View\View;

class ContactController extends Controller
{
    public function __construct(
        protected NavigationService $navigation
    ) {}

    /**
     * Display the contact page.
     */
    public function index(Request $request): View
    {
        $seoData = [
            'title' => 'Contact Us - '.config('malombo.name'),
            'description' => 'Get in touch with Malombo Selous Forest Camp for enquiries and information.',
            'canonical' => route('contact.index'),
        ];

        $contactInfo = config('malombo.contact');

        return view('public.contact.index', compact('seoData', 'contactInfo'));
    }

    /**
     * Display the reservations page.
     */
    public function reservations(Request $request): View
    {
        $seoData = [
            'title' => 'Reservations - '.config('malombo.name'),
            'description' => 'Make a booking enquiry for your safari adventure at Malombo Selous Forest Camp.',
            'canonical' => route('contact.reservations'),
        ];

        return view('public.contact.reservations', compact('seoData'));
    }

    /**
     * Submit a general enquiry.
     */
    public function submitEnquiry(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'phone' => 'nullable|string|max:20',
            'subject' => 'required|string|max:255',
            'message' => 'required|string|max:2000',
        ]);

        // Store the enquiry
        Enquiry::create([
            'name' => $validated['name'],
            'email' => $validated['email'],
            'phone' => $validated['phone'],
            'message' => $validated['subject'] . "\n\n" . $validated['message'],
            'source' => 'website',
            'status' => 'new',
        ]);

        return redirect()->route('contact.index')->with('success', 'Thank you for your enquiry. We will get back to you soon.');
    }

    /**
     * Submit a reservation enquiry.
     */
    public function submitReservation(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'phone' => 'nullable|string|max:20',
            'arrival_date' => 'required|date|after:today',
            'departure_date' => 'required|date|after:arrival_date',
            'adults' => 'required|integer|min:1|max:20',
            'children' => 'integer|min:0|max:20',
            'accommodation_preference' => 'nullable|string|max:255',
            'special_requests' => 'nullable|string|max:1000',
        ]);

        // Store the reservation enquiry
        $message = "Accommodation Preference: " . ($validated['accommodation_preference'] ?? 'None specified');
        if (!empty($validated['special_requests'])) {
            $message .= "\n\nSpecial Requests: " . $validated['special_requests'];
        }

        Enquiry::create([
            'name' => $validated['name'],
            'email' => $validated['email'],
            'phone' => $validated['phone'],
            'adults' => $validated['adults'],
            'children' => $validated['children'] ?? 0,
            'check_in' => $validated['arrival_date'],
            'check_out' => $validated['departure_date'],
            'accommodation_type' => $validated['accommodation_preference'],
            'message' => $message,
            'source' => 'website',
            'status' => 'new',
        ]);

        return redirect()->route('contact.reservations')->with('success', 'Thank you for your reservation enquiry. We will contact you within 24 hours.');
    }
}
