<?php

declare(strict_types=1);

namespace App\Http\Controllers\Public;

use App\Http\Controllers\Controller;
use App\Models\Accommodation;
use App\Models\Activity;
use App\Models\Facility;
use App\Models\NearbyPoint;
use App\Services\SEOService;
use Illuminate\Http\Request;
use Illuminate\View\View;

class HomeController extends Controller
{
    protected $seoService;

    public function __construct(SEOService $seoService)
    {
        $this->seoService = $seoService;
    }

    /**
     * Display the homepage.
     */
    public function index(Request $request): View
    {
        // Featured accommodations (max 3)
        $featuredAccommodations = Accommodation::published()
            ->featured()
            ->byPriority()
            ->take(3)
            ->get();

        // Top activities (max 6)
        $topActivities = Activity::published()
            ->inRandomOrder()
            ->take(6)
            ->get();

        // Key facilities for highlights
        $keyFacilities = Facility::active()
            ->ordered()
            ->take(6)
            ->get();

        // Nearby points for "How to get here" section
        $nearbyPoints = NearbyPoint::active()
            ->ordered()
            ->take(4)
            ->get();

        // SEO data
        $seoData = $this->seoService->getMetaTags($request, [
            'title' => setting('site_name', 'Malombo Selous Forest Camp') . ' | Luxury Safari Lodge in Nyerere National Park',
            'description' => setting('seo_meta_description', 'Experience luxury eco-lodge accommodation in Nyerere National Park (Selous), Tanzania. Wildlife safaris, boat excursions, and authentic African wilderness adventures.'),
            'keywords' => setting('seo_keywords', 'Selous Game Reserve, Nyerere National Park, Tanzania Safari, Eco Lodge, Wildlife, Rufiji River'),
        ]);

        // Structured data
        $structuredData = [
            'hotel' => $this->seoService->getCachedStructuredData('hotel', function () {
                return $this->seoService->generateHotelStructuredData();
            }),
            'organization' => $this->seoService->getCachedStructuredData('organization', function () {
                return $this->seoService->generateOrganizationStructuredData();
            }),
            'breadcrumbs' => $this->seoService->generateBreadcrumbStructuredData([
                ['name' => 'Home', 'url' => route('home')]
            ])
        ];

        return view('public.home.index', compact(
            'featuredAccommodations',
            'topActivities', 
            'keyFacilities',
            'nearbyPoints',
            'seoData',
            'structuredData'
        ));
    }
}
