<?php

declare(strict_types=1);

namespace App\Http\Controllers\Public;

use App\Http\Controllers\Controller;
use App\Models\Accommodation;
use App\Models\Activity;
use App\Models\Facility;
use App\Services\NavigationService;
use Illuminate\Http\Request;
use Illuminate\View\View;

class LodgeController extends Controller
{
    public function __construct(
        protected NavigationService $navigation
    ) {}

    /**
     * Display the main lodge page (about + experience overview).
     */
    public function index(Request $request): View
    {
        $seoData = [
            'title' => 'The Lodge - '.config('malombo.name'),
            'description' => 'Discover our eco-luxury safari lodge in the heart of Nyerere National Park. Authentic African wilderness meets sustainable comfort.',
            'canonical' => route('lodge.index'),
        ];

        // Get featured accommodations for showcase
        $accommodations = Accommodation::where('is_active', true)
            ->where('is_featured', true)
            ->orderBy('sort_order')
            ->take(3)
            ->get();

        // Get signature experiences
        $signatureActivities = Activity::where('is_active', true)
            ->where('is_featured', true)
            ->orderBy('sort_order')
            ->take(4)
            ->get();

        // Get key facilities
        $keyFacilities = Facility::where('is_active', true)
            ->where('is_featured', true)
            ->orderBy('sort_order')
            ->take(6)
            ->get();

        return view('public.lodge.index', compact(
            'seoData',
            'accommodations',
            'signatureActivities',
            'keyFacilities'
        ));
    }

    /**
     * Display detailed about page.
     */
    public function about(Request $request): View
    {
        $seoData = [
            'title' => 'About Us - '.config('malombo.name'),
            'description' => 'Learn about our commitment to sustainable tourism and conservation in Tanzania\'s Nyerere National Park.',
            'canonical' => route('lodge.about'),
        ];

        return view('public.lodge.about', compact('seoData'));
    }

    /**
     * Display the complete experience page.
     */
    public function experience(Request $request): View
    {
        $seoData = [
            'title' => 'Safari Experience - '.config('malombo.name'),
            'description' => 'Immerse yourself in authentic African wilderness. From sunrise game drives to starlit dinners by the river.',
            'canonical' => route('lodge.experience'),
        ];

        // Get all active activities for the experience showcase
        $activities = Activity::where('is_active', true)
            ->orderBy('sort_order')
            ->get();

        return view('public.lodge.experience', compact('seoData', 'activities'));
    }

    /**
     * Display the history and heritage page.
     */
    public function history(Request $request): View
    {
        $seoData = [
            'title' => 'Our History - '.config('malombo.name'),
            'description' => 'Discover the rich history of our lodge and the legendary Selous Game Reserve, now Nyerere National Park.',
            'canonical' => route('lodge.history'),
        ];

        return view('public.lodge.history', compact('seoData'));
    }
}
