<?php

namespace App\Http\Controllers\Public;

use App\Http\Controllers\Controller;
use App\Models\Facility;
use Illuminate\Http\Request;
use Illuminate\View\View;

class FacilityController extends Controller
{
    /**
     * Display a listing of facilities.
     */
    public function index(Request $request): View
    {
        $seoData = [
            'title' => 'Facilities & Amenities - '.config('malombo.name'),
            'description' => 'Discover the luxury facilities and amenities at Malombo Selous Forest Camp. From dining to wellness, we have everything for your comfort.',
            'canonical' => route('facilities.index'),
        ];

        $facilities = Facility::where('is_active', true)
            ->orderBy('sort_order')
            ->orderBy('title')
            ->get();

        return view('public.facilities.index', compact('seoData', 'facilities'));
    }

    /**
     * Display the specified facility.
     */
    public function show(Request $request, string $slug): View
    {
        $facility = Facility::where('slug', $slug)
            ->where('is_active', true)
            ->firstOrFail();

        $seoData = [
            'title' => $facility->meta_title ?? $facility->title.' - '.config('malombo.name'),
            'description' => $facility->meta_description ?? $facility->description,
            'canonical' => route('facilities.show', $facility->slug),
        ];

        // Get related facilities
        $relatedFacilities = Facility::where('id', '!=', $facility->id)
            ->where('is_active', true)
            ->when($facility->category, function ($query, $category) {
                return $query->where('category', $category);
            })
            ->orderBy('sort_order')
            ->take(3)
            ->get();

        return view('public.facilities.show', compact('seoData', 'facility', 'relatedFacilities'));
    }
}
