<?php

declare(strict_types=1);

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Services\NavigationService;
use Illuminate\Http\Request;
use Illuminate\View\View;

class DashboardController extends Controller
{
    public function __construct(
        protected NavigationService $navigation
    ) {}

    /**
     * Display the admin dashboard.
     */
    public function index(Request $request): View
    {
        // Get dashboard statistics
        $stats = [
            'total_enquiries' => 0, // TODO: Implement when models are created
            'pending_enquiries' => 0,
            'total_blog_posts' => 0,
            'published_posts' => 0,
        ];

        // Get recent activities
        $recentActivities = collect(); // TODO: Implement activity log

        return view('admin.dashboard.index', compact('stats', 'recentActivities'));
    }
}
