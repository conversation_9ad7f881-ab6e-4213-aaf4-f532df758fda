<?php

namespace App\Http\Controllers;

use App\Services\SEOService;
use Illuminate\Http\Response;

class SitemapController extends Controller
{
    protected $seoService;

    public function __construct(SEOService $seoService)
    {
        $this->seoService = $seoService;
    }

    /**
     * Generate and return XML sitemap
     */
    public function index(): Response
    {
        $xml = $this->seoService->generateSitemap();

        return response($xml, 200, [
            'Content-Type' => 'application/xml',
            'Cache-Control' => 'public, max-age=86400', // Cache for 24 hours
        ]);
    }

    /**
     * Generate robots.txt
     */
    public function robots(): Response
    {
        $content = "User-agent: *\n";
        
        // Allow all by default for production, but check environment
        if (app()->environment('production')) {
            $content .= "Disallow: /admin\n";
            $content .= "Disallow: /filament\n";
            $content .= "Disallow: /livewire\n";
            $content .= "Disallow: /_debugbar\n";
            $content .= "Allow: /\n";
        } else {
            // Disallow all on non-production environments
            $content .= "Disallow: /\n";
        }

        $content .= "\nSitemap: " . route('sitemap.xml') . "\n";

        return response($content, 200, [
            'Content-Type' => 'text/plain',
            'Cache-Control' => 'public, max-age=86400',
        ]);
    }
}
