<?php

declare(strict_types=1);

namespace App\Http\Controllers\System;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\View\View;

class LegalController extends Controller
{
    /**
     * Display the privacy policy page.
     */
    public function privacy(Request $request): View
    {
        $seoData = [
            'title' => 'Privacy Policy - '.config('malombo.name'),
            'description' => 'Learn about how we protect your personal information.',
            'canonical' => route('legal.privacy'),
        ];

        return view('system.legal.privacy', compact('seoData'));
    }

    /**
     * Display the cookie policy page.
     */
    public function cookies(Request $request): View
    {
        $seoData = [
            'title' => 'Cookie Policy - '.config('malombo.name'),
            'description' => 'Information about how we use cookies on our website.',
            'canonical' => route('legal.cookies'),
        ];

        return view('system.legal.cookies', compact('seoData'));
    }

    /**
     * Display the terms of service page.
     */
    public function terms(Request $request): View
    {
        $seoData = [
            'title' => 'Terms of Service - '.config('malombo.name'),
            'description' => 'Terms and conditions for using our website and services.',
            'canonical' => route('legal.terms'),
        ];

        return view('system.legal.terms', compact('seoData'));
    }

    /**
     * Display the disclaimer page.
     */
    public function disclaimer(Request $request): View
    {
        $seoData = [
            'title' => 'Disclaimer - '.config('malombo.name'),
            'description' => 'Important disclaimers and legal information.',
            'canonical' => route('legal.disclaimer'),
        ];

        return view('system.legal.disclaimer', compact('seoData'));
    }
}
