<?php

declare(strict_types=1);

namespace App\Http\Controllers\System;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\View\View;

class ErrorController extends Controller
{
    /**
     * Display the 404 error page.
     */
    public function notFound(Request $request): View
    {
        $seoData = [
            'title' => 'Page Not Found - '.config('malombo.name'),
            'description' => 'The page you are looking for could not be found.',
        ];

        return view('system.errors.404', compact('seoData'))->setStatusCode(404);
    }

    /**
     * Display the 500 error page.
     */
    public function serverError(Request $request): View
    {
        $seoData = [
            'title' => 'Server Error - '.config('malombo.name'),
            'description' => 'An unexpected error occurred. Please try again later.',
        ];

        return view('system.errors.500', compact('seoData'))->setStatusCode(500);
    }

    /**
     * Display the 503 maintenance page.
     */
    public function maintenance(Request $request): View
    {
        $seoData = [
            'title' => 'Site Maintenance - '.config('malombo.name'),
            'description' => 'The site is currently under maintenance. Please check back soon.',
        ];

        return view('system.errors.503', compact('seoData'))->setStatusCode(503);
    }
}
