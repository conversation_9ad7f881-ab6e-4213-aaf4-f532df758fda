<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use App\Rules\NoMaliciousContent;

class BookingEnquiryRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            // Travel Details
            'arrival_date' => ['required', 'date', 'after:today'],
            'departure_date' => ['required', 'date', 'after:arrival_date'],
            'adults' => ['required', 'integer', 'min:1', 'max:10'],
            'children' => ['nullable', 'integer', 'min:0', 'max:8'],
            
            // Accommodation Selection
            'accommodation_ids' => ['nullable', 'array'],
            'accommodation_ids.*' => ['exists:accommodations,id'],
            
            // Activity Selection
            'activity_ids' => ['nullable', 'array'],
            'activity_ids.*' => ['exists:activities,id'],
            
            // Contact Information
            'name' => ['required', 'string', 'max:100', new NoMaliciousContent],
            'email' => ['required', 'email:dns', 'max:255'],
            'phone' => ['required', 'string', 'regex:/^[\+]?[\d\s\-\(\)]{10,20}$/', new NoMaliciousContent],
            'country' => ['required', 'string', 'max:100', new NoMaliciousContent],
            
            // Special Requests
            'special_requests' => ['nullable', 'string', 'max:1000', new NoMaliciousContent],
            'dietary_requirements' => ['nullable', 'string', 'max:500', new NoMaliciousContent],
            
            // Marketing & Privacy
            'marketing_consent' => ['boolean'],
            'privacy_accepted' => ['required', 'accepted'],
            
            // Honeypot (spam protection)
            'website' => ['nullable', 'max:0'], // Should be empty
            
            // Rate limiting - remove the invalid required_if rule
            // 'g-recaptcha-response' => ['required_if:' . config('services.recaptcha.enabled', false)],
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'arrival_date.required' => 'Please select your arrival date.',
            'arrival_date.after' => 'Arrival date must be in the future.',
            'departure_date.required' => 'Please select your departure date.',
            'departure_date.after' => 'Departure date must be after arrival date.',
            'adults.required' => 'Please specify the number of adults.',
            'adults.min' => 'At least one adult is required.',
            'adults.max' => 'Maximum 10 adults allowed.',
            'children.max' => 'Maximum 8 children allowed.',
            'email.email' => 'Please provide a valid email address.',
            'email.dns' => 'Please provide an email with a valid domain.',
            'phone.regex' => 'Please provide a valid phone number.',
            'privacy_accepted.required' => 'You must accept the privacy policy.',
            'privacy_accepted.accepted' => 'You must accept the privacy policy.',
            'website.max' => 'Spam protection triggered.',
            'g-recaptcha-response.required_if' => 'Please complete the reCAPTCHA verification.',
        ];
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            // Additional validation logic
            $this->validateDateRange($validator);
            $this->validateGuestCount($validator);
            $this->checkSpamProtection($validator);
        });
    }

    /**
     * Validate the date range.
     */
    protected function validateDateRange($validator): void
    {
        if ($this->arrival_date && $this->departure_date) {
            $arrival = \Carbon\Carbon::parse($this->arrival_date);
            $departure = \Carbon\Carbon::parse($this->departure_date);
            
            $daysDiff = $departure->diffInDays($arrival);
            
            if ($daysDiff < 1) {
                $validator->errors()->add('departure_date', 'Minimum stay is 1 night.');
            }
            
            if ($daysDiff > 30) {
                $validator->errors()->add('departure_date', 'Maximum stay is 30 nights.');
            }
        }
    }

    /**
     * Validate guest count.
     */
    protected function validateGuestCount($validator): void
    {
        $totalGuests = ($this->adults ?? 0) + ($this->children ?? 0);
        
        if ($totalGuests > 12) {
            $validator->errors()->add('children', 'Total guests cannot exceed 12.');
        }
    }

    /**
     * Check spam protection.
     */
    protected function checkSpamProtection($validator): void
    {
        // Honeypot field check
        if (!empty($this->website)) {
            $validator->errors()->add('website', 'Spam protection triggered.');
        }
        
        // Check submission speed (too fast = likely bot)
        $submissionTime = $this->input('submission_time');
        if ($submissionTime && (time() - $submissionTime) < 5) {
            $validator->errors()->add('general', 'Form submitted too quickly.');
        }
        
        // Check for duplicate content patterns
        $suspiciousContent = [
            'lorem ipsum',
            'test test test',
            str_repeat('a', 20),
        ];
        
        foreach (['name', 'special_requests'] as $field) {
            $value = strtolower($this->input($field, ''));
            foreach ($suspiciousContent as $pattern) {
                if (strpos($value, $pattern) !== false) {
                    $validator->errors()->add($field, 'Content appears to be spam.');
                    break;
                }
            }
        }
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Sanitize input data
        $this->merge([
            'name' => $this->sanitizeString($this->name),
            'phone' => $this->sanitizePhone($this->phone),
            'special_requests' => $this->sanitizeString($this->special_requests),
            'dietary_requirements' => $this->sanitizeString($this->dietary_requirements),
        ]);
    }

    /**
     * Sanitize string input.
     */
    protected function sanitizeString(?string $value): ?string
    {
        if (!$value) return $value;
        
        // Remove null bytes and control characters
        $value = preg_replace('/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/', '', $value);
        
        // Trim whitespace
        $value = trim($value);
        
        // Remove excessive whitespace
        $value = preg_replace('/\s+/', ' ', $value);
        
        return $value;
    }

    /**
     * Sanitize phone number.
     */
    protected function sanitizePhone(?string $phone): ?string
    {
        if (!$phone) return $phone;
        
        // Remove all non-digit, non-plus, non-space, non-dash, non-parentheses characters
        return preg_replace('/[^\d\+\s\-\(\)]/', '', $phone);
    }
}
