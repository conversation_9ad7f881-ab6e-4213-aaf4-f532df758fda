<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class CacheControlMiddleware
{
    /**
     * Cache configurations for different route patterns
     */
    private array $cacheRules = [
        // Static assets - long cache
        'assets/*' => [
            'max_age' => 31536000, // 1 year
            'public' => true,
            'immutable' => true,
        ],
        
        // Images - long cache with revalidation
        'images/*' => [
            'max_age' => 2592000, // 30 days
            'public' => true,
            's_maxage' => 2592000,
        ],
        
        // API endpoints - short cache
        'api/*' => [
            'max_age' => 300, // 5 minutes
            'public' => false,
            'must_revalidate' => true,
        ],
        
        // Public pages - medium cache
        '/' => [
            'max_age' => 1800, // 30 minutes
            'public' => true,
            's_maxage' => 3600, // 1 hour for CDN
        ],
        
        // Content pages - medium cache
        'lodge/*' => [
            'max_age' => 3600, // 1 hour
            'public' => true,
            's_maxage' => 7200, // 2 hours for CDN
        ],
        
        'accommodation/*' => [
            'max_age' => 3600,
            'public' => true,
            's_maxage' => 7200,
        ],
        
        'activities/*' => [
            'max_age' => 3600,
            'public' => true,
            's_maxage' => 7200,
        ],
        
        // Blog posts - longer cache
        'blog/*' => [
            'max_age' => 7200, // 2 hours
            'public' => true,
            's_maxage' => 14400, // 4 hours for CDN
        ],
        
        // Admin pages - no cache
        'admin/*' => [
            'no_cache' => true,
            'no_store' => true,
            'must_revalidate' => true,
        ],
        
        // User dashboard - no cache
        'dashboard' => [
            'no_cache' => true,
            'private' => true,
            'must_revalidate' => true,
        ],
    ];

    public function handle(Request $request, Closure $next): Response
    {
        $response = $next($request);

        // Only apply caching to successful GET requests
        if (!$request->isMethod('GET') || !$response->isSuccessful()) {
            return $response;
        }

        $this->applyCacheHeaders($request, $response);

        return $response;
    }

    private function applyCacheHeaders(Request $request, Response $response): void
    {
        $path = $request->path();
        $config = $this->getCacheConfigForPath($path);

        if (empty($config)) {
            return;
        }

        $cacheControl = [];

        // Handle no-cache scenarios
        if (isset($config['no_cache']) && $config['no_cache']) {
            $cacheControl[] = 'no-cache';
            if (isset($config['no_store']) && $config['no_store']) {
                $cacheControl[] = 'no-store';
            }
            if (isset($config['must_revalidate']) && $config['must_revalidate']) {
                $cacheControl[] = 'must-revalidate';
            }
            if (isset($config['private']) && $config['private']) {
                $cacheControl[] = 'private';
            }
        } else {
            // Handle caching scenarios
            if (isset($config['public']) && $config['public']) {
                $cacheControl[] = 'public';
            } elseif (isset($config['private']) && $config['private']) {
                $cacheControl[] = 'private';
            }

            if (isset($config['max_age'])) {
                $cacheControl[] = "max-age={$config['max_age']}";
            }

            if (isset($config['s_maxage'])) {
                $cacheControl[] = "s-maxage={$config['s_maxage']}";
            }

            if (isset($config['must_revalidate']) && $config['must_revalidate']) {
                $cacheControl[] = 'must-revalidate';
            }

            if (isset($config['immutable']) && $config['immutable']) {
                $cacheControl[] = 'immutable';
            }

            // Set ETag for content that can be cached
            if (isset($config['max_age']) && $config['max_age'] > 0) {
                $etag = md5($response->getContent() . $response->getLastModified());
                $response->setEtag($etag);
            }

            // Set Last-Modified header
            if (!$response->headers->has('Last-Modified')) {
                $response->setLastModified(new \DateTime());
            }
        }

        if (!empty($cacheControl)) {
            $response->headers->set('Cache-Control', implode(', ', $cacheControl));
        }

        // Security headers
        $this->addSecurityHeaders($response);
    }

    private function getCacheConfigForPath(string $path): array
    {
        // Direct match first
        if (isset($this->cacheRules[$path])) {
            return $this->cacheRules[$path];
        }

        // Pattern matching
        foreach ($this->cacheRules as $pattern => $config) {
            if (str_contains($pattern, '*')) {
                $regex = str_replace('*', '.*', $pattern);
                if (preg_match("/^{$regex}$/", $path)) {
                    return $config;
                }
            }
        }

        // Default for public content
        if (!str_starts_with($path, 'admin/') && !str_starts_with($path, 'dashboard')) {
            return [
                'max_age' => 1800, // 30 minutes
                'public' => true,
            ];
        }

        return [];
    }

    private function addSecurityHeaders(Response $response): void
    {
        // Security headers for all responses
        $response->headers->set('X-Content-Type-Options', 'nosniff');
        $response->headers->set('X-Frame-Options', 'SAMEORIGIN');
        $response->headers->set('X-XSS-Protection', '1; mode=block');
        $response->headers->set('Referrer-Policy', 'strict-origin-when-cross-origin');
        
        // Content Security Policy for HTML responses
        if (str_contains($response->headers->get('Content-Type', ''), 'text/html')) {
            $csp = [
                "default-src 'self'",
                "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://www.googletagmanager.com https://www.google-analytics.com",
                "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com",
                "font-src 'self' https://fonts.gstatic.com",
                "img-src 'self' data: https:",
                "connect-src 'self' https://www.google-analytics.com",
                "frame-src 'self' https://www.google.com",
            ];
            
            $response->headers->set('Content-Security-Policy', implode('; ', $csp));
        }
    }
}
