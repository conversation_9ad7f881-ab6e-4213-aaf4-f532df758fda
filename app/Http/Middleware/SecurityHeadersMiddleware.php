<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class SecurityHeadersMiddleware
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next): Response
    {
        $response = $next($request);

        // Security headers
        $response->headers->set('X-Content-Type-Options', 'nosniff');
        $response->headers->set('X-Frame-Options', 'SAMEORIGIN');
        $response->headers->set('X-XSS-Protection', '1; mode=block');
        $response->headers->set('Referrer-Policy', 'strict-origin-when-cross-origin');
        $response->headers->set('Permissions-Policy', 'camera=(), microphone=(), geolocation=()');

        // HSTS (HTTP Strict Transport Security)
        if ($request->isSecure()) {
            $response->headers->set('Strict-Transport-Security', 'max-age=31536000; includeSubDomains; preload');
        }

        // Content Security Policy
        if (config('performance.http_cache.csp.enabled', true)) {
            $cspDirectives = config('performance.http_cache.csp.directives', []);
            $csp = collect($cspDirectives)->map(function ($value, $directive) {
                if (is_array($value)) {
                    return $directive . ' ' . implode(' ', $value);
                }
                return $directive . ' ' . $value;
            })->implode('; ');

            $headerName = config('performance.http_cache.csp.report_only', false) 
                ? 'Content-Security-Policy-Report-Only' 
                : 'Content-Security-Policy';
            
            $response->headers->set($headerName, $csp);
        }

        return $response;
    }
}
