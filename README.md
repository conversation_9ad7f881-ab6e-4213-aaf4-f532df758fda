# Malombo Selous Forest Camp Website

A production-ready marketing and booking website for Malombo Selous Forest Camp, built with Laravel 12, Tailwind CSS, and modern web technologies.

## Features

- **Modern Architecture**: Laravel 12 with PHP 8.4+ and strict types
- **Authentication**: <PERSON><PERSON> Breeze with role-based permissions (<PERSON><PERSON>, Editor, Viewer)
- **Media Management**: Spatie Media Library for image handling and optimization
- **Interactive Components**: Livewire for dynamic booking forms
- **Responsive Design**: Tailwind CSS with accessibility (WCAG 2.2 AA) compliance
- **Performance**: Optimized for Core Web Vitals and SEO
- **CI/CD**: GitHub Actions for automated testing and deployment

## Tech Stack

- **Backend**: Laravel 12, PHP 8.4+
- **Frontend**: Tailwind CSS, Alpine.js, Livewire
- **Database**: MySQL 8+ (SQLite for local development)
- **Cache**: Redis (Database for local development)
- **File Storage**: AWS S3 (Local for development)
- **Image Processing**: Intervention Image
- **Code Quality**: PHPStan (Level 6), <PERSON><PERSON> Pint, PHPUnit

## Quick Start

### Prerequisites

- PHP 8.4+
- Composer
- Node.js 20+
- MySQL 8+ (for production)

### Installation

1. **Clone and install dependencies:**
   ```bash
   git clone <repository-url>
   cd malombo
   composer install
   npm install
   ```

2. **Environment setup:**
   ```bash
   cp .env.example .env
   php artisan key:generate
   ```

3. **Database setup:**
   ```bash
   php artisan migrate
   php artisan db:seed
   ```

4. **Build assets:**
   ```bash
   npm run build
   ```

5. **Start development server:**
   ```bash
   composer run dev
   ```

## Development Commands

```bash
# Code quality
composer run format          # Format code with Pint
composer run format-check     # Check code formatting
composer run analyse          # Run PHPStan analysis
composer run test            # Run test suite
composer run ide-helper      # Generate IDE helper files

# Development server
composer run dev             # Start all services (server, queue, logs, vite)
```

## User Roles

- **Admin**: Full access to all features
- **Editor**: Content and booking management
- **Viewer**: Read-only access

Default users (after seeding):
- Admin: <EMAIL>
- Editor: <EMAIL>  
- Viewer: <EMAIL>

## Project Structure

```
app/
├── Http/Controllers/        # Application controllers
├── Models/                  # Eloquent models
└── Providers/              # Service providers

config/
├── malombo.php             # Camp-specific configuration
└── ...                     # Laravel configuration files

database/
├── migrations/             # Database migrations
└── seeders/               # Database seeders

resources/
├── css/                   # Tailwind CSS
├── js/                    # Alpine.js components
└── views/                 # Blade templates

tests/                     # PHPUnit tests
```

## Deployment

### Staging
Pushes to `develop` branch automatically deploy to staging environment.

### Production
Pushes to `main` branch automatically deploy to production environment.

### Environment Variables

See `.env.staging.example` and `.env.production.example` for environment-specific configurations.

## Contact Information

**Malombo Selous Forest Camp**
- Website: www.malomboselousforestcamp.com
- Email: <EMAIL>
- Reservations: <EMAIL>
- Phones: +*********** 901, +*********** 104, +*********** 555

## License

This project is proprietary software for Malombo Selous Forest Camp.

## About Laravel

Laravel is a web application framework with expressive, elegant syntax. We believe development must be an enjoyable and creative experience to be truly fulfilling. Laravel takes the pain out of development by easing common tasks used in many web projects, such as:

- [Simple, fast routing engine](https://laravel.com/docs/routing).
- [Powerful dependency injection container](https://laravel.com/docs/container).
- Multiple back-ends for [session](https://laravel.com/docs/session) and [cache](https://laravel.com/docs/cache) storage.
- Expressive, intuitive [database ORM](https://laravel.com/docs/eloquent).
- Database agnostic [schema migrations](https://laravel.com/docs/migrations).
- [Robust background job processing](https://laravel.com/docs/queues).
- [Real-time event broadcasting](https://laravel.com/docs/broadcasting).

Laravel is accessible, powerful, and provides tools required for large, robust applications.

## Learning Laravel

Laravel has the most extensive and thorough [documentation](https://laravel.com/docs) and video tutorial library of all modern web application frameworks, making it a breeze to get started with the framework.

You may also try the [Laravel Bootcamp](https://bootcamp.laravel.com), where you will be guided through building a modern Laravel application from scratch.

If you don't feel like reading, [Laracasts](https://laracasts.com) can help. Laracasts contains thousands of video tutorials on a range of topics including Laravel, modern PHP, unit testing, and JavaScript. Boost your skills by digging into our comprehensive video library.

## Laravel Sponsors

We would like to extend our thanks to the following sponsors for funding Laravel development. If you are interested in becoming a sponsor, please visit the [Laravel Partners program](https://partners.laravel.com).

### Premium Partners

- **[Vehikl](https://vehikl.com)**
- **[Tighten Co.](https://tighten.co)**
- **[Kirschbaum Development Group](https://kirschbaumdevelopment.com)**
- **[64 Robots](https://64robots.com)**
- **[Curotec](https://www.curotec.com/services/technologies/laravel)**
- **[DevSquad](https://devsquad.com/hire-laravel-developers)**
- **[Redberry](https://redberry.international/laravel-development)**
- **[Active Logic](https://activelogic.com)**

## Contributing

Thank you for considering contributing to the Laravel framework! The contribution guide can be found in the [Laravel documentation](https://laravel.com/docs/contributions).

## Code of Conduct

In order to ensure that the Laravel community is welcoming to all, please review and abide by the [Code of Conduct](https://laravel.com/docs/contributions#code-of-conduct).

## Security Vulnerabilities

If you discover a security vulnerability within Laravel, please send an e-mail to Taylor Otwell via [<EMAIL>](mailto:<EMAIL>). All security vulnerabilities will be promptly addressed.

## License

The Laravel framework is open-sourced software licensed under the [MIT license](https://opensource.org/licenses/MIT).
