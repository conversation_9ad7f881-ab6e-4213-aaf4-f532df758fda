<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('rates', function (Blueprint $table) {
            $table->id();
            $table->string('season'); // e.g., "High Season", "Low Season", "Peak Season"
            $table->date('from_date');
            $table->date('to_date');
            $table->decimal('per_person_min', 10, 2); // Minimum rate per person
            $table->decimal('per_person_max', 10, 2); // Maximum rate per person
            $table->text('notes')->nullable(); // Additional pricing notes
            $table->boolean('is_active')->default(true);
            $table->timestamps();
            
            // Indexes
            $table->index(['from_date', 'to_date']);
            $table->index('is_active');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('rates');
    }
};
