<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('activities', function (Blueprint $table) {
            $table->id();
            $table->string('title');
            $table->string('slug')->unique();
            $table->enum('category', [
                'game_drive', 'walk_safari', 'boat_safari', 'village_tour', 
                'cultural_tour', 'fishing', 'night_viewing', 'social_drive'
            ]);
            $table->longText('description');
            $table->text('tips')->nullable();
            $table->string('duration')->nullable(); // e.g., "3-4 hours", "Full day"
            $table->json('includes')->nullable(); // JSON array of what's included
            $table->timestamp('published_at')->nullable();
            
            // SEO Meta fields
            $table->string('meta_title')->nullable();
            $table->text('meta_description')->nullable();
            $table->string('meta_keywords')->nullable();
            $table->string('og_image')->nullable();
            
            $table->timestamps();
            
            // Indexes
            $table->index(['category', 'published_at']);
            $table->index('published_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('activities');
    }
};
