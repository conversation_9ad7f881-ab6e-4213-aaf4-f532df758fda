<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('settings', function (Blueprint $table) {
            $table->id();
            $table->string('key')->unique(); // e.g., 'site_name', 'contact_email'
            $table->text('value')->nullable();
            $table->string('type')->default('text'); // text, email, url, textarea, image, json
            $table->string('group')->nullable(); // general, contact, seo, social, analytics
            $table->string('label');
            $table->text('description')->nullable();
            $table->boolean('is_public')->default(false); // Can be accessed publicly
            $table->integer('sort_order')->default(0);
            $table->timestamps();
            
            // Indexes
            $table->index(['group', 'sort_order']);
            $table->index('is_public');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('settings');
    }
};
