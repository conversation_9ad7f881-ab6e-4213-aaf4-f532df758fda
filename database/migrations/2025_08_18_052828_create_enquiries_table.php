<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('enquiries', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('email');
            $table->string('phone')->nullable();
            $table->integer('adults')->default(2);
            $table->integer('children')->default(0);
            $table->date('check_in')->nullable();
            $table->date('check_out')->nullable();
            $table->string('accommodation_type')->nullable(); // Preferred accommodation type
            $table->longText('message');
            $table->string('source')->default('website'); // website, phone, email, etc.
            $table->enum('status', ['new', 'contacted', 'quoted', 'booked', 'cancelled', 'closed'])->default('new');
            $table->longText('internal_notes')->nullable();
            $table->timestamp('contacted_at')->nullable();
            $table->foreignId('assigned_to')->nullable()->constrained('users')->nullOnDelete();
            $table->timestamps();
            
            // Indexes
            $table->index(['status', 'created_at']);
            $table->index('check_in');
            $table->index('email');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('enquiries');
    }
};
