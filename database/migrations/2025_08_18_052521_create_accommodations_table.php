<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('accommodations', function (Blueprint $table) {
            $table->id();
            $table->string('title');
            $table->string('slug')->unique();
            $table->enum('type', ['banda', 'room', 'tent', 'tree_house', 'inter_leading', 'campsite']);
            $table->text('short_intro');
            $table->longText('description');
            $table->json('features')->nullable(); // JSON array of features
            $table->boolean('wheelchair_access')->default(false);
            $table->boolean('has_balcony')->default(false);
            $table->boolean('has_heated_shower')->default(false);
            $table->boolean('has_mini_bar')->default(false);
            $table->integer('max_occupancy');
            $table->integer('priority')->default(0); // For ordering
            $table->boolean('is_featured')->default(false);
            $table->timestamp('published_at')->nullable();
            
            // SEO Meta fields
            $table->string('meta_title')->nullable();
            $table->text('meta_description')->nullable();
            $table->string('meta_keywords')->nullable();
            $table->string('og_image')->nullable();
            
            $table->timestamps();
            
            // Indexes
            $table->index(['type', 'published_at']);
            $table->index(['is_featured', 'priority']);
            $table->index('published_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('accommodations');
    }
};
