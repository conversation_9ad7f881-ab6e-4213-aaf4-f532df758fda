<?php

declare(strict_types=1);

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Facility;

class FacilitySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $facilities = [
            [
                'key' => 'restaurant',
                'title' => 'Restaurant',
                'description' => 'Our main restaurant serves delicious meals featuring fresh local ingredients and international cuisine. Open for breakfast, lunch, and dinner with indoor and outdoor seating options overlooking the river.',
                'icon' => 'utensils',
                'is_active' => true,
                'sort_order' => 1,
            ],
            [
                'key' => 'bar-lounge',
                'title' => 'Bar & Lounge',
                'description' => 'Relax at our well-stocked bar offering local and international beverages, wines, and cocktails. Perfect for sundowners or evening socializing with comfortable seating and river views.',
                'icon' => 'wine-glass',
                'is_active' => true,
                'sort_order' => 2,
            ],
            [
                'key' => 'wifi-internet',
                'title' => 'Wi-Fi Internet',
                'description' => 'Complimentary wireless internet access available in main areas of the camp. Stay connected with family and friends or share your safari experiences on social media.',
                'icon' => 'wifi',
                'is_active' => true,
                'sort_order' => 3,
            ],
            [
                'key' => 'swimming-pool',
                'title' => 'Swimming Pool',
                'description' => 'Cool off in our swimming pool with stunning views over the Rufiji River. Pool towels and loungers provided for your comfort.',
                'icon' => 'swimming-pool',
                'is_active' => true,
                'sort_order' => 4,
            ],
            [
                'key' => 'spa-wellness',
                'title' => 'Spa & Wellness',
                'description' => 'Rejuvenate with our spa services including traditional massages and wellness treatments. Relax and unwind after exciting safari activities.',
                'icon' => 'spa',
                'is_active' => true,
                'sort_order' => 5,
            ],
            [
                'key' => 'gift-shop',
                'title' => 'Gift Shop',
                'description' => 'Browse our selection of local crafts, souvenirs, and essential items. Perfect for finding unique mementos of your safari experience.',
                'icon' => 'gift',
                'is_active' => true,
                'sort_order' => 6,
            ],
            [
                'key' => 'laundry-service',
                'title' => 'Laundry Service',
                'description' => 'Professional laundry and dry cleaning services available. Keep your safari wardrobe fresh throughout your stay.',
                'icon' => 'tshirt',
                'is_active' => true,
                'sort_order' => 7,
            ],
            [
                'key' => 'airport-transfers',
                'title' => 'Airport Transfers',
                'description' => 'Convenient transfer services to and from nearby airstrips. Our professional drivers ensure safe and comfortable transportation.',
                'icon' => 'plane',
                'is_active' => true,
                'sort_order' => 8,
            ],
            [
                'key' => 'conference-room',
                'title' => 'Conference Room',
                'description' => 'Modern conference facilities for business meetings or special events. Equipped with audiovisual equipment and can accommodate small to medium groups.',
                'icon' => 'presentation',
                'is_active' => true,
                'sort_order' => 9,
            ],
            [
                'key' => 'library',
                'title' => 'Library',
                'description' => 'Quiet reading area with a collection of books about African wildlife, culture, and history. Perfect for learning more about the region.',
                'icon' => 'book',
                'is_active' => true,
                'sort_order' => 10,
            ],
            [
                'key' => 'game-viewing-hide',
                'title' => 'Game Viewing Hide',
                'description' => 'Strategic wildlife viewing hide positioned for optimal animal observation. Quiet space for patient wildlife watchers and photographers.',
                'icon' => 'binoculars',
                'is_active' => true,
                'sort_order' => 11,
            ],
            [
                'key' => 'campfire-area',
                'title' => 'Campfire Area',
                'description' => 'Traditional campfire area for evening gatherings. Share stories of the day\'s adventures under the African stars.',
                'icon' => 'fire',
                'is_active' => true,
                'sort_order' => 12,
            ],
        ];

        foreach ($facilities as $data) {
            Facility::create($data);
        }
    }
}
