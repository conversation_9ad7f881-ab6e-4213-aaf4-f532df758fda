<?php

declare(strict_types=1);

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class RoleAndPermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Reset cached roles and permissions
        app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();

        // Create permissions
        $permissions = [
            // User management
            'view users',
            'create users',
            'edit users',
            'delete users',

            // Booking management
            'view bookings',
            'create bookings',
            'edit bookings',
            'delete bookings',
            'export bookings',

            // Content management
            'view content',
            'create content',
            'edit content',
            'delete content',
            'publish content',

            // Media management
            'view media',
            'upload media',
            'edit media',
            'delete media',

            // System settings
            'view settings',
            'edit settings',

            // Reports
            'view reports',
            'export reports',
        ];

        foreach ($permissions as $permission) {
            Permission::firstOrCreate(['name' => $permission]);
        }

        // Create roles and assign permissions
        $adminRole = Role::firstOrCreate(['name' => 'admin']);
        $adminRole->syncPermissions(Permission::all());

        $editorRole = Role::firstOrCreate(['name' => 'editor']);
        $editorRole->syncPermissions([
            'view users',
            'view bookings',
            'create bookings',
            'edit bookings',
            'export bookings',
            'view content',
            'create content',
            'edit content',
            'publish content',
            'view media',
            'upload media',
            'edit media',
            'view reports',
        ]);

        $viewerRole = Role::firstOrCreate(['name' => 'viewer']);
        $viewerRole->syncPermissions([
            'view users',
            'view bookings',
            'view content',
            'view media',
            'view reports',
        ]);
    }
}
