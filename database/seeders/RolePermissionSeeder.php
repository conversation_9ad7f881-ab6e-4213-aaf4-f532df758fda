<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;
use App\Models\User;

class RolePermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Reset cached roles and permissions
        app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();

        // Create permissions
        $permissions = [
            // Accommodation permissions
            'view accommodations',
            'create accommodations',
            'edit accommodations',
            'delete accommodations',
            'publish accommodations',

            // Activity permissions
            'view activities',
            'create activities',
            'edit activities',
            'delete activities',
            'publish activities',

            // Facility permissions
            'view facilities',
            'create facilities',
            'edit facilities',
            'delete facilities',

            // NearbyPoint permissions
            'view nearby-points',
            'create nearby-points',
            'edit nearby-points',
            'delete nearby-points',

            // Rate permissions
            'view rates',
            'create rates',
            'edit rates',
            'delete rates',

            // Policy permissions
            'view policies',
            'create policies',
            'edit policies',
            'delete policies',

            // Enquiry permissions
            'view enquiries',
            'edit enquiries',
            'delete enquiries',

            // Settings permissions
            'view settings',
            'edit settings',

            // Admin dashboard
            'access admin',
        ];

        foreach ($permissions as $permission) {
            Permission::firstOrCreate(['name' => $permission]);
        }

        // Create roles
        
        // Super Admin - has all permissions
        $superAdminRole = Role::firstOrCreate(['name' => 'super-admin']);
        $superAdminRole->givePermissionTo(Permission::all());

        // Admin - has most permissions except super-admin specific ones
        $adminRole = Role::firstOrCreate(['name' => 'admin']);
        $adminRole->givePermissionTo([
            'view accommodations', 'create accommodations', 'edit accommodations', 'delete accommodations', 'publish accommodations',
            'view activities', 'create activities', 'edit activities', 'delete activities', 'publish activities',
            'view facilities', 'create facilities', 'edit facilities', 'delete facilities',
            'view nearby-points', 'create nearby-points', 'edit nearby-points', 'delete nearby-points',
            'view rates', 'create rates', 'edit rates', 'delete rates',
            'view policies', 'create policies', 'edit policies', 'delete policies',
            'view enquiries', 'edit enquiries',
            'view settings', 'edit settings',
            'access admin',
        ]);

        // Editor - can manage content but not critical settings
        $editorRole = Role::firstOrCreate(['name' => 'editor']);
        $editorRole->givePermissionTo([
            'view accommodations', 'create accommodations', 'edit accommodations',
            'view activities', 'create activities', 'edit activities',
            'view facilities', 'create facilities', 'edit facilities',
            'view nearby-points', 'create nearby-points', 'edit nearby-points',
            'view policies', 'create policies', 'edit policies',
            'view enquiries', 'edit enquiries',
            'access admin',
        ]);

        // Viewer - read-only access
        $viewerRole = Role::firstOrCreate(['name' => 'viewer']);
        $viewerRole->givePermissionTo([
            'view accommodations',
            'view activities',
            'view facilities',
            'view nearby-points',
            'view rates',
            'view policies',
            'view enquiries',
            'view settings',
            'access admin',
        ]);

        // Assign super-admin role to the existing admin user
        $adminUser = User::where('email', '<EMAIL>')->first();
        if ($adminUser) {
            $adminUser->assignRole('super-admin');
        }
    }
}
