<?php

declare(strict_types=1);

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Setting;

class SettingSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $settings = [
            // General Settings
            [
                'key' => 'site_name',
                'value' => 'Malombo Selous Forest Camp',
                'type' => 'text',
                'group' => 'general',
                'label' => 'Site Name',
                'description' => 'The name of your website',
                'is_public' => true,
                'sort_order' => 1,
            ],
            [
                'key' => 'site_tagline',
                'value' => 'Experience the Wild Heart of Tanzania',
                'type' => 'text',
                'group' => 'general',
                'label' => 'Site Tagline',
                'description' => 'A short description or tagline for your site',
                'is_public' => true,
                'sort_order' => 2,
            ],
            [
                'key' => 'site_logo',
                'value' => null,
                'type' => 'image',
                'group' => 'general',
                'label' => 'Site Logo',
                'description' => 'Upload your site logo',
                'is_public' => true,
                'sort_order' => 3,
            ],
            [
                'key' => 'site_favicon',
                'value' => null,
                'type' => 'image',
                'group' => 'general',
                'label' => 'Site Favicon',
                'description' => 'Upload your site favicon (16x16 or 32x32 pixels)',
                'is_public' => true,
                'sort_order' => 4,
            ],

            // Contact Information
            [
                'key' => 'contact_email',
                'value' => '<EMAIL>',
                'type' => 'email',
                'group' => 'contact',
                'label' => 'Primary Email',
                'description' => 'Main contact email address',
                'is_public' => true,
                'sort_order' => 1,
            ],
            [
                'key' => 'contact_phone',
                'value' => '+255 123 456 789',
                'type' => 'text',
                'group' => 'contact',
                'label' => 'Primary Phone',
                'description' => 'Main contact phone number',
                'is_public' => true,
                'sort_order' => 2,
            ],
            [
                'key' => 'contact_phone_secondary',
                'value' => '+255 987 654 321',
                'type' => 'text',
                'group' => 'contact',
                'label' => 'Secondary Phone',
                'description' => 'Secondary contact phone number',
                'is_public' => true,
                'sort_order' => 3,
            ],
            [
                'key' => 'contact_address',
                'value' => 'Selous Game Reserve, Tanzania',
                'type' => 'textarea',
                'group' => 'contact',
                'label' => 'Address',
                'description' => 'Physical address of the camp',
                'is_public' => true,
                'sort_order' => 4,
            ],
            [
                'key' => 'contact_coordinates',
                'value' => json_encode(['lat' => -8.0000, 'lng' => 36.5000]),
                'type' => 'json',
                'group' => 'contact',
                'label' => 'GPS Coordinates',
                'description' => 'Latitude and longitude coordinates',
                'is_public' => true,
                'sort_order' => 5,
            ],

            // Social Media
            [
                'key' => 'social_facebook',
                'value' => null,
                'type' => 'url',
                'group' => 'social',
                'label' => 'Facebook URL',
                'description' => 'Facebook page URL',
                'is_public' => true,
                'sort_order' => 1,
            ],
            [
                'key' => 'social_instagram',
                'value' => null,
                'type' => 'url',
                'group' => 'social',
                'label' => 'Instagram URL',
                'description' => 'Instagram profile URL',
                'is_public' => true,
                'sort_order' => 2,
            ],
            [
                'key' => 'social_twitter',
                'value' => null,
                'type' => 'url',
                'group' => 'social',
                'label' => 'Twitter URL',
                'description' => 'Twitter profile URL',
                'is_public' => true,
                'sort_order' => 3,
            ],
            [
                'key' => 'social_youtube',
                'value' => null,
                'type' => 'url',
                'group' => 'social',
                'label' => 'YouTube URL',
                'description' => 'YouTube channel URL',
                'is_public' => true,
                'sort_order' => 4,
            ],
            [
                'key' => 'social_tripadvisor',
                'value' => null,
                'type' => 'url',
                'group' => 'social',
                'label' => 'TripAdvisor URL',
                'description' => 'TripAdvisor listing URL',
                'is_public' => true,
                'sort_order' => 5,
            ],

            // SEO Settings
            [
                'key' => 'seo_meta_title',
                'value' => 'Malombo Selous Forest Camp - Authentic Safari Experience in Tanzania',
                'type' => 'text',
                'group' => 'seo',
                'label' => 'Default Meta Title',
                'description' => 'Default title tag for SEO (50-60 characters)',
                'is_public' => true,
                'sort_order' => 1,
            ],
            [
                'key' => 'seo_meta_description',
                'value' => 'Experience authentic African safari at Malombo Selous Forest Camp. Luxury accommodation, guided game drives, and unforgettable wildlife encounters in Tanzania\'s largest game reserve.',
                'type' => 'textarea',
                'group' => 'seo',
                'label' => 'Default Meta Description',
                'description' => 'Default meta description for SEO (150-160 characters)',
                'is_public' => true,
                'sort_order' => 2,
            ],
            [
                'key' => 'seo_keywords',
                'value' => 'safari, Tanzania, Selous, wildlife, luxury camp, game drives, accommodation',
                'type' => 'text',
                'group' => 'seo',
                'label' => 'Default Keywords',
                'description' => 'Default keywords for SEO (comma-separated)',
                'is_public' => true,
                'sort_order' => 3,
            ],
            [
                'key' => 'seo_og_image',
                'value' => null,
                'type' => 'image',
                'group' => 'seo',
                'label' => 'Default OG Image',
                'description' => 'Default Open Graph image for social sharing (1200x630px)',
                'is_public' => true,
                'sort_order' => 4,
            ],

            // Analytics
            [
                'key' => 'analytics_google_id',
                'value' => null,
                'type' => 'text',
                'group' => 'analytics',
                'label' => 'Google Analytics ID',
                'description' => 'Google Analytics tracking ID (GA4)',
                'is_public' => false,
                'sort_order' => 1,
            ],
            [
                'key' => 'analytics_facebook_pixel',
                'value' => null,
                'type' => 'text',
                'group' => 'analytics',
                'label' => 'Facebook Pixel ID',
                'description' => 'Facebook Pixel ID for tracking',
                'is_public' => false,
                'sort_order' => 2,
            ],
            [
                'key' => 'analytics_google_tag_manager',
                'value' => null,
                'type' => 'text',
                'group' => 'analytics',
                'label' => 'Google Tag Manager ID',
                'description' => 'Google Tag Manager container ID',
                'is_public' => false,
                'sort_order' => 3,
            ],

            // Booking Settings
            [
                'key' => 'booking_email',
                'value' => '<EMAIL>',
                'type' => 'email',
                'group' => 'booking',
                'label' => 'Booking Email',
                'description' => 'Email address for booking inquiries',
                'is_public' => true,
                'sort_order' => 1,
            ],
            [
                'key' => 'booking_minimum_nights',
                'value' => '2',
                'type' => 'integer',
                'group' => 'booking',
                'label' => 'Minimum Nights',
                'description' => 'Minimum number of nights for bookings',
                'is_public' => true,
                'sort_order' => 2,
            ],
            [
                'key' => 'booking_advance_days',
                'value' => '30',
                'type' => 'integer',
                'group' => 'booking',
                'label' => 'Advance Booking Days',
                'description' => 'Minimum days in advance for bookings',
                'is_public' => true,
                'sort_order' => 3,
            ],
        ];

        foreach ($settings as $data) {
            Setting::firstOrCreate(
                ['key' => $data['key']], 
                $data
            );
        }
    }
}
