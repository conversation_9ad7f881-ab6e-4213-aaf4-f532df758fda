<?php

declare(strict_types=1);

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Accommodation;
use Illuminate\Support\Str;

class AccommodationSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $accommodations = [
            [
                'title' => 'Luxury Banda',
                'slug' => 'luxury-banda',
                'type' => 'banda',
                'short_intro' => 'Traditional African architecture meets modern luxury in our spacious bandas.',
                'description' => 'Our luxury bandas offer the perfect blend of traditional African architecture and modern comfort. Each banda features a thatched roof, private veranda, and panoramic views of the Selous wilderness. Built with local materials and designed to harmonize with the natural environment, these accommodations provide an authentic safari experience without compromising on luxury.',
                'features' => [
                    'Private veranda with wilderness views',
                    'Traditional thatched roof design',
                    'En-suite bathroom with hot water',
                    'Mosquito netting',
                    'Solar power lighting',
                    'Comfortable bedding',
                    'Writing desk',
                    'Wardrobe storage'
                ],
                'wheelchair_access' => false,
                'has_balcony' => true,
                'has_heated_shower' => true,
                'has_mini_bar' => false,
                'max_occupancy' => 2,
                'priority' => 10,
                'is_featured' => true,
                'published_at' => now(),
            ],
            [
                'title' => 'Safari Room',
                'slug' => 'safari-room',
                'type' => 'room',
                'short_intro' => 'Comfortable safari rooms with modern amenities and forest views.',
                'description' => 'Our safari rooms provide comfortable accommodation with all essential amenities. Each room is thoughtfully designed with large windows to maximize natural light and offer beautiful views of the surrounding forest. Perfect for travelers seeking comfort and convenience in the heart of the wilderness.',
                'features' => [
                    'Large windows with forest views',
                    'En-suite bathroom',
                    'Hot water shower',
                    'Ceiling fan',
                    'Reading lights',
                    'Mosquito netting',
                    'Storage space',
                    'Private entrance'
                ],
                'wheelchair_access' => true,
                'has_balcony' => false,
                'has_heated_shower' => true,
                'has_mini_bar' => false,
                'max_occupancy' => 2,
                'priority' => 8,
                'is_featured' => false,
                'published_at' => now(),
            ],
            [
                'title' => 'Safari Tent',
                'slug' => 'safari-tent',
                'type' => 'tent',
                'short_intro' => 'Authentic canvas tents offering a genuine safari camping experience.',
                'description' => 'Experience the authentic safari atmosphere in our comfortable canvas tents. Each tent is equipped with proper beds, lighting, and basic amenities while maintaining the adventurous spirit of traditional safari camping. Perfect for those seeking a more rustic yet comfortable accommodation option.',
                'features' => [
                    'Canvas construction',
                    'Proper beds with mattresses',
                    'Solar lighting',
                    'Shared bathroom facilities',
                    'Mosquito netting',
                    'Storage pockets',
                    'Ventilation windows',
                    'Ground sheets'
                ],
                'wheelchair_access' => false,
                'has_balcony' => false,
                'has_heated_shower' => false,
                'has_mini_bar' => false,
                'max_occupancy' => 2,
                'priority' => 6,
                'is_featured' => false,
                'published_at' => now(),
            ],
            [
                'title' => 'Tree House Suite',
                'slug' => 'tree-house-suite',
                'type' => 'tree_house',
                'short_intro' => 'Elevated luxury accommodation with panoramic canopy views.',
                'description' => 'Our unique tree house suite offers an elevated safari experience, literally and figuratively. Built among the trees with minimal environmental impact, this accommodation provides unparalleled views of the forest canopy and wildlife corridors below. The ultimate in eco-luxury accommodation.',
                'features' => [
                    'Elevated position in the trees',
                    'Panoramic canopy views',
                    'Private deck with seating',
                    'En-suite bathroom',
                    'Hot water shower',
                    'Mosquito screening',
                    'Solar power',
                    'Wildlife viewing opportunities',
                    'Spiral staircase access'
                ],
                'wheelchair_access' => false,
                'has_balcony' => true,
                'has_heated_shower' => true,
                'has_mini_bar' => true,
                'max_occupancy' => 2,
                'priority' => 15,
                'is_featured' => true,
                'published_at' => now(),
            ],
            [
                'title' => 'Inter-leading Family Room',
                'slug' => 'inter-leading-family-room',
                'type' => 'inter_leading',
                'short_intro' => 'Connected rooms perfect for families and groups traveling together.',
                'description' => 'Our inter-leading rooms are specially designed for families and groups who want to stay close while maintaining privacy. These connected accommodations feature a shared entrance and common area, making them perfect for parents with children or groups of friends traveling together.',
                'features' => [
                    'Two connected rooms',
                    'Shared common area',
                    'Family-friendly layout',
                    'En-suite bathrooms in each room',
                    'Hot water showers',
                    'Mosquito netting',
                    'Ample storage space',
                    'Child-safe features',
                    'Private entrance'
                ],
                'wheelchair_access' => true,
                'has_balcony' => false,
                'has_heated_shower' => true,
                'has_mini_bar' => false,
                'max_occupancy' => 6,
                'priority' => 7,
                'is_featured' => false,
                'published_at' => now(),
            ],
            [
                'title' => 'Campsite',
                'slug' => 'campsite',
                'type' => 'campsite',
                'short_intro' => 'Designated camping areas for those bringing their own tents.',
                'description' => 'For the adventurous traveler who prefers to bring their own camping equipment, we offer designated campsites with basic facilities. Each site includes access to shared bathroom and kitchen facilities, making it an affordable option for backpackers and camping enthusiasts.',
                'features' => [
                    'Designated camping area',
                    'Shared bathroom facilities',
                    'Shared kitchen access',
                    'Fire pit area',
                    'Potable water access',
                    'Waste disposal',
                    'Security patrol',
                    'Vehicle parking'
                ],
                'wheelchair_access' => false,
                'has_balcony' => false,
                'has_heated_shower' => false,
                'has_mini_bar' => false,
                'max_occupancy' => 4,
                'priority' => 3,
                'is_featured' => false,
                'published_at' => now(),
            ],
        ];

        foreach ($accommodations as $data) {
            Accommodation::create($data);
        }
    }
}
