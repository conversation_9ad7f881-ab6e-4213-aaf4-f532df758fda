<?php

declare(strict_types=1);

namespace Database\Seeders;

use App\Models\User;
// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // Seed roles and permissions first
        $this->call(RoleAndPermissionSeeder::class);

        // Create admin user
        $admin = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            ['name' => 'Admin User']
        );
        $admin->assignRole('admin');

        // Create editor user
        $editor = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            ['name' => 'Editor User']
        );
        $editor->assignRole('editor');

        // Create viewer user
        $viewer = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            ['name' => 'Viewer User']
        );
        $viewer->assignRole('viewer');

        // Seed domain data
        $this->call([
            AccommodationSeeder::class,
            ActivitySeeder::class,
            FacilitySeeder::class,
            NearbyPointSeeder::class,
            RateSeeder::class,
            PolicySeeder::class,
            EnquirySeeder::class,
            SettingSeeder::class,
        ]);
    }
}
