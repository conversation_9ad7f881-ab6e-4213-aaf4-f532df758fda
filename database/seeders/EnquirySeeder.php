<?php

declare(strict_types=1);

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Enquiry;

class EnquirySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $enquiries = [
            [
                'name' => '<PERSON>',
                'email' => '<EMAIL>',
                'phone' => '******-123-4567',
                'accommodation_type' => 'banda',
                'check_in' => '2024-07-15',
                'check_out' => '2024-07-20',
                'adults' => 2,
                'children' => 0,
                'message' => 'We are interested in a 5-day luxury safari experience. This will be our first time in Tanzania and we would like to see the Big Five if possible. We prefer the luxury banda accommodation and are particularly interested in boat safaris on the Rufiji River.',
                'status' => 'new',
                'source' => 'website',
                'created_at' => now()->subDays(5),
                'updated_at' => now()->subDays(5),
            ],
            [
                'name' => '<PERSON>',
                'email' => '<EMAIL>',
                'phone' => '+44-20-7123-4567',
                'accommodation_type' => 'room',
                'check_in' => '2024-08-10',
                'check_out' => '2024-08-17',
                'adults' => 2,
                'children' => 2,
                'message' => 'Family safari for 7 nights. Children ages 8 and 10. Interested in cultural village tours and educational activities for the kids. Need to know about child-friendly activities and safety measures.',
                'status' => 'contacted',
                'source' => 'email',
                'internal_notes' => 'Sent detailed family safari proposal with child rates and activity recommendations.',
                'contacted_at' => now()->subDays(2),
                'created_at' => now()->subDays(8),
                'updated_at' => now()->subDays(2),
            ],
            [
                'name' => 'Hans Mueller',
                'email' => '<EMAIL>',
                'phone' => '+49-30-12345678',
                'accommodation_type' => 'tent',
                'check_in' => '2024-09-05',
                'check_out' => '2024-09-12',
                'adults' => 1,
                'children' => 0,
                'message' => 'Solo traveler looking for photography safari. Interested in early morning and late afternoon game drives for optimal lighting. Need information about photographic hides and equipment storage.',
                'status' => 'booked',
                'source' => 'phone',
                'internal_notes' => 'Booked photography package with extended game drives and hide access.',
                'contacted_at' => now()->subDays(10),
                'created_at' => now()->subDays(15),
                'updated_at' => now()->subDays(1),
            ],
            [
                'name' => 'Sarah Williams',
                'email' => '<EMAIL>',
                'phone' => '+61-2-9876-5432',
                'accommodation_type' => 'tree_house',
                'check_in' => '2024-06-20',
                'check_out' => '2024-06-25',
                'adults' => 2,
                'children' => 0,
                'message' => 'Honeymoon safari - looking for romantic and unique accommodation. Very interested in the tree house option. Would like private dinners and couples activities. Budget is flexible for special experiences.',
                'status' => 'quoted',
                'source' => 'website',
                'internal_notes' => 'Sent honeymoon package quote with tree house accommodation and romantic extras.',
                'contacted_at' => now()->subDays(1),
                'created_at' => now()->subDays(3),
                'updated_at' => now()->subDays(1),
            ],
            [
                'name' => 'Pierre Dubois',
                'email' => '<EMAIL>',
                'phone' => '+33-1-23-45-67-89',
                'accommodation_type' => 'campsite',
                'check_in' => '2024-11-15',
                'check_out' => '2024-11-22',
                'adults' => 4,
                'children' => 0,
                'message' => 'Group of friends (4 people) looking for budget accommodation. We have our own camping equipment. Interested in walking safaris and cultural experiences. Need information about group discounts.',
                'status' => 'cancelled',
                'source' => 'phone',
                'internal_notes' => 'Client cancelled due to change in travel plans.',
                'contacted_at' => now()->subDays(7),
                'created_at' => now()->subDays(20),
                'updated_at' => now()->subDays(7),
            ],
            [
                'name' => 'Maria Santos',
                'email' => '<EMAIL>',
                'phone' => '+55-11-98765-4321',
                'accommodation_type' => 'inter_leading',
                'check_in' => '2024-10-01',
                'check_out' => '2024-10-08',
                'adults' => 3,
                'children' => 1,
                'message' => 'Extended family trip - grandparents and grandchild (age 12). Need inter-leading rooms for easy access. Grandmother has mobility issues, need accessible facilities and gentle activities.',
                'status' => 'contacted',
                'source' => 'website',
                'internal_notes' => 'Sent accessibility information and gentle activity options. Awaiting confirmation.',
                'contacted_at' => now()->subDays(1),
                'created_at' => now()->subDays(4),
                'updated_at' => now()->subDays(1),
            ],
        ];

        foreach ($enquiries as $data) {
            Enquiry::create($data);
        }
    }
}
