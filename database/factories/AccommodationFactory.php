<?php

namespace Database\Factories;

use App\Models\Accommodation;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Accommodation>
 */
class AccommodationFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $title = $this->faker->words(3, true);
        $types = ['banda', 'room', 'tent', 'tree_house', 'inter_leading', 'campsite'];
        
        return [
            'title' => ucwords($title),
            'slug' => Str::slug($title),
            'type' => $this->faker->randomElement($types),
            'short_intro' => $this->faker->sentence(10),
            'description' => $this->faker->paragraphs(3, true),
            'features' => json_encode($this->faker->randomElements(
                ['Air Conditioning', 'WiFi', 'Mini Bar', 'Balcony', 'View', 'Safe Box'],
                $this->faker->numberBetween(2, 4)
            )),
            'wheelchair_access' => $this->faker->boolean(30),
            'has_balcony' => $this->faker->boolean(60),
            'has_heated_shower' => $this->faker->boolean(80),
            'has_mini_bar' => $this->faker->boolean(70),
            'max_occupancy' => $this->faker->numberBetween(1, 6),
            'priority' => $this->faker->numberBetween(0, 100),
            'is_featured' => $this->faker->boolean(30),
            'published_at' => $this->faker->optional(0.9)->dateTimeBetween('-1 year', 'now'),
            'meta_title' => $this->faker->optional()->sentence(6),
            'meta_description' => $this->faker->optional()->sentence(15),
            'meta_keywords' => $this->faker->optional()->words(5, true),
        ];
    }

    /**
     * Indicate that the accommodation is featured.
     */
    public function featured(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_featured' => true,
            'priority' => $this->faker->numberBetween(80, 100),
        ]);
    }

    /**
     * Indicate that the accommodation is published.
     */
    public function published(): static
    {
        return $this->state(fn (array $attributes) => [
            'published_at' => $this->faker->dateTimeBetween('-6 months', 'now'),
        ]);
    }

    /**
     * Indicate that the accommodation is unpublished.
     */
    public function unpublished(): static
    {
        return $this->state(fn (array $attributes) => [
            'published_at' => null,
        ]);
    }
}
