<?php

namespace Database\Factories;

use App\Models\Activity;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Activity>
 */
class ActivityFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $title = $this->faker->words(2, true);
        $categories = ['wildlife', 'adventure', 'cultural', 'relaxation', 'dining', 'educational'];
        
        return [
            'title' => ucwords($title),
            'slug' => Str::slug($title),
            'category' => $this->faker->randomElement($categories),
            'short_intro' => $this->faker->sentence(8),
            'description' => $this->faker->paragraphs(2, true),
            'duration' => $this->faker->randomElement(['30 minutes', '1 hour', '2 hours', '3 hours', 'Half day', 'Full day']),
            'difficulty_level' => $this->faker->randomElement(['easy', 'moderate', 'challenging']),
            'min_age' => $this->faker->numberBetween(0, 12),
            'max_participants' => $this->faker->numberBetween(4, 20),
            'price_adult' => $this->faker->numberBetween(20, 150),
            'price_child' => $this->faker->numberBetween(10, 75),
            'includes' => json_encode($this->faker->randomElements(
                ['Guide', 'Equipment', 'Refreshments', 'Transport', 'Insurance'],
                $this->faker->numberBetween(2, 4)
            )),
            'requirements' => $this->faker->optional()->sentence(),
            'priority' => $this->faker->numberBetween(0, 100),
            'is_featured' => $this->faker->boolean(25),
            'published_at' => $this->faker->optional(0.9)->dateTimeBetween('-1 year', 'now'),
            'meta_title' => $this->faker->optional()->sentence(6),
            'meta_description' => $this->faker->optional()->sentence(15),
        ];
    }

    /**
     * Indicate that the activity is featured.
     */
    public function featured(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_featured' => true,
            'priority' => $this->faker->numberBetween(80, 100),
        ]);
    }

    /**
     * Indicate that the activity is published.
     */
    public function published(): static
    {
        return $this->state(fn (array $attributes) => [
            'published_at' => $this->faker->dateTimeBetween('-6 months', 'now'),
        ]);
    }
}
