<?php

namespace Database\Factories;

use App\Models\Facility;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Facility>
 */
class FacilityFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $title = $this->faker->words(2, true);
        $categories = ['dining', 'recreation', 'wellness', 'business', 'convenience', 'outdoor'];
        
        return [
            'title' => ucwords($title),
            'slug' => Str::slug($title),
            'category' => $this->faker->randomElement($categories),
            'short_intro' => $this->faker->sentence(8),
            'description' => $this->faker->paragraphs(2, true),
            'operating_hours' => $this->faker->randomElement([
                '24/7',
                '06:00 - 22:00',
                '08:00 - 18:00',
                '09:00 - 17:00',
                'By appointment'
            ]),
            'location' => $this->faker->randomElement([
                'Main Lodge',
                'Pool Area',
                'Garden',
                'Reception',
                'Restaurant',
                'Spa Building'
            ]),
            'capacity' => $this->faker->optional()->numberBetween(10, 100),
            'is_complimentary' => $this->faker->boolean(70),
            'requires_booking' => $this->faker->boolean(40),
            'priority' => $this->faker->numberBetween(0, 100),
            'is_featured' => $this->faker->boolean(30),
            'published_at' => $this->faker->optional(0.9)->dateTimeBetween('-1 year', 'now'),
            'meta_title' => $this->faker->optional()->sentence(6),
            'meta_description' => $this->faker->optional()->sentence(15),
        ];
    }

    /**
     * Indicate that the facility is featured.
     */
    public function featured(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_featured' => true,
            'priority' => $this->faker->numberBetween(80, 100),
        ]);
    }

    /**
     * Indicate that the facility is published.
     */
    public function published(): static
    {
        return $this->state(fn (array $attributes) => [
            'published_at' => $this->faker->dateTimeBetween('-6 months', 'now'),
        ]);
    }
}
