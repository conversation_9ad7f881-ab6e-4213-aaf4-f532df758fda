# Production Environment Configuration
APP_NAME="Malombo Selous Forest Camp"
APP_ENV=production
APP_KEY=
APP_DEBUG=false
APP_URL=https://www.malomboselousforestcamp.com

# Database Configuration (MySQL 8+)
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=malombo_camp_production
DB_USERNAME=production_user
DB_PASSWORD=

# Cache & Queue (Redis)
CACHE_STORE=redis
QUEUE_CONNECTION=redis

# Mail Configuration (SMTP)
MAIL_MAILER=smtp
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=
MAIL_PASSWORD=
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

# File Storage (S3)
FILESYSTEM_DISK=s3
AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=malombo-camp-production

# Logging
LOG_CHANNEL=stack
LOG_LEVEL=error

# Session
SESSION_DRIVER=redis
SESSION_ENCRYPT=true

# Analytics
GOOGLE_ANALYTICS_ID=
GOOGLE_TAG_MANAGER_ID=
