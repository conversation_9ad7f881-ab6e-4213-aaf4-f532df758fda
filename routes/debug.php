<?php

use App\Services\NavigationService;
use Illuminate\Support\Facades\Route;

Route::get('/debug/navigation', function () {
    $navigation = config('sitemap.main_navigation', []);
    $routes = config('sitemap.public_routes', []);
    
    return response()->json([
        'navigation_config' => $navigation,
        'routes_config' => array_keys($routes),
        'navigation_data' => NavigationService::getMainNavigation()->toArray(),
    ]);
});
