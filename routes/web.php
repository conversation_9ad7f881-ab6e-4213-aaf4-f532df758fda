<?php

declare(strict_types=1);

use App\Http\Controllers\Admin\AccommodationController as AdminAccommodationController;
use App\Http\Controllers\Admin\ActivityController as AdminActivityController;
use App\Http\Controllers\Admin\BlogController as AdminBlogController;
use App\Http\Controllers\Admin\DashboardController;
use App\Http\Controllers\Admin\EnquiryController;
use App\Http\Controllers\Admin\FacilityController as AdminFacilityController;
use App\Http\Controllers\Admin\FaqController;
use App\Http\Controllers\Admin\MediaController;
use App\Http\Controllers\Admin\PageController as AdminPageController;
use App\Http\Controllers\Admin\PolicyController;
use App\Http\Controllers\Admin\RateController as AdminRateController;
use App\Http\Controllers\Admin\SettingController;
use App\Http\Controllers\ProfileController;
use App\Http\Controllers\Public\AccommodationController;
use App\Http\Controllers\Public\ActivityController;
use App\Http\Controllers\Public\BlogController;
use App\Http\Controllers\Public\BookingController;
use App\Http\Controllers\Public\ContactController;
use App\Http\Controllers\Public\FacilityController;
use App\Http\Controllers\Public\GalleryController;
use App\Http\Controllers\Public\HomeController;
use App\Http\Controllers\Public\LocationController;
use App\Http\Controllers\Public\LodgeController;
use App\Http\Controllers\Public\RateController;
use App\Http\Controllers\System\ErrorController;
use App\Http\Controllers\System\LegalController;
use App\Http\Controllers\SitemapController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Public Routes
|--------------------------------------------------------------------------
|
| These routes are accessible to all visitors without authentication.
| They represent the main marketing website for Malombo Selous Forest Camp.
|
*/

// Home
Route::get('/', [HomeController::class, 'index'])->name('home');

// Booking Enquiry
Route::prefix('booking')->name('booking.')->group(function () {
    Route::get('/', [BookingController::class, 'index'])->name('index');
    Route::get('/success/{reference?}', [BookingController::class, 'success'])->name('success');
});

// The Lodge (About + Experience)
Route::prefix('lodge')->name('lodge.')->group(function () {
    Route::get('/', [LodgeController::class, 'index'])->name('index');
    Route::get('/about', [LodgeController::class, 'about'])->name('about');
    Route::get('/experience', [LodgeController::class, 'experience'])->name('experience');
    Route::get('/history', [LodgeController::class, 'history'])->name('history');
});

// Accommodation
Route::prefix('accommodation')->name('accommodation.')->group(function () {
    Route::get('/', [AccommodationController::class, 'index'])->name('index');
    Route::get('/{slug}', [AccommodationController::class, 'show'])->name('show');
});

// Activities
Route::prefix('activities')->name('activities.')->group(function () {
    Route::get('/', [ActivityController::class, 'index'])->name('index');
    Route::get('/{slug}', [ActivityController::class, 'show'])->name('show');
});

// Facilities & Amenities
Route::prefix('facilities')->name('facilities.')->group(function () {
    Route::get('/', [FacilityController::class, 'index'])->name('index');
    Route::get('/{slug}', [FacilityController::class, 'show'])->name('show');
});

// Rates & Policies
Route::prefix('rates')->name('rates.')->group(function () {
    Route::get('/', [RateController::class, 'index'])->name('index');
    Route::get('/policies', [RateController::class, 'policies'])->name('policies');
    Route::get('/seasons', [RateController::class, 'seasons'])->name('seasons');
});

// Gallery
Route::prefix('gallery')->name('gallery.')->group(function () {
    Route::get('/', [GalleryController::class, 'index'])->name('index');
    Route::get('/{category}', [GalleryController::class, 'category'])->name('category');
});

// Location & Getting Here
Route::prefix('location')->name('location.')->group(function () {
    Route::get('/', [LocationController::class, 'index'])->name('index');
    Route::get('/getting-here', [LocationController::class, 'gettingHere'])->name('getting-here');
    Route::get('/nearby', [LocationController::class, 'nearby'])->name('nearby');
});

// Contact & Reservations
Route::prefix('contact')->name('contact.')->group(function () {
    Route::get('/', [ContactController::class, 'index'])->name('index');
    Route::post('/enquiry', [ContactController::class, 'submitEnquiry'])->name('submitEnquiry');
    Route::get('/reservations', [ContactController::class, 'reservations'])->name('reservations');
    Route::post('/reservations', [ContactController::class, 'submitReservation'])->name('submitReservation');
});

// Blog/News
Route::prefix('blog')->name('blog.')->group(function () {
    Route::get('/', [BlogController::class, 'index'])->name('index');
    Route::get('/{slug}', [BlogController::class, 'show'])->name('show');
    Route::get('/category/{slug}', [BlogController::class, 'category'])->name('category');
    Route::get('/tag/{slug}', [BlogController::class, 'tag'])->name('tag');
});

/*
|--------------------------------------------------------------------------
| System Routes
|--------------------------------------------------------------------------
|
| Error pages, legal pages, and other system-level routes.
|
*/

// Error Pages
Route::prefix('error')->name('error.')->group(function () {
    Route::get('/404', [ErrorController::class, 'notFound'])->name('404');
    Route::get('/500', [ErrorController::class, 'serverError'])->name('500');
    Route::get('/503', [ErrorController::class, 'maintenance'])->name('503');
});

// Legal Pages
Route::prefix('legal')->name('legal.')->group(function () {
    Route::get('/privacy', [LegalController::class, 'privacy'])->name('privacy');
    Route::get('/cookies', [LegalController::class, 'cookies'])->name('cookies');
    Route::get('/terms', [LegalController::class, 'terms'])->name('terms');
    Route::get('/disclaimer', [LegalController::class, 'disclaimer'])->name('disclaimer');
});

// SEO & System Routes
Route::get('/sitemap.xml', [SitemapController::class, 'index'])->name('sitemap.xml');
Route::get('/robots.txt', [SitemapController::class, 'robots'])->name('robots.txt');

/*
|--------------------------------------------------------------------------
| Authentication Routes
|--------------------------------------------------------------------------
|
| Routes for user authentication (login, register, password reset, etc.)
|
*/

// Legacy dashboard route for compatibility with Breeze
Route::get('/dashboard', function () {
    return redirect()->route('admin.dashboard');
})->middleware(['auth', 'verified'])->name('dashboard');

Route::middleware('auth')->group(function () {
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');
});

require __DIR__.'/auth.php';

// Include debug routes (for development only)
if (app()->environment('local', 'testing')) {
    require __DIR__.'/debug.php';
}

/*
|--------------------------------------------------------------------------
| Admin Routes (CMS)
|--------------------------------------------------------------------------
|
| Protected admin routes for content management system.
| Requires authentication and appropriate permissions.
|
*/

Route::middleware(['auth', 'verified'])->prefix('admin')->name('admin.')->group(function () {

    // Dashboard
    Route::get('/', [DashboardController::class, 'index'])->name('dashboard');
    Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard.index');

    // Pages Management
    Route::prefix('pages')->name('pages.')->middleware('can:view content')->group(function () {
        Route::get('/', [AdminPageController::class, 'index'])->name('index');
        Route::get('/create', [AdminPageController::class, 'create'])->name('create')->middleware('can:create content');
        Route::post('/', [AdminPageController::class, 'store'])->name('store')->middleware('can:create content');
        Route::get('/{page}', [AdminPageController::class, 'show'])->name('show');
        Route::get('/{page}/edit', [AdminPageController::class, 'edit'])->name('edit')->middleware('can:edit content');
        Route::patch('/{page}', [AdminPageController::class, 'update'])->name('update')->middleware('can:edit content');
        Route::delete('/{page}', [AdminPageController::class, 'destroy'])->name('destroy')->middleware('can:delete content');
        Route::patch('/{page}/publish', [AdminPageController::class, 'publish'])->name('publish')->middleware('can:publish content');
    });

    // Accommodations Management
    Route::prefix('accommodations')->name('accommodations.')->middleware('can:view content')->group(function () {
        Route::get('/', [AdminAccommodationController::class, 'index'])->name('index');
        Route::get('/create', [AdminAccommodationController::class, 'create'])->name('create')->middleware('can:create content');
        Route::post('/', [AdminAccommodationController::class, 'store'])->name('store')->middleware('can:create content');
        Route::get('/{accommodation}', [AdminAccommodationController::class, 'show'])->name('show');
        Route::get('/{accommodation}/edit', [AdminAccommodationController::class, 'edit'])->name('edit')->middleware('can:edit content');
        Route::patch('/{accommodation}', [AdminAccommodationController::class, 'update'])->name('update')->middleware('can:edit content');
        Route::delete('/{accommodation}', [AdminAccommodationController::class, 'destroy'])->name('destroy')->middleware('can:delete content');
    });

    // Activities Management
    Route::prefix('activities')->name('activities.')->middleware('can:view content')->group(function () {
        Route::get('/', [AdminActivityController::class, 'index'])->name('index');
        Route::get('/create', [AdminActivityController::class, 'create'])->name('create')->middleware('can:create content');
        Route::post('/', [AdminActivityController::class, 'store'])->name('store')->middleware('can:create content');
        Route::get('/{activity}', [AdminActivityController::class, 'show'])->name('show');
        Route::get('/{activity}/edit', [AdminActivityController::class, 'edit'])->name('edit')->middleware('can:edit content');
        Route::patch('/{activity}', [AdminActivityController::class, 'update'])->name('update')->middleware('can:edit content');
        Route::delete('/{activity}', [AdminActivityController::class, 'destroy'])->name('destroy')->middleware('can:delete content');
    });

    // Facilities Management
    Route::prefix('facilities')->name('facilities.')->middleware('can:view content')->group(function () {
        Route::get('/', [AdminFacilityController::class, 'index'])->name('index');
        Route::get('/create', [AdminFacilityController::class, 'create'])->name('create')->middleware('can:create content');
        Route::post('/', [AdminFacilityController::class, 'store'])->name('store')->middleware('can:create content');
        Route::get('/{facility}', [AdminFacilityController::class, 'show'])->name('show');
        Route::get('/{facility}/edit', [AdminFacilityController::class, 'edit'])->name('edit')->middleware('can:edit content');
        Route::patch('/{facility}', [AdminFacilityController::class, 'update'])->name('update')->middleware('can:edit content');
        Route::delete('/{facility}', [AdminFacilityController::class, 'destroy'])->name('destroy')->middleware('can:delete content');
    });

    // Gallery & Media Management
    Route::prefix('media')->name('media.')->middleware('can:view media')->group(function () {
        Route::get('/', [MediaController::class, 'index'])->name('index');
        Route::get('/gallery', [MediaController::class, 'gallery'])->name('gallery');
        Route::post('/upload', [MediaController::class, 'upload'])->name('upload')->middleware('can:upload media');
        Route::get('/{media}', [MediaController::class, 'show'])->name('show');
        Route::patch('/{media}', [MediaController::class, 'update'])->name('update')->middleware('can:edit media');
        Route::delete('/{media}', [MediaController::class, 'destroy'])->name('destroy')->middleware('can:delete media');
        Route::post('/bulk-upload', [MediaController::class, 'bulkUpload'])->name('bulk-upload')->middleware('can:upload media');
    });

    // Rates & Seasons Management
    Route::prefix('rates')->name('rates.')->middleware('can:view content')->group(function () {
        Route::get('/', [AdminRateController::class, 'index'])->name('index');
        Route::get('/seasons', [AdminRateController::class, 'seasons'])->name('seasons');
        Route::post('/seasons', [AdminRateController::class, 'storeSeason'])->name('seasons.store')->middleware('can:edit content');
        Route::patch('/seasons/{season}', [AdminRateController::class, 'updateSeason'])->name('seasons.update')->middleware('can:edit content');
        Route::delete('/seasons/{season}', [AdminRateController::class, 'destroySeason'])->name('seasons.destroy')->middleware('can:delete content');
    });

    // Policies Management
    Route::prefix('policies')->name('policies.')->middleware('can:view content')->group(function () {
        Route::get('/', [PolicyController::class, 'index'])->name('index');
        Route::get('/{policy}/edit', [PolicyController::class, 'edit'])->name('edit')->middleware('can:edit content');
        Route::patch('/{policy}', [PolicyController::class, 'update'])->name('update')->middleware('can:edit content');
    });

    // FAQs Management
    Route::prefix('faqs')->name('faqs.')->middleware('can:view content')->group(function () {
        Route::get('/', [FaqController::class, 'index'])->name('index');
        Route::get('/create', [FaqController::class, 'create'])->name('create')->middleware('can:create content');
        Route::post('/', [FaqController::class, 'store'])->name('store')->middleware('can:create content');
        Route::get('/{faq}/edit', [FaqController::class, 'edit'])->name('edit')->middleware('can:edit content');
        Route::patch('/{faq}', [FaqController::class, 'update'])->name('update')->middleware('can:edit content');
        Route::delete('/{faq}', [FaqController::class, 'destroy'])->name('destroy')->middleware('can:delete content');
    });

    // Blog Management
    Route::prefix('blog')->name('blog.')->middleware('can:view content')->group(function () {
        Route::get('/', [AdminBlogController::class, 'index'])->name('index');
        Route::get('/create', [AdminBlogController::class, 'create'])->name('create')->middleware('can:create content');
        Route::post('/', [AdminBlogController::class, 'store'])->name('store')->middleware('can:create content');
        Route::get('/{post}', [AdminBlogController::class, 'show'])->name('show');
        Route::get('/{post}/edit', [AdminBlogController::class, 'edit'])->name('edit')->middleware('can:edit content');
        Route::patch('/{post}', [AdminBlogController::class, 'update'])->name('update')->middleware('can:edit content');
        Route::delete('/{post}', [AdminBlogController::class, 'destroy'])->name('destroy')->middleware('can:delete content');
        Route::patch('/{post}/publish', [AdminBlogController::class, 'publish'])->name('publish')->middleware('can:publish content');
    });

    // Enquiries (Inbox)
    Route::prefix('enquiries')->name('enquiries.')->middleware('can:view bookings')->group(function () {
        Route::get('/', [EnquiryController::class, 'index'])->name('index');
        Route::get('/{enquiry}', [EnquiryController::class, 'show'])->name('show');
        Route::patch('/{enquiry}/status', [EnquiryController::class, 'updateStatus'])->name('status')->middleware('can:edit bookings');
        Route::post('/{enquiry}/reply', [EnquiryController::class, 'reply'])->name('reply')->middleware('can:create bookings');
        Route::delete('/{enquiry}', [EnquiryController::class, 'destroy'])->name('destroy')->middleware('can:delete bookings');
        Route::get('/export/csv', [EnquiryController::class, 'exportCsv'])->name('export.csv')->middleware('can:export bookings');
    });

    // Settings (contacts, social, metadata)
    Route::prefix('settings')->name('settings.')->middleware('can:view settings')->group(function () {
        Route::get('/', [SettingController::class, 'index'])->name('index');
        Route::get('/contacts', [SettingController::class, 'contacts'])->name('contacts');
        Route::patch('/contacts', [SettingController::class, 'updateContacts'])->name('contacts.update')->middleware('can:edit settings');
        Route::get('/social', [SettingController::class, 'social'])->name('social');
        Route::patch('/social', [SettingController::class, 'updateSocial'])->name('social.update')->middleware('can:edit settings');
        Route::get('/seo', [SettingController::class, 'seo'])->name('seo');
        Route::patch('/seo', [SettingController::class, 'updateSeo'])->name('seo.update')->middleware('can:edit settings');
        Route::get('/general', [SettingController::class, 'general'])->name('general');
        Route::patch('/general', [SettingController::class, 'updateGeneral'])->name('general.update')->middleware('can:edit settings');
    });
});
