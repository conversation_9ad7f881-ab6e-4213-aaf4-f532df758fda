@tailwind base;
@tailwind components;
@tailwind utilities;

/* Alpine.js x-cloak directive */
[x-cloak] {
    display: none !important;
}

/* Accessibility Improvements */
/* Skip to content link */
.skip-link {
    position: absolute;
    top: -40px;
    left: 6px;
    background: #2563eb;
    color: white;
    padding: 8px 16px;
    text-decoration: none;
    border-radius: 4px;
    z-index: 9999;
    font-weight: 600;
    transition: top 0.2s ease;
}

.skip-link:focus {
    top: 6px;
    outline: 3px solid #fbbf24;
    outline-offset: 2px;
}

/* Enhanced focus styles - WCAG 2.1 AA compliant */
*:focus {
    outline: none;
}

*:focus-visible {
    outline: 3px solid #3b82f6;
    outline-offset: 2px;
    border-radius: 4px;
}

/* High contrast focus for form elements */
input:focus-visible,
textarea:focus-visible,
select:focus-visible,
button:focus-visible {
    outline: 3px solid #1d4ed8;
    outline-offset: 2px;
    box-shadow: 0 0 0 1px #ffffff;
}

/* Links with proper contrast */
a {
    color: #1d4ed8;
    text-decoration: underline;
    text-decoration-color: transparent;
    transition: text-decoration-color 0.2s ease;
}

a:hover {
    text-decoration-color: currentColor;
}

a:focus-visible {
    text-decoration-color: currentColor;
    background-color: rgba(59, 130, 246, 0.1);
    border-radius: 2px;
}

/* Ensure sufficient color contrast */
.text-stone-600 {
    color: #4b5563; /* Improved contrast ratio */
}

.text-stone-500 {
    color: #374151; /* Improved contrast ratio */
}

/* Screen reader only text */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Make screen reader text visible when focused */
.sr-only:focus {
    position: static;
    width: auto;
    height: auto;
    margin: 0;
    overflow: visible;
    clip: auto;
    white-space: normal;
}

/* Reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .card {
        border: 2px solid #000000;
    }
    
    .btn-primary {
        background: #000000;
        color: #ffffff;
        border: 2px solid #000000;
    }
    
    .btn-secondary {
        background: #ffffff;
        color: #000000;
        border: 2px solid #000000;
    }
    
    a {
        color: #0000ff;
        text-decoration: underline;
    }
}

/* Custom Base Styles */
@layer base {
    html {
        scroll-behavior: smooth;
    }
    
    body {
        @apply text-stone-900 antialiased;
        font-feature-settings: 'kern' 1, 'liga' 1, 'calt' 1;
    }
    
    /* Focus styles for accessibility */
    *:focus {
        outline: none;
    }
    
    *:focus-visible {
        @apply ring-2 ring-forest-500 ring-offset-2 rounded;
    }
    
    /* Heading hierarchy */
    h1, h2, h3, h4, h5, h6 {
        @apply font-display text-stone-900;
        line-height: 1.2;
    }
    
    /* Selection styles */
    ::selection {
        @apply bg-forest-200 text-forest-900;
    }
    
    /* Scrollbar styles */
    ::-webkit-scrollbar {
        width: 8px;
        height: 8px;
    }
    
    ::-webkit-scrollbar-track {
        @apply bg-stone-100;
    }
    
    ::-webkit-scrollbar-thumb {
        @apply bg-stone-300 rounded-full;
    }
    
    ::-webkit-scrollbar-thumb:hover {
        @apply bg-stone-400;
    }
}

/* Custom Component Styles */
@layer components {
    /* Button variants */
    .btn {
        @apply inline-flex items-center justify-center px-6 py-3 rounded-xl font-semibold text-sm transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2;
    }
    
    .btn-primary {
        @apply bg-forest-600 text-white hover:bg-forest-700 focus:ring-forest-500 shadow-soft hover:shadow-medium transform hover:scale-105;
    }
    
    .btn-secondary {
        @apply bg-white text-forest-600 border-2 border-forest-600 hover:bg-forest-600 hover:text-white focus:ring-forest-500;
    }
    
    .btn-ghost {
        @apply text-forest-600 hover:bg-forest-50 focus:ring-forest-500;
    }
    
    /* Form input styles */
    .form-input {
        @apply w-full px-4 py-3 border border-stone-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-forest-500 focus:border-transparent transition-all duration-200 placeholder-stone-400;
    }
    
    .form-textarea {
        @apply form-input resize-none;
    }
    
    .form-select {
        @apply form-input pr-10 bg-white;
    }
    
    /* Card styles */
    .card {
        @apply bg-white rounded-xl shadow-soft border border-stone-200 overflow-hidden;
    }
    
    .card-hover {
        @apply card hover:shadow-medium hover:scale-105 transition-all duration-300;
    }
    
    /* Section spacing */
    .section {
        @apply py-16 lg:py-24;
    }
    
    .section-sm {
        @apply py-8 lg:py-12;
    }
    
    /* Typography utilities */
    .text-balance {
        text-wrap: balance;
    }
    
    .text-pretty {
        text-wrap: pretty;
    }
    
    /* Container utilities */
    .container-narrow {
        @apply max-w-4xl mx-auto px-4 sm:px-6 lg:px-8;
    }
    
    .container-wide {
        @apply max-w-8xl mx-auto px-4 sm:px-6 lg:px-8;
    }
    
    /* Aspect ratio utilities for images */
    .aspect-square {
        aspect-ratio: 1 / 1;
    }
    
    .aspect-landscape {
        aspect-ratio: 4 / 3;
    }
    
    .aspect-portrait {
        aspect-ratio: 3 / 4;
    }
    
    .aspect-widescreen {
        aspect-ratio: 16 / 9;
    }
    
    /* Image styles */
    .img-cover {
        @apply object-cover w-full h-full;
    }
    
    .img-contain {
        @apply object-contain w-full h-full;
    }
    
    /* Loading states */
    .skeleton {
        @apply bg-stone-200 animate-pulse rounded;
    }
    
    /* Glass effect */
    .glass {
        backdrop-filter: blur(16px) saturate(180%);
        -webkit-backdrop-filter: blur(16px) saturate(180%);
        @apply bg-white/80 border border-white/20;
    }
    
    .glass-dark {
        backdrop-filter: blur(16px) saturate(180%);
        -webkit-backdrop-filter: blur(16px) saturate(180%);
        @apply bg-black/60 border border-white/10;
    }
}

/* Custom Utility Classes */
@layer utilities {
    /* Text gradient */
    .text-gradient {
        background: linear-gradient(135deg, theme('colors.forest.600'), theme('colors.earth.600'));
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }
    
    /* Safari-specific fixes */
    .safari-fix {
        transform: translateZ(0);
    }
    
    /* Smooth animations */
    .animate-smooth {
        @apply transition-all duration-300 ease-in-out;
    }
    
    /* Hide scrollbar but keep functionality */
    .no-scrollbar {
        -ms-overflow-style: none;
        scrollbar-width: none;
    }
    
    .no-scrollbar::-webkit-scrollbar {
        display: none;
    }
    
    /* Custom shadows with safari theme */
    .shadow-safari {
        box-shadow: 0 10px 25px -5px rgba(120, 113, 108, 0.1), 0 8px 10px -6px rgba(120, 113, 108, 0.1);
    }
    
    .shadow-safari-lg {
        box-shadow: 0 20px 35px -12px rgba(120, 113, 108, 0.15), 0 12px 20px -8px rgba(120, 113, 108, 0.1);
    }
    
    /* Clip path utilities for creative layouts */
    .clip-diagonal {
        clip-path: polygon(0 0, 100% 0, 100% 85%, 0 100%);
    }
    
    .clip-wave {
        clip-path: polygon(0 0, 100% 0, 100% 80%, 50% 100%, 0 80%);
    }
}

/* Responsive Design Enhancements */
@media (max-width: 640px) {
    .container {
        @apply px-4;
    }
    
    /* Mobile-specific typography adjustments */
    h1 {
        @apply text-3xl;
    }
    
    h2 {
        @apply text-2xl;
    }
    
    h3 {
        @apply text-xl;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .card {
        @apply border-2 border-stone-900;
    }
    
    .btn-primary {
        @apply bg-black text-white border-2 border-black;
    }
    
    .btn-secondary {
        @apply bg-white text-black border-2 border-black;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
    
    html {
        scroll-behavior: auto !important;
    }
}

/* Dark mode support (future enhancement) */
@media (prefers-color-scheme: dark) {
    /* Dark mode styles would go here */
    /* Currently keeping light theme for safari luxury aesthetic */
}

/* Print styles */
@media print {
    .no-print {
        display: none !important;
    }
    
    body {
        @apply text-black bg-white;
    }
    
    .card {
        @apply border border-stone-400 shadow-none;
    }
    
    a {
        text-decoration: underline;
    }
    
    a[href^="http"]:after {
        content: " (" attr(href) ")";
        font-size: 0.8em;
        color: #666;
    }
}

/* Loading animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInScale {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes shimmer {
    0% {
        background-position: -200% 0;
    }
    100% {
        background-position: 200% 0;
    }
}

/* Loading shimmer effect */
.shimmer {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
}

/* Safari-specific optimizations */
@supports (-webkit-touch-callout: none) {
    /* iOS Safari specific styles */
    .glass {
        -webkit-backdrop-filter: blur(16px) saturate(180%);
    }
    
    /* Fix for iOS Safari viewport units */
    .h-screen-ios {
        height: 100vh;
        height: calc(var(--vh, 1vh) * 100);
    }
}

/* Focus management for better accessibility */
.skip-link {
    position: absolute;
    top: -40px;
    left: 6px;
    background: theme('colors.forest.600');
    color: white;
    padding: 8px;
    text-decoration: none;
    border-radius: 4px;
    z-index: 1000;
}

.skip-link:focus {
    top: 6px;
}

/* Custom checkbox and radio styles */
.custom-checkbox,
.custom-radio {
    @apply appearance-none w-5 h-5 border-2 border-stone-300 rounded focus:ring-2 focus:ring-forest-500 focus:ring-offset-2 checked:bg-forest-600 checked:border-forest-600;
}

.custom-radio {
    @apply rounded-full;
}

.custom-checkbox:checked {
    background-image: url("data:image/svg+xml,%3csvg viewBox='0 0 16 16' fill='white' xmlns='http://www.w3.org/2000/svg'%3e%3cpath d='m13.854 3.646-7-7a.5.5 0 0 0-.708 0l-3.5 3.5a.5.5 0 1 0 .708.708L6.5 3.707l6.646 6.647a.5.5 0 0 0 .708-.708z'/%3e%3c/svg%3e");
}

.custom-radio:checked {
    background-image: url("data:image/svg+xml,%3csvg viewBox='0 0 16 16' fill='white' xmlns='http://www.w3.org/2000/svg'%3e%3ccircle cx='8' cy='8' r='3'/%3e%3c/svg%3e");
}
