<nav id="main-navigation"
     x-data="{ mobileMenuOpen: false, scrolled: false }" 
     x-init="window.addEventListener('scroll', () => scrolled = window.scrollY > 100)"
     :class="{ 'bg-white shadow-lg': scrolled, 'bg-transparent': !scrolled }"
     class="fixed top-0 left-0 right-0 z-50 transition-all duration-300"
     role="navigation"
     aria-label="Main navigation">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center h-20">
            {{-- Logo --}}
            <div class="flex-shrink-0">
                <a href="{{ route('home') }}" class="flex items-center">
                    @if(setting('site_logo'))
                        <img src="{{ asset(setting('site_logo')) }}" 
                             alt="{{ setting('site_name', 'Malombo Selous Forest Camp') }}" 
                             class="h-12 w-auto">
                    @else
                        <div class="text-2xl font-bold text-amber-600">
                            {{ setting('site_name', 'Malombo') }}
                        </div>
                    @endif
                </a>
            </div>

            {{-- Desktop Navigation --}}
            <div class="hidden lg:flex lg:items-center lg:space-x-8">
                <a href="{{ route('home') }}" 
                   class="nav-link {{ request()->routeIs('home') ? 'active' : '' }}">
                    Home
                </a>
                
                <div class="relative group">
                    <a href="{{ route('lodge.index') }}" 
                       class="nav-link {{ request()->routeIs('lodge.*') ? 'active' : '' }}">
                        The Lodge
                    </a>
                    <div class="absolute top-full left-0 mt-2 w-48 bg-white rounded-lg shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300">
                        <a href="{{ route('lodge.about') }}" class="block px-4 py-2 text-gray-700 hover:bg-amber-50 hover:text-amber-600">About Us</a>
                        <a href="{{ route('lodge.experience') }}" class="block px-4 py-2 text-gray-700 hover:bg-amber-50 hover:text-amber-600">Experience</a>
                        <a href="{{ route('lodge.history') }}" class="block px-4 py-2 text-gray-700 hover:bg-amber-50 hover:text-amber-600">History</a>
                    </div>
                </div>
                
                <a href="{{ route('accommodation.index') }}" 
                   class="nav-link {{ request()->routeIs('accommodation.*') ? 'active' : '' }}">
                    Accommodation
                </a>
                
                <a href="{{ route('activities.index') }}" 
                   class="nav-link {{ request()->routeIs('activities.*') ? 'active' : '' }}">
                    Activities
                </a>
                
                <a href="{{ route('facilities.index') }}" 
                   class="nav-link {{ request()->routeIs('facilities.*') ? 'active' : '' }}">
                    Facilities
                </a>
                
                <div class="relative group">
                    <button class="nav-link {{ request()->routeIs(['rates.*', 'gallery.*', 'location.*']) ? 'active' : '' }}">
                        Explore
                        <svg class="w-4 h-4 ml-1 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                        </svg>
                    </button>
                    <div class="absolute top-full right-0 mt-2 w-48 bg-white rounded-lg shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300">
                        <a href="{{ route('rates.index') }}" class="block px-4 py-2 text-gray-700 hover:bg-amber-50 hover:text-amber-600">Rates & Policies</a>
                        <a href="{{ route('gallery.index') }}" class="block px-4 py-2 text-gray-700 hover:bg-amber-50 hover:text-amber-600">Gallery</a>
                        <a href="{{ route('location.index') }}" class="block px-4 py-2 text-gray-700 hover:bg-amber-50 hover:text-amber-600">Location</a>
                        <a href="{{ route('blog.index') }}" class="block px-4 py-2 text-gray-700 hover:bg-amber-50 hover:text-amber-600">Blog</a>
                    </div>
                </div>
                
                <a href="{{ route('contact.index') }}" 
                   class="nav-link {{ request()->routeIs('contact.*') ? 'active' : '' }}">
                    Contact
                </a>
                
                {{-- CTA Button --}}
                <button onclick="openBookingModal()" 
                        class="bg-amber-600 hover:bg-amber-700 text-white px-6 py-2 rounded-full font-medium transition-colors duration-300">
                    Book Now
                </button>
            </div>

            {{-- Mobile menu button --}}
            <div class="lg:hidden">
                <button @click="mobileMenuOpen = !mobileMenuOpen" 
                        type="button" 
                        class="text-gray-700 hover:text-amber-600 focus:outline-none focus:text-amber-600">
                    <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path x-show="!mobileMenuOpen" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                        <path x-show="mobileMenuOpen" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" style="display: none;" />
                    </svg>
                </button>
            </div>
        </div>

        {{-- Mobile Navigation --}}
        <div x-show="mobileMenuOpen" 
             x-transition:enter="transition ease-out duration-200"
             x-transition:enter-start="opacity-0 transform scale-95"
             x-transition:enter-end="opacity-100 transform scale-100"
             x-transition:leave="transition ease-in duration-150"
             x-transition:leave-start="opacity-100 transform scale-100"
             x-transition:leave-end="opacity-0 transform scale-95"
             class="lg:hidden bg-white border-t border-gray-200"
             style="display: none;">
            <div class="px-2 pt-2 pb-3 space-y-1">
                <a href="{{ route('home') }}" class="mobile-nav-link {{ request()->routeIs('home') ? 'active' : '' }}">Home</a>
                <a href="{{ route('lodge.index') }}" class="mobile-nav-link {{ request()->routeIs('lodge.*') ? 'active' : '' }}">The Lodge</a>
                <a href="{{ route('accommodation.index') }}" class="mobile-nav-link {{ request()->routeIs('accommodation.*') ? 'active' : '' }}">Accommodation</a>
                <a href="{{ route('activities.index') }}" class="mobile-nav-link {{ request()->routeIs('activities.*') ? 'active' : '' }}">Activities</a>
                <a href="{{ route('facilities.index') }}" class="mobile-nav-link {{ request()->routeIs('facilities.*') ? 'active' : '' }}">Facilities</a>
                <a href="{{ route('rates.index') }}" class="mobile-nav-link {{ request()->routeIs('rates.*') ? 'active' : '' }}">Rates & Policies</a>
                <a href="{{ route('gallery.index') }}" class="mobile-nav-link {{ request()->routeIs('gallery.*') ? 'active' : '' }}">Gallery</a>
                <a href="{{ route('location.index') }}" class="mobile-nav-link {{ request()->routeIs('location.*') ? 'active' : '' }}">Location</a>
                <a href="{{ route('blog.index') }}" class="mobile-nav-link {{ request()->routeIs('blog.*') ? 'active' : '' }}">Blog</a>
                <a href="{{ route('contact.index') }}" class="mobile-nav-link {{ request()->routeIs('contact.*') ? 'active' : '' }}">Contact</a>
                <button onclick="openBookingModal()" class="w-full text-left mobile-nav-link text-amber-600 font-medium">Book Now</button>
            </div>
        </div>
    </div>
</nav>

<style>
.nav-link {
    @apply text-gray-700 hover:text-amber-600 font-medium transition-colors duration-300 py-2;
}

.nav-link.active {
    @apply text-amber-600;
}

.mobile-nav-link {
    @apply block px-3 py-2 text-base font-medium text-gray-700 hover:text-amber-600 hover:bg-amber-50 rounded-md transition-colors duration-300;
}

.mobile-nav-link.active {
    @apply text-amber-600 bg-amber-50;
}
</style>
