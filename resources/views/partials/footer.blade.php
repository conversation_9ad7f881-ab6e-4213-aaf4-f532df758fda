<footer id="footer" class="bg-gray-900 text-gray-300" role="contentinfo">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            
            {{-- Brand & Description --}}
            <div class="lg:col-span-1">
                <div class="mb-4">
                    @if(setting('site_logo'))
                        <img src="{{ asset(setting('site_logo')) }}" 
                             alt="{{ setting('site_name', 'Malombo Selous Forest Camp') }}" 
                             class="h-10 w-auto mb-4">
                    @else
                        <h3 class="text-xl font-bold text-white mb-4">{{ setting('site_name', 'Malombo') }}</h3>
                    @endif
                </div>
                <p class="text-sm mb-4">
                    {{ setting('site_tagline', 'Experience the authentic African wilderness in Nyerere National Park (formerly Selous Game Reserve). Luxury eco-lodge accommodation with unparalleled wildlife experiences.') }}
                </p>
                
                {{-- Social Media Links --}}
                @if(setting('social_facebook') || setting('social_instagram') || setting('social_twitter') || setting('social_youtube') || setting('social_tripadvisor'))
                    <div class="flex space-x-4">
                        @if(setting('social_facebook'))
                            <a href="{{ setting('social_facebook') }}" target="_blank" rel="noopener noreferrer" 
                               class="text-gray-400 hover:text-white transition-colors duration-300">
                                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                                </svg>
                            </a>
                        @endif
                        
                        @if(setting('social_instagram'))
                            <a href="{{ setting('social_instagram') }}" target="_blank" rel="noopener noreferrer" 
                               class="text-gray-400 hover:text-white transition-colors duration-300">
                                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 6.62 5.367 11.987 11.988 11.987 6.62 0 11.987-5.367 11.987-11.987C24.014 5.367 18.637.001 12.017.001zM8.449 16.988c-1.297 0-2.448-.73-3.016-1.8-.567-1.07-.344-2.395.56-3.299.904-.904 2.229-1.127 3.299-.56 1.07.568 1.8 1.719 1.8 3.016 0 1.874-1.517 3.391-3.391 3.391z"/>
                                </svg>
                            </a>
                        @endif
                        
                        @if(setting('social_twitter'))
                            <a href="{{ setting('social_twitter') }}" target="_blank" rel="noopener noreferrer" 
                               class="text-gray-400 hover:text-white transition-colors duration-300">
                                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
                                </svg>
                            </a>
                        @endif
                        
                        @if(setting('social_youtube'))
                            <a href="{{ setting('social_youtube') }}" target="_blank" rel="noopener noreferrer" 
                               class="text-gray-400 hover:text-white transition-colors duration-300">
                                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z"/>
                                </svg>
                            </a>
                        @endif
                        
                        @if(setting('social_tripadvisor'))
                            <a href="{{ setting('social_tripadvisor') }}" target="_blank" rel="noopener noreferrer" 
                               class="text-gray-400 hover:text-white transition-colors duration-300">
                                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M12.006 4.295c-2.67 0-5.338.784-7.645 2.353H0l1.963 1.904c1.24-1.21 2.89-1.984 4.715-2.117.684-.742 1.825-1.267 3.106-1.267 1.284 0 2.42.528 3.106 1.267 1.825.133 3.478.907 4.715 2.117L19.567 6.648h-4.361c-2.309-1.569-4.977-2.353-7.645-2.353H12.006z"/>
                                </svg>
                            </a>
                        @endif
                    </div>
                @endif
            </div>
            
            {{-- Quick Links --}}
            <div>
                <h4 class="text-lg font-semibold text-white mb-4">Quick Links</h4>
                <ul class="space-y-2 text-sm">
                    <li><a href="{{ route('lodge.index') }}" class="hover:text-white transition-colors duration-300">About The Lodge</a></li>
                    <li><a href="{{ route('accommodation.index') }}" class="hover:text-white transition-colors duration-300">Accommodation</a></li>
                    <li><a href="{{ route('activities.index') }}" class="hover:text-white transition-colors duration-300">Safari Activities</a></li>
                    <li><a href="{{ route('facilities.index') }}" class="hover:text-white transition-colors duration-300">Facilities</a></li>
                    <li><a href="{{ route('rates.index') }}" class="hover:text-white transition-colors duration-300">Rates & Policies</a></li>
                    <li><a href="{{ route('gallery.index') }}" class="hover:text-white transition-colors duration-300">Photo Gallery</a></li>
                </ul>
            </div>
            
            {{-- Contact Information --}}
            <div>
                <h4 class="text-lg font-semibold text-white mb-4">Contact Us</h4>
                <div class="space-y-3 text-sm">
                    @if(setting('contact_email'))
                        <div class="flex items-center">
                            <svg class="w-4 h-4 mr-2 text-amber-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
                            </svg>
                            <a href="mailto:{{ setting('contact_email') }}" class="hover:text-white transition-colors duration-300">
                                {{ setting('contact_email') }}
                            </a>
                        </div>
                    @endif
                    
                    @if(setting('contact_phone'))
                        <div class="flex items-center">
                            <svg class="w-4 h-4 mr-2 text-amber-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"/>
                            </svg>
                            <a href="tel:{{ setting('contact_phone') }}" class="hover:text-white transition-colors duration-300">
                                {{ setting('contact_phone') }}
                            </a>
                        </div>
                    @endif
                    
                    @if(setting('booking_email'))
                        <div class="flex items-center">
                            <svg class="w-4 h-4 mr-2 text-amber-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3a2 2 0 012-2h4a2 2 0 012 2v4h3a2 2 0 012 2v1.586a1 1 0 01-.293.707L18 14l3.707 3.707A1 1 0 0121 18.414V20a2 2 0 01-2 2H5a2 2 0 01-2-2v-1.586a1 1 0 01.293-.707L7 14 3.293 10.293A1 1 0 013 9.586V8a2 2 0 012-2h3z"/>
                            </svg>
                            <div>
                                <div class="text-xs text-gray-400">Reservations:</div>
                                <a href="mailto:{{ setting('booking_email') }}" class="hover:text-white transition-colors duration-300">
                                    {{ setting('booking_email') }}
                                </a>
                            </div>
                        </div>
                    @endif
                    
                    @if(setting('contact_address'))
                        <div class="flex items-start">
                            <svg class="w-4 h-4 mr-2 mt-0.5 text-amber-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"/>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"/>
                            </svg>
                            <div class="leading-relaxed">{{ setting('contact_address') }}</div>
                        </div>
                    @endif
                </div>
            </div>
            
            {{-- Certifications & Awards --}}
            <div>
                <h4 class="text-lg font-semibold text-white mb-4">Certifications</h4>
                <div class="space-y-3 text-sm">
                    <div class="flex items-center">
                        <svg class="w-4 h-4 mr-2 text-green-500" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                        </svg>
                        <span>Eco-Certified Lodge</span>
                    </div>
                    <div class="flex items-center">
                        <svg class="w-4 h-4 mr-2 text-green-500" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                        </svg>
                        <span>TANAPA Licensed</span>
                    </div>
                    <div class="flex items-center">
                        <svg class="w-4 h-4 mr-2 text-green-500" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                        </svg>
                        <span>Responsible Tourism</span>
                    </div>
                </div>
            </div>
        </div>
        
        {{-- Bottom Section --}}
        <div class="border-t border-gray-800 mt-8 pt-8">
            <div class="flex flex-col md:flex-row md:justify-between md:items-center">
                <div class="text-sm text-gray-400 mb-4 md:mb-0">
                    <p>&copy; {{ date('Y') }} {{ setting('site_name', 'Malombo Selous Forest Camp') }}. All rights reserved.</p>
                </div>
                
                <div class="flex space-x-6 text-sm">
                    <a href="{{ route('legal.privacy') }}" class="text-gray-400 hover:text-white transition-colors duration-300">Privacy Policy</a>
                    <a href="{{ route('legal.terms') }}" class="text-gray-400 hover:text-white transition-colors duration-300">Terms of Service</a>
                    <a href="{{ route('legal.cookies') }}" class="text-gray-400 hover:text-white transition-colors duration-300">Cookie Policy</a>
                </div>
            </div>
        </div>
    </div>
</footer>
