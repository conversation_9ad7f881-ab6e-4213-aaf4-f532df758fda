<div class="max-w-4xl mx-auto p-6">
    {{-- Progress Indicator --}}
    <div class="mb-8">
        <div class="flex items-center justify-between mb-4">
            @for($i = 1; $i <= $totalSteps; $i++)
                <div class="flex items-center {{ $i < $totalSteps ? 'flex-1' : '' }}">
                    <div class="flex items-center justify-center w-10 h-10 rounded-full border-2 
                        {{ $currentStep >= $i ? 'bg-green-600 border-green-600 text-white' : 'border-gray-300 text-gray-500' }}">
                        @if($currentStep > $i)
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                            </svg>
                        @else
                            {{ $i }}
                        @endif
                    </div>
                    @if($i < $totalSteps)
                        <div class="flex-1 h-1 mx-4 {{ $currentStep > $i ? 'bg-green-600' : 'bg-gray-300' }}"></div>
                    @endif
                </div>
            @endfor
        </div>
        
        <div class="text-center">
            <h2 class="text-2xl font-bold text-gray-900 mb-2">
                @switch($currentStep)
                    @case(1) Your Information @break
                    @case(2) Travel Dates @break
                    @case(3) Guest Details @break
                    @case(4) Preferences @break
                    @case(5) Final Details @break
                @endswitch
            </h2>
            <p class="text-gray-600">Step {{ $currentStep }} of {{ $totalSteps }}</p>
        </div>
    </div>

    {{-- Error Messages --}}
    @if($errors->has('general'))
        <div class="mb-6 p-4 bg-red-100 border border-red-400 text-red-700 rounded-lg">
            {{ $errors->first('general') }}
        </div>
    @endif

    <form wire:submit="submit">
        {{-- Honeypot Field --}}
        <input type="text" wire:model="website" style="display: none;" tabindex="-1" autocomplete="off">

        {{-- Step 1: Your Information --}}
        @if($currentStep === 1)
            <div class="space-y-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="name" class="block text-sm font-medium text-gray-700 mb-2">Full Name *</label>
                        <input type="text" id="name" wire:model="name" 
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 @error('name') border-red-500 @enderror"
                               placeholder="Enter your full name">
                        @error('name')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                    
                    <div>
                        <label for="email" class="block text-sm font-medium text-gray-700 mb-2">Email Address *</label>
                        <input type="email" id="email" wire:model="email" 
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 @error('email') border-red-500 @enderror"
                               placeholder="<EMAIL>">
                        @error('email')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="phone" class="block text-sm font-medium text-gray-700 mb-2">Phone Number</label>
                        <input type="tel" id="phone" wire:model="phone" 
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 @error('phone') border-red-500 @enderror"
                               placeholder="+****************">
                        @error('phone')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                    
                    <div>
                        <label for="country" class="block text-sm font-medium text-gray-700 mb-2">Country</label>
                        <input type="text" id="country" wire:model="country" 
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 @error('country') border-red-500 @enderror"
                               placeholder="Your country">
                        @error('country')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>
            </div>
        @endif

        {{-- Step 2: Travel Dates --}}
        @if($currentStep === 2)
            <div class="space-y-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="arrival_date" class="block text-sm font-medium text-gray-700 mb-2">Arrival Date *</label>
                        <input type="date" id="arrival_date" wire:model="arrival_date" 
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 @error('arrival_date') border-red-500 @enderror"
                               min="{{ date('Y-m-d', strtotime('+1 day')) }}">
                        @error('arrival_date')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                    
                    <div>
                        <label for="departure_date" class="block text-sm font-medium text-gray-700 mb-2">Departure Date *</label>
                        <input type="date" id="departure_date" wire:model="departure_date" 
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 @error('departure_date') border-red-500 @enderror"
                               min="{{ $arrival_date ?: date('Y-m-d', strtotime('+2 days')) }}">
                        @error('departure_date')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>
                
                @if($arrival_date && $departure_date)
                    <div class="p-4 bg-green-50 border border-green-200 rounded-lg">
                        <div class="flex items-center">
                            <svg class="w-5 h-5 text-green-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                            </svg>
                            <span class="text-green-800 font-medium">
                                {{ \Carbon\Carbon::parse($departure_date)->diffInDays(\Carbon\Carbon::parse($arrival_date)) }} nights stay
                            </span>
                        </div>
                    </div>
                @endif
            </div>
        @endif

        {{-- Step 3: Guest Details --}}
        @if($currentStep === 3)
            <div class="space-y-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="adults" class="block text-sm font-medium text-gray-700 mb-2">Adults *</label>
                        <select id="adults" wire:model="adults" 
                                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 @error('adults') border-red-500 @enderror">
                            @for($i = 1; $i <= 20; $i++)
                                <option value="{{ $i }}">{{ $i }} {{ $i === 1 ? 'Adult' : 'Adults' }}</option>
                            @endfor
                        </select>
                        @error('adults')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                    
                    <div>
                        <label for="children" class="block text-sm font-medium text-gray-700 mb-2">Children</label>
                        <select id="children" wire:model="children" 
                                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 @error('children') border-red-500 @enderror">
                            @for($i = 0; $i <= 20; $i++)
                                <option value="{{ $i }}">{{ $i }} {{ $i === 1 ? 'Child' : 'Children' }}</option>
                            @endfor
                        </select>
                        @error('children')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>
                
                <div>
                    <label for="special_requirements" class="block text-sm font-medium text-gray-700 mb-2">Special Requirements</label>
                    <textarea id="special_requirements" wire:model="special_requirements" rows="4"
                              class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 @error('special_requirements') border-red-500 @enderror"
                              placeholder="Dietary restrictions, accessibility needs, celebrations, etc."></textarea>
                    @error('special_requirements')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
            </div>
        @endif

        {{-- Step 4: Preferences --}}
        @if($currentStep === 4)
            <div class="space-y-6">
                <div>
                    <label for="accommodation_preference" class="block text-sm font-medium text-gray-700 mb-2">Preferred Accommodation</label>
                    <select id="accommodation_preference" wire:model="accommodation_preference" 
                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500">
                        <option value="">No preference</option>
                        @foreach($accommodations as $accommodation)
                            <option value="{{ $accommodation->title }}">{{ $accommodation->title }}</option>
                        @endforeach
                    </select>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-3">Activities of Interest</label>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
                        @foreach($activities as $activity)
                            <label class="flex items-center p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer">
                                <input type="checkbox" wire:model="activity_interests" value="{{ $activity->id }}" 
                                       class="rounded border-gray-300 text-green-600 focus:ring-green-500">
                                <span class="ml-3 text-sm font-medium text-gray-700">{{ $activity->title }}</span>
                            </label>
                        @endforeach
                    </div>
                </div>
                
                <div>
                    <label for="budget_range" class="block text-sm font-medium text-gray-700 mb-2">Budget Range (per person per night)</label>
                    <select id="budget_range" wire:model="budget_range" 
                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500">
                        <option value="">Prefer not to say</option>
                        <option value="budget">Budget ($100-200)</option>
                        <option value="mid-range">Mid-range ($200-400)</option>
                        <option value="luxury">Luxury ($400-800)</option>
                        <option value="ultra-luxury">Ultra-luxury ($800+)</option>
                    </select>
                </div>
            </div>
        @endif

        {{-- Step 5: Final Details --}}
        @if($currentStep === 5)
            <div class="space-y-6">
                <div>
                    <label for="message" class="block text-sm font-medium text-gray-700 mb-2">Additional Message</label>
                    <textarea id="message" wire:model="message" rows="4"
                              class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 @error('message') border-red-500 @enderror"
                              placeholder="Tell us about your dream safari experience..."></textarea>
                    @error('message')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
                
                <div>
                    <label for="how_did_you_hear" class="block text-sm font-medium text-gray-700 mb-2">How did you hear about us?</label>
                    <select id="how_did_you_hear" wire:model="how_did_you_hear" 
                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500">
                        <option value="">Please select</option>
                        <option value="website">Online search</option>
                        <option value="social_media">Social media</option>
                        <option value="referral">Friend/family recommendation</option>
                        <option value="travel_agent">Travel agent</option>
                        <option value="other">Other</option>
                    </select>
                </div>
                
                {{-- Summary Review --}}
                <div class="p-6 bg-gray-50 border border-gray-200 rounded-lg">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Booking Summary</h3>
                    <div class="space-y-2 text-sm">
                        <div class="flex justify-between">
                            <span class="text-gray-600">Guest:</span>
                            <span class="font-medium">{{ $name }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Dates:</span>
                            <span class="font-medium">{{ $arrival_date }} to {{ $departure_date }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Guests:</span>
                            <span class="font-medium">{{ $adults }} adults{{ $children > 0 ? ', ' . $children . ' children' : '' }}</span>
                        </div>
                        @if($accommodation_preference)
                            <div class="flex justify-between">
                                <span class="text-gray-600">Accommodation:</span>
                                <span class="font-medium">{{ $accommodation_preference }}</span>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        @endif

        {{-- Navigation Buttons --}}
        <div class="flex justify-between items-center mt-8 pt-6 border-t border-gray-200">
            <div>
                @if($currentStep > 1)
                    <button type="button" wire:click="previousStep" 
                            class="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 font-medium transition-colors duration-200">
                        Previous
                    </button>
                @endif
            </div>
            
            <div>
                @if($currentStep < $totalSteps)
                    <button type="button" wire:click="nextStep" 
                            class="px-8 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 font-medium transition-colors duration-200">
                        Continue
                    </button>
                @else
                    <button type="submit" 
                            class="px-8 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 font-medium transition-colors duration-200 flex items-center">
                        <svg wire:loading wire:target="submit" class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <span wire:loading.remove wire:target="submit">Submit Enquiry</span>
                        <span wire:loading wire:target="submit">Submitting...</span>
                    </button>
                @endif
            </div>
        </div>
    </form>
</div>
