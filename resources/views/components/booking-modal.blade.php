{{-- Booking Modal Component --}}
<x-modal name="booking-modal" :show="false" max-width="4xl" focusable>
    <div class="px-6 py-8 bg-white">
        {{-- Modal Header --}}
        <div class="flex items-center justify-between mb-6 pb-4 border-b border-gray-200">
            <div>
                <h2 class="text-2xl font-bold text-gray-900">Start Your Safari Adventure</h2>
                <p class="text-gray-600 mt-1">Complete your booking enquiry and we'll get back to you within 24 hours</p>
            </div>
            <button @click="$dispatch('close-modal', 'booking-modal')" 
                    class="text-gray-400 hover:text-gray-600 transition-colors">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                </svg>
            </button>
        </div>

        {{-- Livewire Booking Form --}}
        <div class="max-h-96 overflow-y-auto">
            @livewire('booking-enquiry')
        </div>
    </div>
</x-modal>

{{-- Alpine.js helper function --}}
@push('scripts')
<script>
    function openBookingModal() {
        window.dispatchEvent(new CustomEvent('open-modal', {
            detail: 'booking-modal'
        }));
    }
    
    // Make it globally available
    window.openBookingModal = openBookingModal;
</script>
@endpush
