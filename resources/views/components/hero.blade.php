@props([
    'backgroundImage' => null,
    'title' => null,
    'subtitle' => null,
    'tagline' => null,
    'ctaText' => 'Enquire Now',
    'ctaUrl' => null,
    'height' => 'screen', // screen, lg, md, sm
    'overlay' => 'dark', // dark, light, none
    'textAlign' => 'center', // center, left, right
    'backgroundPosition' => 'center', // center, top, bottom
])

@php
    $heightClass = match($height) {
        'screen' => 'h-screen',
        'lg' => 'h-96 lg:h-[32rem]',
        'md' => 'h-80 lg:h-96',
        'sm' => 'h-64 lg:h-80',
        default => 'h-screen'
    };

    $overlayClass = match($overlay) {
        'dark' => 'bg-black/40',
        'light' => 'bg-white/20',
        'none' => '',
        default => 'bg-black/40'
    };

    $textAlignClass = match($textAlign) {
        'center' => 'text-center',
        'left' => 'text-left',
        'right' => 'text-right',
        default => 'text-center'
    };

    $backgroundPositionClass = match($backgroundPosition) {
        'center' => 'bg-center',
        'top' => 'bg-top',
        'bottom' => 'bg-bottom',
        default => 'bg-center'
    };

    $ctaRoute = $ctaUrl ?? route('enquire');
@endphp

<section 
    class="relative {{ $heightClass }} flex items-center justify-center overflow-hidden"
    role="banner"
>
    <!-- Background Image -->
    @if($backgroundImage)
        <div 
            class="absolute inset-0 bg-cover bg-no-repeat {{ $backgroundPositionClass }} transform scale-105"
            style="background-image: url('{{ $backgroundImage }}')"
            role="img"
            aria-label="Hero background"
        ></div>
        
        <!-- Overlay -->
        @if($overlay !== 'none')
            <div class="absolute inset-0 {{ $overlayClass }}"></div>
        @endif
    @else
        <!-- Gradient Background -->
        <div class="absolute inset-0 bg-gradient-to-br from-forest-600 via-forest-700 to-forest-900"></div>
    @endif

    <!-- Content Container -->
    <div class="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 w-full">
        <div class="max-w-4xl {{ $textAlign === 'center' ? 'mx-auto' : ($textAlign === 'right' ? 'ml-auto' : '') }}">
            
            <!-- Tagline -->
            @if($tagline)
                <p class="text-sm sm:text-base font-medium text-accent-amber-300 uppercase tracking-widest mb-4 animate-fade-in">
                    {{ $tagline }}
                </p>
            @endif

            <!-- Main Title -->
            @if($title)
                <h1 class="text-4xl sm:text-5xl lg:text-6xl xl:text-7xl font-display font-bold text-white leading-tight mb-6 animate-slide-up">
                    {!! $title !!}
                </h1>
            @endif

            <!-- Subtitle -->
            @if($subtitle)
                <p class="text-lg sm:text-xl lg:text-2xl text-stone-200 leading-relaxed mb-8 max-w-3xl {{ $textAlign === 'center' ? 'mx-auto' : '' }} animate-slide-up" style="animation-delay: 0.2s;">
                    {{ $subtitle }}
                </p>
            @endif

            <!-- CTA Button -->
            @if($ctaText && $ctaRoute)
                <div class="animate-slide-up" style="animation-delay: 0.4s;">
                    <a
                        href="{{ $ctaRoute }}"
                        class="inline-flex items-center bg-forest-600 hover:bg-forest-700 text-white px-8 py-4 rounded-2xl font-semibold text-lg transition-all duration-300 shadow-hard hover:shadow-xl focus:outline-none focus:ring-4 focus:ring-forest-500/50 focus:ring-offset-2 focus:ring-offset-transparent transform hover:scale-105 group"
                    >
                        {{ $ctaText }}
                        <svg class="ml-2 h-5 w-5 transition-transform duration-200 group-hover:translate-x-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                        </svg>
                    </a>
                </div>
            @endif

            <!-- Additional Content Slot -->
            @if(isset($slot) && !$slot->isEmpty())
                <div class="mt-8 animate-fade-in" style="animation-delay: 0.6s;">
                    {{ $slot }}
                </div>
            @endif
        </div>
    </div>

    <!-- Scroll Down Indicator -->
    @if($height === 'screen')
        <div class="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-float">
            <a 
                href="#content"
                class="text-white/80 hover:text-white transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-white/50 rounded-full p-2"
                aria-label="Scroll down to content"
            >
                <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3"></path>
                </svg>
            </a>
        </div>
    @endif
</section>

@push('head')
<style>
    @keyframes fadeIn {
        from { opacity: 0; }
        to { opacity: 1; }
    }
    
    @keyframes slideUp {
        from { 
            opacity: 0; 
            transform: translateY(30px); 
        }
        to { 
            opacity: 1; 
            transform: translateY(0); 
        }
    }
    
    @keyframes float {
        0%, 100% { 
            transform: translateY(0px); 
        }
        50% { 
            transform: translateY(-10px); 
        }
    }
    
    .animate-fade-in {
        animation: fadeIn 0.8s ease-out;
    }
    
    .animate-slide-up {
        animation: slideUp 0.8s ease-out;
    }
    
    .animate-float {
        animation: float 3s ease-in-out infinite;
    }
</style>
@endpush
