@props([
    'title' => '',
    'subtitle' => '',
    'ctaText' => 'Get Started',
    'ctaUrl' => '#',
    'secondaryCtaText' => null,
    'secondaryCtaUrl' => null,
    'backgroundImage' => null,
    'variant' => 'default', // default, gradient, image, minimal
    'size' => 'default', // small, default, large
    'alignment' => 'center', // left, center, right
])

@php
    $containerClasses = match($size) {
        'small' => 'py-8 lg:py-12',
        'default' => 'py-12 lg:py-16',
        'large' => 'py-16 lg:py-24',
        default => 'py-12 lg:py-16'
    };

    $bgClasses = match($variant) {
        'gradient' => 'bg-gradient-to-r from-forest-600 via-forest-700 to-earth-700',
        'image' => 'bg-cover bg-center bg-no-repeat relative',
        'minimal' => 'bg-stone-50 border border-stone-200',
        default => 'bg-forest-600'
    };

    $textColorClasses = match($variant) {
        'minimal' => 'text-stone-900',
        default => 'text-white'
    };

    $alignmentClasses = match($alignment) {
        'left' => 'text-left',
        'center' => 'text-center',
        'right' => 'text-right',
        default => 'text-center'
    };

    $titleSizeClasses = match($size) {
        'small' => 'text-2xl lg:text-3xl',
        'default' => 'text-3xl lg:text-4xl',
        'large' => 'text-4xl lg:text-5xl',
        default => 'text-3xl lg:text-4xl'
    };
@endphp

<section class="{{ $bgClasses }} {{ $containerClasses }} rounded-2xl overflow-hidden"
    @if($backgroundImage && $variant === 'image')
        style="background-image: url('{{ $backgroundImage }}')"
    @endif
>
    @if($backgroundImage && $variant === 'image')
        <!-- Image Overlay -->
        <div class="absolute inset-0 bg-black/50"></div>
    @endif

    <div class="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="max-w-4xl {{ $alignment === 'center' ? 'mx-auto' : ($alignment === 'right' ? 'ml-auto' : '') }}">
            
            <!-- Title -->
            @if($title)
                <h2 class="{{ $titleSizeClasses }} font-display font-bold {{ $textColorClasses }} mb-4 {{ $alignmentClasses }}">
                    {!! $title !!}
                </h2>
            @endif

            <!-- Subtitle -->
            @if($subtitle)
                <p class="text-lg lg:text-xl {{ $variant === 'minimal' ? 'text-stone-600' : 'text-stone-200' }} leading-relaxed mb-8 max-w-2xl {{ $alignment === 'center' ? 'mx-auto' : '' }} {{ $alignmentClasses }}">
                    {{ $subtitle }}
                </p>
            @endif

            <!-- Additional Content Slot -->
            @if(isset($slot) && !$slot->isEmpty())
                <div class="mb-8 {{ $alignmentClasses }}">
                    {{ $slot }}
                </div>
            @endif

            <!-- CTA Buttons -->
            <div class="flex flex-col sm:flex-row gap-4 {{ $alignment === 'center' ? 'justify-center' : ($alignment === 'right' ? 'justify-end' : 'justify-start') }}">
                @if($ctaText && $ctaUrl)
                    <a
                        href="{{ $ctaUrl }}"
                        class="inline-flex items-center justify-center {{ $variant === 'minimal' ? 'bg-forest-600 hover:bg-forest-700' : 'bg-white hover:bg-stone-100' }} {{ $variant === 'minimal' ? 'text-white' : 'text-forest-700' }} px-8 py-4 rounded-xl font-semibold text-lg transition-all duration-300 shadow-medium hover:shadow-hard focus:outline-none focus:ring-4 {{ $variant === 'minimal' ? 'focus:ring-forest-500/50' : 'focus:ring-white/50' }} focus:ring-offset-2 {{ $variant === 'minimal' ? 'focus:ring-offset-transparent' : 'focus:ring-offset-forest-600' }} transform hover:scale-105 group"
                    >
                        {{ $ctaText }}
                        <svg class="ml-2 h-5 w-5 transition-transform duration-200 group-hover:translate-x-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                        </svg>
                    </a>
                @endif

                @if($secondaryCtaText && $secondaryCtaUrl)
                    <a
                        href="{{ $secondaryCtaUrl }}"
                        class="inline-flex items-center justify-center border-2 {{ $variant === 'minimal' ? 'border-forest-600 text-forest-600 hover:bg-forest-600 hover:text-white' : 'border-white text-white hover:bg-white hover:text-forest-700' }} px-8 py-4 rounded-xl font-semibold text-lg transition-all duration-300 focus:outline-none focus:ring-4 {{ $variant === 'minimal' ? 'focus:ring-forest-500/50' : 'focus:ring-white/50' }} focus:ring-offset-2 {{ $variant === 'minimal' ? 'focus:ring-offset-transparent' : 'focus:ring-offset-forest-600' }} transform hover:scale-105"
                    >
                        {{ $secondaryCtaText }}
                    </a>
                @endif
            </div>
        </div>
    </div>
</section>
