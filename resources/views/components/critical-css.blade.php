@props(['page' => 'default'])

@php
$criticalCSS = match($page) {
    'home' => '
        .hero-section{background:linear-gradient(to right,#d97706,#92400e);min-height:100vh;display:flex;align-items:center;justify-content:center;color:white;position:relative}
        .hero-section::before{content:"";position:absolute;top:0;left:0;right:0;bottom:0;background:rgba(0,0,0,0.4)}
        .hero-content{position:relative;z-index:10;text-align:center;max-width:64rem;margin:0 auto;padding:1rem}
        .hero-title{font-size:3rem;font-weight:bold;margin-bottom:1.5rem;font-family:"Playfair Display",serif}
        .hero-subtitle{font-size:1.25rem;margin-bottom:2rem}
        .hero-description{font-size:1.125rem;margin-bottom:3rem;max-width:42rem;margin-left:auto;margin-right:auto;opacity:0.9}
        .btn-primary{background:#d97706;color:white;padding:1rem 2rem;border-radius:9999px;font-weight:600;transition:all 0.3s}
        .btn-primary:hover{background:#92400e}
        .btn-secondary{background:transparent;border:2px solid white;color:white;padding:1rem 2rem;border-radius:9999px;font-weight:600;transition:all 0.3s}
        .btn-secondary:hover{background:white;color:#1f2937}
        @media (min-width:768px){.hero-title{font-size:4.5rem}.hero-subtitle{font-size:1.5rem}}
        .skip-links{position:absolute;top:-40px;left:0;z-index:1000}
        .skip-link{position:absolute;top:0;left:-10000px;width:1px;height:1px;overflow:hidden}
        .skip-link:focus{position:relative;left:0;width:auto;height:auto;padding:0.5rem 1rem;background:#000;color:#fff;text-decoration:none;z-index:1001}
    ',
    'accommodation' => '
        .page-header{background:linear-gradient(to right,#d97706,#92400e);padding:6rem 0 4rem;color:white;text-align:center}
        .page-title{font-size:3rem;font-weight:bold;margin-bottom:1rem;font-family:"Playfair Display",serif}
        .page-subtitle{font-size:1.25rem;opacity:0.9}
        .accommodation-grid{display:grid;grid-template-columns:repeat(auto-fit,minmax(350px,1fr));gap:2rem;padding:4rem 0}
        .accommodation-card{background:white;border-radius:0.5rem;overflow:hidden;box-shadow:0 4px 6px rgba(0,0,0,0.1);transition:transform 0.3s}
        .accommodation-card:hover{transform:translateY(-4px)}
        @media (min-width:768px){.page-title{font-size:4rem}}
    ',
    default => '
        .page-header{background:linear-gradient(to right,#d97706,#92400e);padding:6rem 0 4rem;color:white;text-align:center}
        .page-title{font-size:3rem;font-weight:bold;margin-bottom:1rem;font-family:"Playfair Display",serif}
        .page-subtitle{font-size:1.25rem;opacity:0.9}
        @media (min-width:768px){.page-title{font-size:4rem}}
        .skip-links{position:absolute;top:-40px;left:0;z-index:1000}
        .skip-link{position:absolute;top:0;left:-10000px;width:1px;height:1px;overflow:hidden}
        .skip-link:focus{position:relative;left:0;width:auto;height:auto;padding:0.5rem 1rem;background:#000;color:#fff;text-decoration:none;z-index:1001}
    '
};
@endphp

@if($criticalCSS)
<style>
{!! $criticalCSS !!}
</style>
@endif
