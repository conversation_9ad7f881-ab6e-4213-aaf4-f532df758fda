@props([
    'title' => 'Find Us',
    'description' => null,
    'latitude' => -7.8767, // Approximate location for Selous/Nyerere
    'longitude' => 37.8767,
    'zoom' => 10,
    'marker' => true,
    'height' => '400px',
    'showDirections' => true,
    'showSatellite' => true,
    'contactInfo' => true,
])

@php
    $mapId = 'map-' . uniqid();
@endphp

<section class="py-8">
    @if($title || $description)
        <div class="mb-8 text-center">
            @if($title)
                <h2 class="text-2xl lg:text-3xl font-display font-bold text-stone-900 mb-4">
                    {{ $title }}
                </h2>
            @endif
            
            @if($description)
                <p class="text-lg text-stone-600 max-w-3xl mx-auto leading-relaxed">
                    {{ $description }}
                </p>
            @endif
        </div>
    @endif

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- Map Container -->
        <div class="lg:col-span-2">
            <div class="relative rounded-2xl overflow-hidden shadow-medium border border-stone-200">
                <!-- Interactive Map Placeholder -->
                <div 
                    id="{{ $mapId }}"
                    class="w-full bg-stone-100"
                    style="height: {{ $height }}"
                    x-data="mapComponent()"
                    x-init="initMap()"
                >
                    <!-- Loading State -->
                    <div class="absolute inset-0 flex items-center justify-center bg-stone-100">
                        <div class="text-center">
                            <svg class="mx-auto h-12 w-12 text-stone-400 animate-spin" fill="none" viewBox="0 0 24 24">
                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                            <p class="mt-2 text-sm text-stone-600">Loading map...</p>
                        </div>
                    </div>
                </div>

                <!-- Map Controls -->
                <div class="absolute top-4 right-4 flex flex-col space-y-2">
                    @if($showSatellite)
                        <button
                            @click="toggleMapType()"
                            class="bg-white hover:bg-stone-50 text-stone-700 px-3 py-2 rounded-lg shadow-soft border border-stone-200 text-sm font-medium transition-colors duration-200"
                            title="Toggle satellite view"
                        >
                            <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L15 4m0 13V4m0 0L9 7"></path>
                            </svg>
                        </button>
                    @endif
                    
                    @if($showDirections)
                        <a
                            href="https://www.google.com/maps/dir/?api=1&destination={{ $latitude }},{{ $longitude }}"
                            target="_blank"
                            rel="noopener noreferrer"
                            class="bg-forest-600 hover:bg-forest-700 text-white px-3 py-2 rounded-lg shadow-soft text-sm font-medium transition-colors duration-200 flex items-center space-x-1"
                            title="Get directions"
                        >
                            <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L15 4m0 13V4m0 0L9 7"></path>
                            </svg>
                            <span>Directions</span>
                        </a>
                    @endif
                </div>
            </div>
        </div>

        <!-- Contact Information -->
        @if($contactInfo)
            <div class="space-y-6">
                <!-- Location Details -->
                <div class="bg-white rounded-xl p-6 shadow-soft border border-stone-200">
                    <h3 class="font-display font-semibold text-stone-900 mb-4 flex items-center">
                        <svg class="h-5 w-5 text-forest-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        </svg>
                        Location
                    </h3>
                    <div class="space-y-2 text-stone-600">
                        <p class="font-medium">{{ config('malombo.name') }}</p>
                        <p>{{ config('malombo.location.park') }}</p>
                        <p>{{ config('malombo.location.distance_to_gate') }}</p>
                        <p class="text-sm">Near {{ config('malombo.location.nearby_village') }}</p>
                    </div>
                </div>

                <!-- Contact Details -->
                <div class="bg-white rounded-xl p-6 shadow-soft border border-stone-200">
                    <h3 class="font-display font-semibold text-stone-900 mb-4 flex items-center">
                        <svg class="h-5 w-5 text-forest-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                        </svg>
                        Contact
                    </h3>
                    <div class="space-y-3">
                        @foreach(config('malombo.contact.phones') as $phone)
                            <div class="flex items-center space-x-2">
                                <svg class="h-4 w-4 text-stone-400 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                                </svg>
                                <a href="tel:{{ str_replace(' ', '', $phone) }}" class="text-stone-600 hover:text-forest-600 transition-colors duration-200">
                                    {{ $phone }}
                                </a>
                            </div>
                        @endforeach
                        
                        <div class="flex items-center space-x-2">
                            <svg class="h-4 w-4 text-stone-400 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                            </svg>
                            <a href="mailto:{{ config('malombo.contact.emails.info') }}" class="text-stone-600 hover:text-forest-600 transition-colors duration-200">
                                {{ config('malombo.contact.emails.info') }}
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Operating Hours -->
                <div class="bg-white rounded-xl p-6 shadow-soft border border-stone-200">
                    <h3 class="font-display font-semibold text-stone-900 mb-4 flex items-center">
                        <svg class="h-5 w-5 text-forest-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        Check-in Hours
                    </h3>
                    <div class="space-y-2 text-stone-600">
                        <div class="flex justify-between">
                            <span>Check-in:</span>
                            <span class="font-medium">{{ config('malombo.policies.check_in_start') }} - {{ config('malombo.policies.check_in_end') }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span>Check-out:</span>
                            <span class="font-medium">{{ config('malombo.policies.check_out') }}</span>
                        </div>
                    </div>
                </div>
            </div>
        @endif
    </div>

    <!-- Additional Content Slot -->
    @if(isset($slot) && !$slot->isEmpty())
        <div class="mt-8">
            {{ $slot }}
        </div>
    @endif
</section>

@push('scripts')
<script>
    function mapComponent() {
        return {
            map: null,
            mapType: 'roadmap',
            
            initMap() {
                // Initialize OpenStreetMap with Leaflet (free alternative to Google Maps)
                this.loadLeaflet().then(() => {
                    const container = document.getElementById('{{ $mapId }}');
                    container.innerHTML = ''; // Clear loading state
                    
                    this.map = L.map('{{ $mapId }}').setView([{{ $latitude }}, {{ $longitude }}], {{ $zoom }});
                    
                    // Add tile layer
                    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                        attribution: '© <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
                    }).addTo(this.map);
                    
                    @if($marker)
                        // Add marker
                        const marker = L.marker([{{ $latitude }}, {{ $longitude }}]).addTo(this.map);
                        marker.bindPopup('<b>{{ config('malombo.name') }}</b><br>{{ config('malombo.location.park') }}');
                    @endif
                });
            },
            
            loadLeaflet() {
                return new Promise((resolve) => {
                    if (window.L) {
                        resolve();
                        return;
                    }
                    
                    // Load Leaflet CSS
                    const link = document.createElement('link');
                    link.rel = 'stylesheet';
                    link.href = 'https://unpkg.com/leaflet@1.9.4/dist/leaflet.css';
                    document.head.appendChild(link);
                    
                    // Load Leaflet JS
                    const script = document.createElement('script');
                    script.src = 'https://unpkg.com/leaflet@1.9.4/dist/leaflet.js';
                    script.onload = resolve;
                    document.head.appendChild(script);
                });
            },
            
            toggleMapType() {
                if (!this.map) return;
                
                // Toggle between roadmap and satellite
                this.mapType = this.mapType === 'roadmap' ? 'satellite' : 'roadmap';
                
                // Remove existing layers
                this.map.eachLayer((layer) => {
                    if (layer instanceof L.TileLayer) {
                        this.map.removeLayer(layer);
                    }
                });
                
                // Add new layer based on type
                const tileUrl = this.mapType === 'satellite' 
                    ? 'https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}'
                    : 'https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png';
                    
                const attribution = this.mapType === 'satellite'
                    ? '© <a href="https://www.esri.com/">Esri</a>'
                    : '© <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors';
                
                L.tileLayer(tileUrl, { attribution }).addTo(this.map);
                
                @if($marker)
                    // Re-add marker
                    const marker = L.marker([{{ $latitude }}, {{ $longitude }}]).addTo(this.map);
                    marker.bindPopup('<b>{{ config('malombo.name') }}</b><br>{{ config('malombo.location.park') }}');
                @endif
            }
        };
    }
</script>
@endpush
