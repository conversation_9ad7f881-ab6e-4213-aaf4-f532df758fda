@props([
    'title' => 'Contact Us',
    'description' => 'Get in touch with us for any inquiries or bookings.',
    'action' => null,
    'method' => 'POST',
    'fields' => [], // Custom field configuration
    'showDefaultFields' => true,
    'variant' => 'default', // default, minimal, inline
    'size' => 'default', // small, default, large
])

@php
    $formAction = $action ?? route('contact.store');
    
    $containerClasses = match($size) {
        'small' => 'max-w-md',
        'default' => 'max-w-2xl',
        'large' => 'max-w-4xl',
        default => 'max-w-2xl'
    };

    $defaultFields = [
        'name' => [
            'type' => 'text',
            'label' => 'Full Name',
            'required' => true,
            'placeholder' => 'Your full name'
        ],
        'email' => [
            'type' => 'email',
            'label' => 'Email Address',
            'required' => true,
            'placeholder' => '<EMAIL>'
        ],
        'phone' => [
            'type' => 'tel',
            'label' => 'Phone Number',
            'required' => false,
            'placeholder' => '+****************'
        ],
        'subject' => [
            'type' => 'text',
            'label' => 'Subject',
            'required' => true,
            'placeholder' => 'What is this regarding?'
        ],
        'message' => [
            'type' => 'textarea',
            'label' => 'Message',
            'required' => true,
            'placeholder' => 'Tell us about your inquiry...',
            'rows' => 5
        ]
    ];

    $formFields = $showDefaultFields ? array_merge($defaultFields, $fields) : $fields;
@endphp

<section class="py-8">
    <div class="{{ $containerClasses }} mx-auto">
        
        @if($variant !== 'minimal')
            <!-- Header -->
            @if($title || $description)
                <div class="mb-8 text-center">
                    @if($title)
                        <h2 class="text-2xl lg:text-3xl font-display font-bold text-stone-900 mb-4">
                            {{ $title }}
                        </h2>
                    @endif
                    
                    @if($description)
                        <p class="text-lg text-stone-600 leading-relaxed">
                            {{ $description }}
                        </p>
                    @endif
                </div>
            @endif
        @endif

        <!-- Form -->
        <form 
            action="{{ $formAction }}" 
            method="{{ strtoupper($method) }}"
            class="bg-white rounded-2xl shadow-medium border border-stone-200 p-6 lg:p-8"
            x-data="contactForm()"
            @submit.prevent="submitForm()"
        >
            @csrf
            @if(strtoupper($method) !== 'GET' && strtoupper($method) !== 'POST')
                @method($method)
            @endif

            <!-- Success Message -->
            <div 
                x-show="showSuccess" 
                x-transition:enter="transition ease-out duration-300"
                x-transition:enter-start="opacity-0 transform -translate-y-2"
                x-transition:enter-end="opacity-100 transform translate-y-0"
                class="mb-6 bg-forest-50 border border-forest-200 text-forest-800 px-4 py-3 rounded-lg"
                style="display: none;"
            >
                <div class="flex items-center">
                    <svg class="h-5 w-5 text-forest-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                    <span x-text="successMessage"></span>
                </div>
            </div>

            <!-- Error Messages -->
            <div 
                x-show="showError" 
                x-transition:enter="transition ease-out duration-300"
                x-transition:enter-start="opacity-0 transform -translate-y-2"
                x-transition:enter-end="opacity-100 transform translate-y-0"
                class="mb-6 bg-red-50 border border-red-200 text-red-800 px-4 py-3 rounded-lg"
                style="display: none;"
            >
                <div class="flex items-center">
                    <svg class="h-5 w-5 text-red-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    <span x-text="errorMessage"></span>
                </div>
            </div>

            <div class="grid grid-cols-1 {{ $variant === 'inline' ? 'md:grid-cols-2' : '' }} gap-6">
                @foreach($formFields as $fieldName => $field)
                    @php
                        $fieldId = 'field_' . $fieldName;
                        $fieldClass = 'w-full px-4 py-3 border border-stone-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-forest-500 focus:border-transparent transition-all duration-200';
                        $fullWidth = in_array($field['type'], ['textarea', 'select']) || ($field['fullWidth'] ?? false);
                    @endphp

                    <div class="{{ $fullWidth && $variant === 'inline' ? 'md:col-span-2' : '' }}">
                        <label for="{{ $fieldId }}" class="block text-sm font-semibold text-stone-700 mb-2">
                            {{ $field['label'] }}
                            @if($field['required'] ?? false)
                                <span class="text-red-500">*</span>
                            @endif
                        </label>

                        @if($field['type'] === 'textarea')
                            <textarea
                                id="{{ $fieldId }}"
                                name="{{ $fieldName }}"
                                rows="{{ $field['rows'] ?? 4 }}"
                                class="{{ $fieldClass }} resize-none"
                                placeholder="{{ $field['placeholder'] ?? '' }}"
                                @if($field['required'] ?? false) required @endif
                                x-model="formData.{{ $fieldName }}"
                            ></textarea>
                        @elseif($field['type'] === 'select')
                            <select
                                id="{{ $fieldId }}"
                                name="{{ $fieldName }}"
                                class="{{ $fieldClass }}"
                                @if($field['required'] ?? false) required @endif
                                x-model="formData.{{ $fieldName }}"
                            >
                                <option value="">{{ $field['placeholder'] ?? 'Select an option' }}</option>
                                @foreach($field['options'] ?? [] as $value => $label)
                                    <option value="{{ $value }}">{{ $label }}</option>
                                @endforeach
                            </select>
                        @else
                            <input
                                type="{{ $field['type'] }}"
                                id="{{ $fieldId }}"
                                name="{{ $fieldName }}"
                                class="{{ $fieldClass }}"
                                placeholder="{{ $field['placeholder'] ?? '' }}"
                                @if($field['required'] ?? false) required @endif
                                @if(isset($field['min'])) min="{{ $field['min'] }}" @endif
                                @if(isset($field['max'])) max="{{ $field['max'] }}" @endif
                                x-model="formData.{{ $fieldName }}"
                            >
                        @endif

                        @if(isset($field['help']))
                            <p class="mt-1 text-sm text-stone-500">{{ $field['help'] }}</p>
                        @endif
                    </div>
                @endforeach
            </div>

            <!-- Additional Content Slot -->
            @if(isset($slot) && !$slot->isEmpty())
                <div class="mt-6">
                    {{ $slot }}
                </div>
            @endif

            <!-- Submit Button -->
            <div class="mt-8">
                <button
                    type="submit"
                    class="w-full bg-forest-600 hover:bg-forest-700 disabled:bg-stone-400 text-white px-8 py-4 rounded-xl font-semibold text-lg transition-all duration-300 shadow-medium hover:shadow-hard focus:outline-none focus:ring-4 focus:ring-forest-500/50 focus:ring-offset-2 transform hover:scale-105 disabled:transform-none disabled:cursor-not-allowed"
                    :disabled="isSubmitting"
                    :class="{ 'cursor-wait': isSubmitting }"
                >
                    <span x-show="!isSubmitting">Send Message</span>
                    <span x-show="isSubmitting" class="flex items-center justify-center" style="display: none;">
                        <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Sending...
                    </span>
                </button>
            </div>

            <!-- Privacy Notice -->
            <p class="mt-4 text-xs text-stone-500 text-center">
                By submitting this form, you agree to our 
                <a href="{{ route('privacy') }}" class="text-forest-600 hover:text-forest-700 underline">Privacy Policy</a> 
                and 
                <a href="{{ route('terms') }}" class="text-forest-600 hover:text-forest-700 underline">Terms of Service</a>.
            </p>
        </form>
    </div>
</section>

@push('scripts')
<script>
    function contactForm() {
        return {
            isSubmitting: false,
            showSuccess: false,
            showError: false,
            successMessage: '',
            errorMessage: '',
            formData: {
                @foreach($formFields as $fieldName => $field)
                    {{ $fieldName }}: '',
                @endforeach
            },

            async submitForm() {
                this.isSubmitting = true;
                this.showSuccess = false;
                this.showError = false;

                try {
                    const formData = new FormData();
                    formData.append('_token', document.querySelector('meta[name="csrf-token"]').getAttribute('content'));
                    
                    Object.keys(this.formData).forEach(key => {
                        formData.append(key, this.formData[key]);
                    });

                    const response = await fetch('{{ $formAction }}', {
                        method: '{{ strtoupper($method) }}',
                        body: formData,
                        headers: {
                            'X-Requested-With': 'XMLHttpRequest',
                        }
                    });

                    const data = await response.json();

                    if (response.ok) {
                        this.showSuccess = true;
                        this.successMessage = data.message || 'Your message has been sent successfully!';
                        this.resetForm();
                    } else {
                        throw new Error(data.message || 'Something went wrong. Please try again.');
                    }
                } catch (error) {
                    this.showError = true;
                    this.errorMessage = error.message;
                } finally {
                    this.isSubmitting = false;
                }
            },

            resetForm() {
                Object.keys(this.formData).forEach(key => {
                    this.formData[key] = '';
                });
            }
        };
    }
</script>
@endpush
