@props([
    'features' => [],
    'title' => null,
    'description' => null,
    'layout' => 'grid', // grid, list, inline
    'columns' => '3', // 2, 3, 4
    'iconStyle' => 'check', // check, arrow, star, custom
    'size' => 'default', // small, default, large
])

@php
    $containerClasses = match($layout) {
        'grid' => "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-{$columns} gap-6",
        'list' => 'space-y-4',
        'inline' => 'flex flex-wrap gap-4',
        default => "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-{$columns} gap-6"
    };

    $itemClasses = match($layout) {
        'grid' => 'flex items-start space-x-3',
        'list' => 'flex items-start space-x-3',
        'inline' => 'flex items-center space-x-2 bg-white px-4 py-2 rounded-full shadow-soft border border-stone-200',
        default => 'flex items-start space-x-3'
    };

    $iconClasses = match($size) {
        'small' => 'h-4 w-4',
        'default' => 'h-5 w-5',
        'large' => 'h-6 w-6',
        default => 'h-5 w-5'
    };

    $textClasses = match($size) {
        'small' => 'text-sm',
        'default' => 'text-base',
        'large' => 'text-lg',
        default => 'text-base'
    };
@endphp

<section class="py-8">
    @if($title || $description)
        <div class="mb-8 text-center">
            @if($title)
                <h2 class="text-2xl lg:text-3xl font-display font-bold text-stone-900 mb-4">
                    {{ $title }}
                </h2>
            @endif
            
            @if($description)
                <p class="text-lg text-stone-600 max-w-3xl mx-auto leading-relaxed">
                    {{ $description }}
                </p>
            @endif
        </div>
    @endif

    <div class="{{ $containerClasses }}">
        @foreach($features as $feature)
            <div class="{{ $itemClasses }}">
                <!-- Icon -->
                <div class="flex-shrink-0 mt-1">
                    @if($iconStyle === 'check')
                        <div class="bg-forest-100 text-forest-600 rounded-full p-1">
                            <svg class="{{ $iconClasses }}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                        </div>
                    @elseif($iconStyle === 'arrow')
                        <div class="bg-forest-100 text-forest-600 rounded-full p-1">
                            <svg class="{{ $iconClasses }}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                            </svg>
                        </div>
                    @elseif($iconStyle === 'star')
                        <div class="bg-accent-amber-100 text-accent-amber-600 rounded-full p-1">
                            <svg class="{{ $iconClasses }}" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"></path>
                            </svg>
                        </div>
                    @elseif($iconStyle === 'custom' && isset($feature['icon']))
                        <div class="bg-forest-100 text-forest-600 rounded-full p-1">
                            {!! $feature['icon'] !!}
                        </div>
                    @else
                        <div class="bg-forest-100 text-forest-600 rounded-full p-1">
                            <svg class="{{ $iconClasses }}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                        </div>
                    @endif
                </div>

                <!-- Content -->
                <div class="flex-1">
                    @if(is_array($feature))
                        <!-- Feature with title and description -->
                        @if(isset($feature['title']))
                            <h3 class="font-semibold text-stone-900 mb-1 {{ $textClasses }}">
                                {{ $feature['title'] }}
                            </h3>
                        @endif
                        
                        @if(isset($feature['description']))
                            <p class="text-stone-600 {{ $size === 'small' ? 'text-xs' : 'text-sm' }} leading-relaxed">
                                {{ $feature['description'] }}
                            </p>
                        @endif
                        
                        @if(isset($feature['text']) && !isset($feature['title']))
                            <span class="text-stone-700 font-medium {{ $textClasses }}">
                                {{ $feature['text'] }}
                            </span>
                        @endif
                    @else
                        <!-- Simple text feature -->
                        <span class="text-stone-700 font-medium {{ $textClasses }}">
                            {{ $feature }}
                        </span>
                    @endif
                </div>
            </div>
        @endforeach
    </div>

    <!-- Additional Content Slot -->
    @if(isset($slot) && !$slot->isEmpty())
        <div class="mt-8">
            {{ $slot }}
        </div>
    @endif
</section>
