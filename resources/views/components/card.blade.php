@props([
    'title' => '',
    'description' => '',
    'image' => null,
    'imageAlt' => '',
    'url' => '#',
    'price' => null,
    'priceLabel' => 'From',
    'currency' => 'USD',
    'features' => [],
    'variant' => 'default', // default, featured, compact
    'orientation' => 'vertical', // vertical, horizontal
])

@php
    $cardClasses = match($variant) {
        'featured' => 'bg-white rounded-2xl shadow-medium hover:shadow-hard border border-stone-200 overflow-hidden group transform hover:scale-105 transition-all duration-300',
        'compact' => 'bg-white rounded-xl shadow-soft hover:shadow-medium border border-stone-200 overflow-hidden group transition-all duration-200',
        default => 'bg-white rounded-xl shadow-soft hover:shadow-medium border border-stone-200 overflow-hidden group transition-all duration-200'
    };

    $imageClasses = $orientation === 'horizontal' 
        ? 'w-full md:w-1/3 h-48 md:h-full' 
        : 'w-full h-48 sm:h-56';
@endphp

<article class="{{ $cardClasses }}">
    <a 
        href="{{ $url }}" 
        class="block focus:outline-none focus:ring-4 focus:ring-forest-500/50 rounded-2xl"
        aria-label="View details for {{ $title }}"
    >
        <div class="{{ $orientation === 'horizontal' ? 'md:flex' : '' }}">
            
            <!-- Image Section -->
            @if($image)
                <div class="{{ $imageClasses }} relative overflow-hidden">
                    <img 
                        src="{{ $image }}" 
                        alt="{{ $imageAlt ?: $title }}"
                        class="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110"
                        loading="lazy"
                    >
                    
                    <!-- Price Badge -->
                    @if($price)
                        <div class="absolute top-4 right-4 bg-forest-600 text-white px-3 py-1 rounded-full text-sm font-semibold shadow-medium">
                            {{ $priceLabel }} ${{ number_format($price) }} {{ $currency }}
                        </div>
                    @endif
                    
                    <!-- Featured Badge -->
                    @if($variant === 'featured')
                        <div class="absolute top-4 left-4 bg-accent-amber-500 text-white px-3 py-1 rounded-full text-sm font-bold shadow-medium">
                            Featured
                        </div>
                    @endif
                </div>
            @endif

            <!-- Content Section -->
            <div class="{{ $orientation === 'horizontal' ? 'md:flex-1' : '' }} p-6 {{ $variant === 'compact' ? 'p-4' : '' }}">
                
                <!-- Title -->
                <h3 class="text-xl {{ $variant === 'featured' ? 'lg:text-2xl' : '' }} {{ $variant === 'compact' ? 'text-lg' : '' }} font-display font-semibold text-stone-900 mb-3 group-hover:text-forest-700 transition-colors duration-200">
                    {{ $title }}
                </h3>

                <!-- Description -->
                @if($description)
                    <p class="text-stone-600 leading-relaxed mb-4 {{ $variant === 'compact' ? 'text-sm mb-3' : '' }}">
                        {{ $description }}
                    </p>
                @endif

                <!-- Features List -->
                @if(count($features) > 0)
                    <ul class="space-y-2 mb-4 {{ $variant === 'compact' ? 'mb-3' : '' }}">
                        @foreach($features as $feature)
                            <li class="flex items-center text-sm text-stone-600">
                                <svg class="h-4 w-4 text-forest-500 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                {{ $feature }}
                            </li>
                        @endforeach
                    </ul>
                @endif

                <!-- Price (if no image) -->
                @if($price && !$image)
                    <div class="mb-4">
                        <span class="text-2xl {{ $variant === 'featured' ? 'text-3xl' : '' }} font-display font-bold text-stone-900">
                            ${{ number_format($price) }}
                        </span>
                        <span class="text-stone-600 ml-1">{{ $currency }}</span>
                        @if($priceLabel)
                            <span class="text-sm text-stone-500 block">{{ $priceLabel }}</span>
                        @endif
                    </div>
                @endif

                <!-- Additional Content Slot -->
                @if(isset($slot) && !$slot->isEmpty())
                    <div class="mb-4">
                        {{ $slot }}
                    </div>
                @endif

                <!-- CTA Arrow -->
                <div class="flex items-center justify-between">
                    <span class="text-forest-600 font-medium group-hover:text-forest-700 transition-colors duration-200">
                        View Details
                    </span>
                    <svg class="h-5 w-5 text-forest-600 group-hover:text-forest-700 group-hover:translate-x-1 transition-all duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                    </svg>
                </div>
            </div>
        </div>
    </a>
</article>
