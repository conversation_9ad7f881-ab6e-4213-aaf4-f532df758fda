@props([
    'src',
    'alt',
    'class' => '',
    'sizes' => '100vw',
    'loading' => 'lazy',
    'fetchpriority' => null,
    'width' => null,
    'height' => null,
    'aspectRatio' => null,
    'objectFit' => 'cover',
    'placeholder' => true,
])

@php
    // Generate responsive image URLs
    $baseUrl = str_starts_with($src, 'http') ? $src : asset($src);
    $filename = pathinfo($src, PATHINFO_FILENAME);
    $extension = pathinfo($src, PATHINFO_EXTENSION);
    $directory = pathinfo($src, PATHINFO_DIRNAME);
    
    // Generate srcset for different formats and sizes
    $breakpoints = [320, 640, 768, 1024, 1280, 1536, 1920];
    $formats = ['avif', 'webp', 'jpg'];
    
    $sources = [];
    foreach ($formats as $format) {
        $srcset = [];
        foreach ($breakpoints as $size) {
            if ($format === 'jpg' && $extension !== 'jpg') {
                $srcset[] = "{$directory}/{$filename}-{$size}w.{$extension} {$size}w";
            } else {
                $srcset[] = "{$directory}/{$filename}-{$size}w.{$format} {$size}w";
            }
        }
        $sources[$format] = implode(', ', $srcset);
    }
    
    // Aspect ratio classes
    $aspectClasses = [
        'square' => 'aspect-square',
        'landscape' => 'aspect-landscape',
        'portrait' => 'aspect-portrait',
        'widescreen' => 'aspect-widescreen',
        '16/9' => 'aspect-video',
        '4/3' => 'aspect-[4/3]',
        '3/2' => 'aspect-[3/2]',
    ];
    
    $aspectClass = $aspectRatio ? ($aspectClasses[$aspectRatio] ?? "aspect-[{$aspectRatio}]") : '';
    
    // Object fit classes
    $objectFitClass = match($objectFit) {
        'cover' => 'object-cover',
        'contain' => 'object-contain',
        'fill' => 'object-fill',
        'scale-down' => 'object-scale-down',
        'none' => 'object-none',
        default => 'object-cover',
    };
@endphp

<div class="relative {{ $aspectClass }} {{ $class }}" 
     @if($width) style="max-width: {{ $width }}px" @endif>
    
    @if($placeholder)
        {{-- Placeholder/skeleton while loading --}}
        <div class="absolute inset-0 bg-stone-200 animate-pulse rounded" 
             x-data="{ loaded: false }"
             x-show="!loaded"
             x-transition:leave="transition-opacity duration-300"
             x-transition:leave-start="opacity-100"
             x-transition:leave-end="opacity-0">
            <div class="flex items-center justify-center h-full">
                <svg class="w-8 h-8 text-stone-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd"/>
                </svg>
            </div>
        </div>
    @endif
    
    <picture class="block w-full h-full">
        {{-- AVIF format for modern browsers --}}
        <source srcset="{{ $sources['avif'] ?? '' }}" 
                sizes="{{ $sizes }}" 
                type="image/avif">
        
        {{-- WebP format for broader compatibility --}}
        <source srcset="{{ $sources['webp'] ?? '' }}" 
                sizes="{{ $sizes }}" 
                type="image/webp">
        
        {{-- Fallback to original format --}}
        <img src="{{ $baseUrl }}"
             alt="{{ $alt }}"
             class="w-full h-full {{ $objectFitClass }} transition-opacity duration-300"
             loading="{{ $loading }}"
             @if($fetchpriority) fetchpriority="{{ $fetchpriority }}" @endif
             @if($width) width="{{ $width }}" @endif
             @if($height) height="{{ $height }}" @endif
             sizes="{{ $sizes }}"
             @if($placeholder)
                x-data="{ loaded: false }"
                @load="loaded = true"
                x-show="loaded"
                x-transition:enter="transition-opacity duration-300"
                x-transition:enter-start="opacity-0"
                x-transition:enter-end="opacity-100"
             @endif
             decoding="async">
    </picture>
</div>
