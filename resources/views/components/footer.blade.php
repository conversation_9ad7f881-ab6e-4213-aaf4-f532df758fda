@props([
    'variant' => 'default' // default, minimal
])

<footer class="bg-stone-900 text-stone-300" role="contentinfo">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        @if($variant === 'default')
            <!-- Main Footer Content -->
            <div class="py-12 lg:py-16">
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 lg:gap-12">
                    
                    <!-- Brand Section -->
                    <div class="lg:col-span-2">
                        <div class="flex items-center space-x-3 mb-6">
                            <img 
                                class="h-12 w-auto" 
                                src="{{ asset('images/malombo_logo.jpg') }}" 
                                alt="Malombo Logo"
                            >
                            <div>
                                <h2 class="text-xl font-display font-semibold text-white">
                                    {{ config('malombo.name') }}
                                </h2>
                                <p class="text-sm text-stone-400 font-medium">
                                    {{ config('malombo.taglines.primary') }}
                                </p>
                            </div>
                        </div>
                        
                        <p class="text-stone-400 mb-6 max-w-md leading-relaxed">
                            Experience the ultimate luxury safari adventure in the heart of {{ config('malombo.location.park') }}. 
                            Your dream African safari awaits at our eco-luxury forest camp.
                        </p>
                        
                        <!-- Contact Info -->
                        <div class="space-y-4">
                            <div>
                                <h3 class="text-sm font-semibold text-white uppercase tracking-wider mb-2">Contact</h3>
                                <div class="space-y-2">
                                    @foreach(config('malombo.contact.phones') as $phone)
                                        <div class="flex items-center space-x-2">
                                            <svg class="h-4 w-4 text-forest-400 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                                            </svg>
                                            <a href="tel:{{ str_replace(' ', '', $phone) }}" class="text-stone-300 hover:text-white transition-colors duration-200">
                                                {{ $phone }}
                                            </a>
                                        </div>
                                    @endforeach
                                    
                                    <div class="flex items-center space-x-2">
                                        <svg class="h-4 w-4 text-forest-400 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                                        </svg>
                                        <a href="mailto:{{ config('malombo.contact.emails.info') }}" class="text-stone-300 hover:text-white transition-colors duration-200">
                                            {{ config('malombo.contact.emails.info') }}
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Links -->
                    <div>
                        <h3 class="text-sm font-semibold text-white uppercase tracking-wider mb-6">Quick Links</h3>
                        <nav class="space-y-3">
                            @foreach(App\Services\NavigationService::getFooterNavigation() as $item)
                                <a 
                                    href="{{ $item['url'] }}" 
                                    class="block text-stone-400 hover:text-white transition-colors duration-200 text-sm"
                                >
                                    {{ $item['label'] }}
                                </a>
                            @endforeach
                        </nav>
                    </div>

                    <!-- Services -->
                    <div>
                        <h3 class="text-sm font-semibold text-white uppercase tracking-wider mb-6">Our Services</h3>
                        <nav class="space-y-3">
                            <a href="{{ route('accommodation.index') }}" class="block text-stone-400 hover:text-white transition-colors duration-200 text-sm">
                                Luxury Accommodation
                            </a>
                            <a href="{{ route('activities.index') }}" class="block text-stone-400 hover:text-white transition-colors duration-200 text-sm">
                                Safari Activities
                            </a>
                            <a href="{{ route('facilities.index') }}" class="block text-stone-400 hover:text-white transition-colors duration-200 text-sm">
                                Fine Dining
                            </a>
                            <a href="{{ route('rates.index') }}" class="block text-stone-400 hover:text-white transition-colors duration-200 text-sm">
                                Safari Packages
                            </a>
                            <a href="{{ route('contact.index') }}" class="block text-stone-400 hover:text-white transition-colors duration-200 text-sm">
                                Book Now
                            </a>
                        </nav>
                    </div>
                </div>
            </div>

            <!-- Newsletter Section -->
            <div class="border-t border-stone-800 py-8">
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
                    <div>
                        <h3 class="text-lg font-semibold text-white mb-2">Stay Updated</h3>
                        <p class="text-stone-400 text-sm">Get the latest news and exclusive offers from Malombo Selous Forest Camp.</p>
                    </div>
                    <div>
                        <form action="{{ route('contact.index') }}" method="POST" class="flex space-x-3">
                            @csrf
                            <div class="flex-1">
                                <label for="newsletter-email" class="sr-only">Email address</label>
                                <input
                                    type="email"
                                    name="email"
                                    id="newsletter-email"
                                    required
                                    class="w-full px-4 py-3 bg-stone-800 border border-stone-700 rounded-lg text-white placeholder-stone-400 focus:outline-none focus:ring-2 focus:ring-forest-500 focus:border-transparent transition-all duration-200"
                                    placeholder="Enter your email"
                                >
                            </div>
                            <button
                                type="submit"
                                class="bg-forest-600 hover:bg-forest-700 text-white px-6 py-3 rounded-lg font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-forest-500 focus:ring-offset-2 focus:ring-offset-stone-900 whitespace-nowrap"
                            >
                                Subscribe
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        @endif

        <!-- Bottom Bar -->
        <div class="border-t border-stone-800 py-6">
            <div class="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
                
                <!-- Copyright -->
                <div class="text-stone-400 text-sm">
                    <p>&copy; {{ date('Y') }} {{ config('malombo.name') }}. All rights reserved.</p>
                </div>

                <!-- Social Links -->
                @if(config('malombo.social.facebook') || config('malombo.social.instagram') || config('malombo.social.twitter'))
                    <div class="flex space-x-6">
                        @if(config('malombo.social.facebook'))
                            <a 
                                href="{{ config('malombo.social.facebook') }}" 
                                target="_blank" 
                                rel="noopener noreferrer"
                                class="text-stone-400 hover:text-white transition-colors duration-200"
                                aria-label="Facebook"
                            >
                                <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                                </svg>
                            </a>
                        @endif

                        @if(config('malombo.social.instagram'))
                            <a 
                                href="{{ config('malombo.social.instagram') }}" 
                                target="_blank" 
                                rel="noopener noreferrer"
                                class="text-stone-400 hover:text-white transition-colors duration-200"
                                aria-label="Instagram"
                            >
                                <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 6.62 5.367 11.987 11.988 11.987 6.62 0 11.987-5.367 11.987-11.987C24.014 5.367 18.637.001 12.017.001zM8.449 16.988c-1.297 0-2.448-.49-3.328-1.297C4.243 14.894 3.752 13.743 3.752 12.446c0-1.297.49-2.448 1.369-3.328.88-.88 2.031-1.369 3.328-1.369 1.297 0 2.448.49 3.328 1.369.88.88 1.369 2.031 1.369 3.328 0 1.297-.49 2.448-1.369 3.328-.88.807-2.031 1.297-3.328 1.297zm7.718 0c-1.297 0-2.448-.49-3.328-1.297-.88-.88-1.369-2.031-1.369-3.328 0-1.297.49-2.448 1.369-3.328.88-.88 2.031-1.369 3.328-1.369 1.297 0 2.448.49 3.328 1.369.88.88 1.369 2.031 1.369 3.328 0 1.297-.49 2.448-1.369 3.328-.88.807-2.031 1.297-3.328 1.297z"/>
                                </svg>
                            </a>
                        @endif

                        @if(config('malombo.social.twitter'))
                            <a 
                                href="{{ config('malombo.social.twitter') }}" 
                                target="_blank" 
                                rel="noopener noreferrer"
                                class="text-stone-400 hover:text-white transition-colors duration-200"
                                aria-label="Twitter"
                            >
                                <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
                                </svg>
                            </a>
                        @endif
                    </div>
                @endif

                <!-- Legal Links -->
                <div class="flex space-x-6 text-sm">
                    <a 
                        href="{{ route('legal.privacy') }}" 
                        class="text-stone-400 hover:text-white transition-colors duration-200"
                    >
                        Privacy Policy
                    </a>
                    <a 
                        href="{{ route('legal.terms') }}" 
                        class="text-stone-400 hover:text-white transition-colors duration-200"
                    >
                        Terms of Service
                    </a>
                </div>
            </div>
        </div>
    </div>
</footer>
