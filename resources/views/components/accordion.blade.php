@props([
    'items' => [],
    'title' => null,
    'description' => null,
    'variant' => 'default', // default, minimal, bordered
    'allowMultiple' => false,
    'size' => 'default', // small, default, large
])

@php
    $accordionId = 'accordion-' . uniqid();
    
    $containerClasses = match($variant) {
        'minimal' => 'space-y-2',
        'bordered' => 'border border-stone-200 rounded-xl overflow-hidden',
        default => 'space-y-4'
    };

    $itemClasses = match($variant) {
        'minimal' => 'border-b border-stone-200 last:border-b-0',
        'bordered' => 'border-b border-stone-200 last:border-b-0',
        default => 'bg-white rounded-xl shadow-soft border border-stone-200 overflow-hidden'
    };

    $headerClasses = match($size) {
        'small' => 'px-4 py-3',
        'default' => 'px-6 py-4',
        'large' => 'px-8 py-6',
        default => 'px-6 py-4'
    };

    $contentClasses = match($size) {
        'small' => 'px-4 pb-3',
        'default' => 'px-6 pb-4',
        'large' => 'px-8 pb-6',
        default => 'px-6 pb-4'
    };
@endphp

<section class="py-8">
    @if($title || $description)
        <div class="mb-8 text-center">
            @if($title)
                <h2 class="text-2xl lg:text-3xl font-display font-bold text-stone-900 mb-4">
                    {{ $title }}
                </h2>
            @endif
            
            @if($description)
                <p class="text-lg text-stone-600 max-w-3xl mx-auto leading-relaxed">
                    {{ $description }}
                </p>
            @endif
        </div>
    @endif

    <div 
        class="max-w-4xl mx-auto"
        x-data="accordion({{ $allowMultiple ? 'true' : 'false' }})"
    >
        <div class="{{ $containerClasses }}">
            @foreach($items as $index => $item)
                @php
                    $itemId = $accordionId . '-item-' . $index;
                @endphp

                <div class="{{ $itemClasses }}">
                    <!-- Header -->
                    <button
                        type="button"
                        class="w-full {{ $headerClasses }} text-left flex items-center justify-between hover:bg-stone-50 focus:outline-none focus:bg-stone-50 transition-colors duration-200"
                        @click="toggleItem({{ $index }})"
                        :aria-expanded="openItems.includes({{ $index }})"
                        aria-controls="{{ $itemId }}-content"
                        id="{{ $itemId }}-header"
                    >
                        <h3 class="font-display font-semibold text-stone-900 text-lg pr-4">
                            {{ $item['question'] ?? $item['title'] ?? 'Question' }}
                        </h3>
                        
                        <svg 
                            class="h-5 w-5 text-stone-500 transition-transform duration-200 flex-shrink-0"
                            :class="{ 'rotate-180': openItems.includes({{ $index }}) }"
                            fill="none" 
                            stroke="currentColor" 
                            viewBox="0 0 24 24"
                        >
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                        </svg>
                    </button>

                    <!-- Content -->
                    <div
                        x-show="openItems.includes({{ $index }})"
                        x-transition:enter="transition ease-out duration-300"
                        x-transition:enter-start="opacity-0 transform -translate-y-2"
                        x-transition:enter-end="opacity-100 transform translate-y-0"
                        x-transition:leave="transition ease-in duration-200"
                        x-transition:leave-start="opacity-100 transform translate-y-0"
                        x-transition:leave-end="opacity-0 transform -translate-y-2"
                        class="{{ $contentClasses }}"
                        id="{{ $itemId }}-content"
                        aria-labelledby="{{ $itemId }}-header"
                        role="region"
                        style="display: none;"
                    >
                        <div class="text-stone-600 leading-relaxed prose prose-stone max-w-none">
                            @if(isset($item['answer']))
                                {!! $item['answer'] !!}
                            @elseif(isset($item['content']))
                                {!! $item['content'] !!}
                            @else
                                <p>No content provided.</p>
                            @endif
                        </div>

                        @if(isset($item['actions']) && count($item['actions']) > 0)
                            <div class="mt-4 pt-4 border-t border-stone-200">
                                <div class="flex flex-wrap gap-3">
                                    @foreach($item['actions'] as $action)
                                        <a
                                            href="{{ $action['url'] }}"
                                            class="inline-flex items-center text-sm font-medium text-forest-600 hover:text-forest-700 transition-colors duration-200"
                                            @if($action['external'] ?? false) 
                                                target="_blank" 
                                                rel="noopener noreferrer"
                                            @endif
                                        >
                                            {{ $action['label'] }}
                                            @if($action['external'] ?? false)
                                                <svg class="ml-1 h-3 w-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
                                                </svg>
                                            @else
                                                <svg class="ml-1 h-3 w-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                                                </svg>
                                            @endif
                                        </a>
                                    @endforeach
                                </div>
                            </div>
                        @endif
                    </div>
                </div>
            @endforeach
        </div>
    </div>

    <!-- Additional Content Slot -->
    @if(isset($slot) && !$slot->isEmpty())
        <div class="mt-8">
            {{ $slot }}
        </div>
    @endif
</section>

@push('scripts')
<script>
    function accordion(allowMultiple = false) {
        return {
            openItems: [],
            allowMultiple: allowMultiple,
            
            toggleItem(index) {
                if (this.allowMultiple) {
                    // Multiple items can be open
                    if (this.openItems.includes(index)) {
                        this.openItems = this.openItems.filter(item => item !== index);
                    } else {
                        this.openItems.push(index);
                    }
                } else {
                    // Only one item can be open at a time
                    if (this.openItems.includes(index)) {
                        this.openItems = [];
                    } else {
                        this.openItems = [index];
                    }
                }
            },
            
            openAll() {
                if (this.allowMultiple) {
                    this.openItems = Array.from({length: {{ count($items) }}}, (_, i) => i);
                }
            },
            
            closeAll() {
                this.openItems = [];
            }
        };
    }
</script>
@endpush
