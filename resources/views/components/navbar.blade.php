@props([
    'transparent' => false,
    'fixed' => true,
])

@php
    $baseClasses = $fixed 
        ? 'fixed top-0 left-0 right-0 z-50 transition-all duration-300' 
        : 'relative';
    
    $bgClasses = $transparent 
    <div class="flex items-center justify-between h-20">
        : 'bg-cream-90 backdrop-blur-sm border-b border-sand-200';
@endphp

    <nav 
        class="{{ $baseClasses }} {{ $bgClasses }} font-inter"
    x-data="{ 
        open: false, 
        scrolled: false,
        init() {
            if ({{ $fixed ? 'true' : 'false' }}) {
                this.updateScrollState();
                window.addEventListener('scroll', () => this.updateScrollState());
            }
        },
        updateScrollState() {
            this.scrolled = window.scrollY > 20;
        }
    }"
    :class="{ 
    'bg-cream-90 backdrop-blur-sm shadow-soft border-b border-sand-200': scrolled && {{ $transparent ? 'true' : 'false' }},
        'py-4': !scrolled,
        'py-2': scrolled
    }"
    role="navigation"
    aria-label="Main navigation"
>
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center h-16">
            <!-- Logo -->
            <div class="flex-shrink-0 flex items-center">
                <a 
                    href="{{ route('home') }}" 
                    class="group flex items-center space-x-3 focus:outline-none focus:ring-2 focus:ring-earth-600 focus:ring-offset-2 rounded-lg p-1"
                    aria-label="Malombo Safari Lodge - Home"
                >
                    <img 
                        class="h-10 w-auto sm:h-12 transition-transform duration-200 group-hover:scale-105 rounded-lg shadow-md" 
                        src="{{ asset('images/malombo_logo.jpg') }}" 
                        alt="Malombo Logo"
                    >
                    <div class="hidden sm:block">
                        <h1 class="text-lg sm:text-xl font-playfair font-semibold text-earth-900 group-hover:text-earth-700 transition-colors">
                            {{ config('malombo.name') }}
                        </h1>
                        <p class="text-xs sm:text-sm text-sand-700 font-medium">
                            {{ config('malombo.taglines.primary') }}
                        </p>
                    </div>
                </a>
            </div>

            <!-- Desktop Navigation -->
            <div class="hidden lg:block">
                <div class="ml-10 flex items-baseline space-x-8 font-inter">
                    @foreach(App\Services\NavigationService::getMainNavigation() as $item)
                        @if(isset($item['children']) && count($item['children']) > 0)
                            <!-- Dropdown Menu -->
                            <div class="relative" x-data="{ dropdownOpen: false }">
                                <button
                                    @click="dropdownOpen = !dropdownOpen"
                                    @keydown.escape="dropdownOpen = false"
                                    class="flex items-center text-stone-700 hover:text-forest-700 px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-forest-500 focus:ring-offset-2"
                                    :class="{ 'text-forest-700': dropdownOpen }"
                                    aria-expanded="false"
                                    :aria-expanded="dropdownOpen"
                                >
                                    {{ $item['label'] }}
                                    <svg class="ml-1 h-4 w-4 transition-transform duration-200" :class="{ 'rotate-180': dropdownOpen }" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                    </svg>
                                </button>
                                
                                <div
                                    x-show="dropdownOpen"
                                    x-transition:enter="transition ease-out duration-200"
                                    x-transition:enter-start="opacity-0 transform scale-95"
                                    x-transition:enter-end="opacity-100 transform scale-100"
                                    x-transition:leave="transition ease-in duration-150"
                                    x-transition:leave-start="opacity-100 transform scale-100"
                                    x-transition:leave-end="opacity-0 transform scale-95"
                                    @click.away="dropdownOpen = false"
                                    class="absolute right-0 mt-2 w-56 bg-cream-100 rounded-xl shadow-xl border border-sand-200 py-2 z-50 ring-1 ring-sand-300"
                                    style="display: none;"
                                >
                                    @foreach($item['children'] as $child)
                                        <a
                                            href="{{ $child['url'] }}"
                                            class="block px-4 py-2 text-sm text-earth-900 hover:bg-sand-50 hover:text-earth-700 transition-colors duration-150 font-inter rounded-lg"
                                            role="menuitem"
                                        >
                                            {{ $child['label'] }}
                                        </a>
                                    @endforeach
                                </div>
                            </div>
                        @else
                            <a
                                href="{{ $item['url'] }}"
                                class="text-earth-900 hover:text-earth-700 px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-earth-600 focus:ring-offset-2 {{ request()->routeIs($item['route']) ? 'text-earth-700 bg-sand-50' : '' }} font-inter"
                            >
                                {{ $item['label'] }}
                            </a>
                        @endif
                    @endforeach
                </div>
            </div>

            <!-- CTA Button & Mobile Menu Button -->
            <div class="flex items-center space-x-4">
                <!-- CTA Buttons -->
                <a
                    href="{{ route('contact.index') }}"
                    class="hidden sm:inline-flex bg-gradient-to-r from-earth-700 via-sand-500 to-earth-600 text-white px-6 py-2.5 rounded-xl font-semibold text-base shadow-lg hover:shadow-xl transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-earth-600 focus:ring-offset-2 transform hover:scale-105 border-2 border-earth-700"
                >
                    Enquire Now
                </a>
                <a
                    href="{{ route('booking.index') }}"
                    class="hidden sm:inline-flex bg-earth-600 hover:bg-earth-700 text-white px-6 py-2.5 rounded-xl font-semibold text-base transition-all duration-200 shadow-soft hover:shadow-medium focus:outline-none focus:ring-2 focus:ring-earth-600 focus:ring-offset-2 transform hover:scale-105 border-2 border-earth-600"
                >
                    Book Safari
                </a>

                <!-- Mobile menu button -->
                <button
                    @click="open = !open"
                    type="button"
                    class="lg:hidden inline-flex items-center justify-center p-2 rounded-md text-stone-700 hover:text-forest-700 hover:bg-stone-100 focus:outline-none focus:ring-2 focus:ring-forest-500 focus:ring-offset-2 transition-colors duration-200"
                    aria-controls="mobile-menu"
                    :aria-expanded="open"
                >
                    <span class="sr-only">Open main menu</span>
                    <!-- Menu icon -->
                    <svg 
                        x-show="!open" 
                        class="block h-6 w-6" 
                        fill="none" 
                        viewBox="0 0 24 24" 
                        stroke="currentColor"
                    >
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                    </svg>
                    <!-- Close icon -->
                    <svg 
                        x-show="open" 
                        class="block h-6 w-6" 
                        fill="none" 
                        viewBox="0 0 24 24" 
                        stroke="currentColor"
                        style="display: none;"
                    >
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
        </div>

        <!-- Mobile Navigation Menu -->
        <div 
            x-show="open"
            x-transition:enter="transition ease-out duration-200"
            x-transition:enter-start="opacity-0 transform -translate-y-2"
            x-transition:enter-end="opacity-100 transform translate-y-0"
            x-transition:leave="transition ease-in duration-150"
            x-transition:leave-start="opacity-100 transform translate-y-0"
            x-transition:leave-end="opacity-0 transform -translate-y-2"
            class="lg:hidden mt-4 pb-4 border-t border-stone-200"
            id="mobile-menu"
            style="display: none;"
        >
            <div class="px-2 pt-4 pb-3 space-y-1">
                @foreach(App\Services\NavigationService::getMainNavigation() as $item)
                    @if(isset($item['children']) && count($item['children']) > 0)
                        <!-- Mobile Dropdown -->
                        <div x-data="{ mobileDropdownOpen: false }">
                            <button
                                @click="mobileDropdownOpen = !mobileDropdownOpen"
                                class="w-full flex items-center justify-between text-stone-700 hover:text-forest-700 hover:bg-stone-50 px-3 py-2 rounded-md text-base font-medium transition-colors duration-200"
                                :class="{ 'text-forest-700 bg-stone-50': mobileDropdownOpen }"
                            >
                                {{ $item['label'] }}
                                <svg class="h-4 w-4 transition-transform duration-200" :class="{ 'rotate-180': mobileDropdownOpen }" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </button>
                            <div
                                x-show="mobileDropdownOpen"
                                x-transition:enter="transition ease-out duration-200"
                                x-transition:enter-start="opacity-0 transform -translate-y-1"
                                x-transition:enter-end="opacity-100 transform translate-y-0"
                                x-transition:leave="transition ease-in duration-150"
                                x-transition:leave-start="opacity-100 transform translate-y-0"
                                x-transition:leave-end="opacity-0 transform -translate-y-1"
                                class="ml-4 mt-2 space-y-1"
                                style="display: none;"
                            >
                                @foreach($item['children'] as $child)
                                    <a
                                        href="{{ $child['url'] }}"
                                        class="block text-stone-600 hover:text-forest-700 hover:bg-stone-50 px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200"
                                    >
                                        {{ $child['label'] }}
                                    </a>
                                @endforeach
                            </div>
                        </div>
                    @else
                        <a
                            href="{{ $item['url'] }}"
                            class="text-stone-700 hover:text-forest-700 hover:bg-stone-50 block px-3 py-2 rounded-md text-base font-medium transition-colors duration-200 {{ request()->routeIs($item['route']) ? 'text-forest-700 bg-forest-50' : '' }}"
                        >
                            {{ $item['label'] }}
                        </a>
                    @endif
                @endforeach
                
                <!-- Mobile CTA -->
                <div class="pt-4 border-t border-sand-200 flex flex-col gap-3">
                    <a
                        href="{{ route('contact.index') }}"
                        class="w-full flex justify-center bg-gradient-to-r from-earth-700 via-sand-500 to-earth-600 text-white px-6 py-3 rounded-xl font-semibold transition-all duration-200 shadow-lg hover:shadow-xl border-2 border-earth-700"
                    >
                        Enquire Now
                    </a>
                    <a
                        href="{{ route('booking.index') }}"
                        class="w-full flex justify-center bg-earth-600 hover:bg-earth-700 text-white px-6 py-3 rounded-xl font-semibold transition-all duration-200 shadow-soft hover:shadow-medium border-2 border-earth-600"
                    >
                        Book Safari
                    </a>
                </div>
            </div>
        </div>
    </div>
</nav>
