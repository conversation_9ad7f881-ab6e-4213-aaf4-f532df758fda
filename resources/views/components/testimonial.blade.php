@props([
    'quote' => '',
    'author' => '',
    'title' => '',
    'location' => '',
    'avatar' => null,
    'rating' => null,
    'date' => null,
    'variant' => 'default', // default, featured, compact
    'layout' => 'card', // card, quote, minimal
])

@php
    $containerClasses = match($variant) {
        'featured' => 'bg-gradient-to-br from-forest-50 to-stone-50 rounded-2xl p-8 lg:p-10 shadow-medium border border-forest-100',
        'compact' => 'bg-white rounded-xl p-6 shadow-soft border border-stone-200',
        default => 'bg-white rounded-xl p-6 lg:p-8 shadow-soft border border-stone-200'
    };

    $quoteSize = match($variant) {
        'featured' => 'text-lg lg:text-xl',
        'compact' => 'text-base',
        default => 'text-lg'
    };
@endphp

<article class="{{ $containerClasses }}">
    @if($layout === 'quote')
        <!-- Quote Layout -->
        <div class="relative">
            <!-- Quote Mark -->
            <svg class="absolute -top-2 -left-2 h-8 w-8 text-forest-200" fill="currentColor" viewBox="0 0 32 32">
                <path d="M9.352 4C4.456 7.456 1 13.12 1 19.36c0 5.088 3.072 8.064 6.624 8.064 3.36 0 5.856-2.688 5.856-5.856 0-3.168-2.208-5.472-5.088-5.472-.576 0-1.344.096-1.536.192.48-3.264 3.552-7.104 6.624-9.024L9.352 4zm16.512 0c-4.8 3.456-8.256 9.12-8.256 15.36 0 5.088 3.072 8.064 6.624 8.064 3.264 0 5.856-2.688 5.856-5.856 0-3.168-2.112-5.472-5.088-5.472-.576 0-1.248.096-1.44.192.48-3.264 3.456-7.104 6.528-9.024L25.864 4z"/>
            </svg>
            
            <!-- Quote Text -->
            <blockquote class="relative z-10 {{ $quoteSize }} text-stone-700 italic leading-relaxed mb-6">
                "{{ $quote }}"
            </blockquote>
        </div>
    @else
        <!-- Card/Minimal Layout -->
        @if($rating)
            <!-- Rating Stars -->
            <div class="flex items-center mb-4">
                @for($i = 1; $i <= 5; $i++)
                    <svg class="h-5 w-5 {{ $i <= $rating ? 'text-accent-amber-400' : 'text-stone-300' }}" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                    </svg>
                @endfor
                @if($rating)
                    <span class="ml-2 text-sm text-stone-600 font-medium">{{ $rating }}/5</span>
                @endif
            </div>
        @endif

        <!-- Quote Text -->
        <blockquote class="{{ $quoteSize }} text-stone-700 leading-relaxed mb-6">
            "{{ $quote }}"
        </blockquote>
    @endif

    <!-- Author Information -->
    <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
            @if($avatar)
                <img 
                    src="{{ $avatar }}" 
                    alt="{{ $author }}"
                    class="h-12 w-12 rounded-full object-cover shadow-soft"
                    loading="lazy"
                >
            @else
                <div class="h-12 w-12 bg-gradient-to-br from-forest-400 to-forest-600 rounded-full flex items-center justify-center shadow-soft">
                    <span class="text-white font-semibold text-lg">
                        {{ substr($author, 0, 1) }}
                    </span>
                </div>
            @endif
            
            <div>
                <cite class="not-italic">
                    <div class="font-semibold text-stone-900">{{ $author }}</div>
                    @if($title)
                        <div class="text-sm text-stone-600">{{ $title }}</div>
                    @endif
                    @if($location)
                        <div class="text-sm text-stone-500">{{ $location }}</div>
                    @endif
                </cite>
            </div>
        </div>

        @if($date)
            <time class="text-sm text-stone-500" datetime="{{ $date }}">
                {{ \Carbon\Carbon::parse($date)->format('M Y') }}
            </time>
        @endif
    </div>

    <!-- Additional Content Slot -->
    @if(isset($slot) && !$slot->isEmpty())
        <div class="mt-6 pt-6 border-t border-stone-200">
            {{ $slot }}
        </div>
    @endif
</article>
