@props([
    'images' => [],
    'title' => null,
    'description' => null,
    'layout' => 'masonry', // masonry, grid, carousel
    'columns' => '3', // 2, 3, 4
    'lightbox' => true,
    'lazy' => true,
])

@php
    $galleryId = 'gallery-' . uniqid();
    
    $containerClasses = match($layout) {
        'masonry' => 'columns-1 md:columns-2 lg:columns-' . $columns . ' gap-6 space-y-6',
        'grid' => 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-' . $columns . ' gap-6',
        'carousel' => 'flex space-x-6 overflow-x-auto pb-4 scrollbar-hide',
        default => 'columns-1 md:columns-2 lg:columns-' . $columns . ' gap-6 space-y-6'
    };
@endphp

<section class="py-8">
    @if($title || $description)
        <div class="mb-8 text-center">
            @if($title)
                <h2 class="text-2xl lg:text-3xl font-display font-bold text-stone-900 mb-4">
                    {{ $title }}
                </h2>
            @endif
            
            @if($description)
                <p class="text-lg text-stone-600 max-w-3xl mx-auto leading-relaxed">
                    {{ $description }}
                </p>
            @endif
        </div>
    @endif

    <div class="{{ $containerClasses }}" id="{{ $galleryId }}">
        @foreach($images as $index => $image)
            @php
                $imageData = is_array($image) ? $image : ['src' => $image];
                $src = $imageData['src'] ?? '';
                $alt = $imageData['alt'] ?? $imageData['title'] ?? 'Gallery image ' . ($index + 1);
                $title = $imageData['title'] ?? '';
                $caption = $imageData['caption'] ?? '';
                $aspectRatio = $imageData['aspect'] ?? 'auto';
            @endphp

            <figure 
                class="group cursor-pointer break-inside-avoid {{ $layout === 'carousel' ? 'flex-shrink-0 w-80' : '' }}"
                @if($lightbox)
                    x-data
                    @click="$dispatch('open-lightbox', { 
                        images: {{ json_encode($images) }}, 
                        current: {{ $index }} 
                    })"
                @endif
            >
                <div class="relative overflow-hidden rounded-xl shadow-soft hover:shadow-medium transition-all duration-300 group-hover:scale-105">
                    <img 
                        src="{{ $src }}"
                        alt="{{ $alt }}"
                        class="w-full h-auto object-cover transition-transform duration-500 group-hover:scale-110"
                        @if($lazy) loading="lazy" @endif
                        @if($title) title="{{ $title }}" @endif
                    >
                    
                    <!-- Overlay -->
                    <div class="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-all duration-300 flex items-center justify-center">
                        @if($lightbox)
                            <svg class="h-8 w-8 text-white opacity-0 group-hover:opacity-100 transition-opacity duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v3m0 0v3m0-3h3m-3 0H7"></path>
                            </svg>
                        @endif
                    </div>
                </div>

                @if($title || $caption)
                    <figcaption class="mt-3 px-2">
                        @if($title)
                            <h3 class="font-semibold text-stone-900 mb-1">{{ $title }}</h3>
                        @endif
                        @if($caption)
                            <p class="text-sm text-stone-600">{{ $caption }}</p>
                        @endif
                    </figcaption>
                @endif
            </figure>
        @endforeach
    </div>

    <!-- Additional Content Slot -->
    @if(isset($slot) && !$slot->isEmpty())
        <div class="mt-8">
            {{ $slot }}
        </div>
    @endif
</section>

@if($lightbox)
    <!-- Lightbox Modal -->
    <div 
        x-data="lightbox()"
        x-show="isOpen"
        x-transition:enter="transition ease-out duration-300"
        x-transition:enter-start="opacity-0"
        x-transition:enter-end="opacity-100"
        x-transition:leave="transition ease-in duration-200"
        x-transition:leave-start="opacity-100"
        x-transition:leave-end="opacity-0"
        @open-lightbox.window="openLightbox($event.detail)"
        @keydown.escape.window="closeLightbox()"
        class="fixed inset-0 z-100 flex items-center justify-center bg-black/90 p-4"
        style="display: none;"
    >
        <!-- Close Button -->
        <button 
            @click="closeLightbox()"
            class="absolute top-4 right-4 text-white hover:text-stone-300 transition-colors duration-200 z-10"
            aria-label="Close lightbox"
        >
            <svg class="h-8 w-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
        </button>

        <!-- Navigation -->
        <button 
            @click="previousImage()"
            x-show="images.length > 1"
            class="absolute left-4 top-1/2 transform -translate-y-1/2 text-white hover:text-stone-300 transition-colors duration-200 z-10"
            aria-label="Previous image"
        >
            <svg class="h-8 w-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
            </svg>
        </button>

        <button 
            @click="nextImage()"
            x-show="images.length > 1"
            class="absolute right-4 top-1/2 transform -translate-y-1/2 text-white hover:text-stone-300 transition-colors duration-200 z-10"
            aria-label="Next image"
        >
            <svg class="h-8 w-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
            </svg>
        </button>

        <!-- Image Container -->
        <div class="max-w-6xl max-h-full w-full h-full flex items-center justify-center">
            <img 
                :src="currentImage.src || currentImage"
                :alt="currentImage.alt || 'Lightbox image'"
                class="max-w-full max-h-full object-contain rounded-lg shadow-hard"
                @click.stop
            >
        </div>

        <!-- Image Counter -->
        <div 
            x-show="images.length > 1"
            class="absolute bottom-4 left-1/2 transform -translate-x-1/2 bg-black/50 text-white px-4 py-2 rounded-full text-sm"
        >
            <span x-text="currentIndex + 1"></span> / <span x-text="images.length"></span>
        </div>
    </div>

    @push('scripts')
    <script>
        function lightbox() {
            return {
                isOpen: false,
                images: [],
                currentIndex: 0,
                
                get currentImage() {
                    return this.images[this.currentIndex] || {};
                },
                
                openLightbox({ images, current }) {
                    this.images = images;
                    this.currentIndex = current;
                    this.isOpen = true;
                    document.body.style.overflow = 'hidden';
                },
                
                closeLightbox() {
                    this.isOpen = false;
                    document.body.style.overflow = '';
                },
                
                nextImage() {
                    this.currentIndex = (this.currentIndex + 1) % this.images.length;
                },
                
                previousImage() {
                    this.currentIndex = this.currentIndex === 0 ? this.images.length - 1 : this.currentIndex - 1;
                }
            };
        }
    </script>
    @endpush
@endif

@push('head')
<style>
    .scrollbar-hide {
        -ms-overflow-style: none;
        scrollbar-width: none;
    }
    .scrollbar-hide::-webkit-scrollbar {
        display: none;
    }
    
    /* Masonry layout optimization */
    .columns-1 { columns: 1; }
    .columns-2 { columns: 2; }
    .columns-3 { columns: 3; }
    .columns-4 { columns: 4; }
    
    @media (min-width: 768px) {
        .md\:columns-2 { columns: 2; }
        .md\:columns-3 { columns: 3; }
        .md\:columns-4 { columns: 4; }
    }
    
    @media (min-width: 1024px) {
        .lg\:columns-2 { columns: 2; }
        .lg\:columns-3 { columns: 3; }
        .lg\:columns-4 { columns: 4; }
    }
</style>
@endpush
