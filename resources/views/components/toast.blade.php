@props([
    'type' => 'success', // success, error, warning, info
    'title' => null,
    'message' => '',
    'duration' => 5000,
    'closable' => true,
    'position' => 'top-right', // top-right, top-left, bottom-right, bottom-left, top-center, bottom-center
])

@php
    $typeClasses = match($type) {
        'success' => 'bg-forest-50 border-forest-200 text-forest-800',
        'error' => 'bg-red-50 border-red-200 text-red-800',
        'warning' => 'bg-accent-amber-50 border-accent-amber-200 text-accent-amber-800',
        'info' => 'bg-sky-50 border-sky-200 text-sky-800',
        default => 'bg-forest-50 border-forest-200 text-forest-800'
    };

    $iconClasses = match($type) {
        'success' => 'text-forest-600',
        'error' => 'text-red-600',
        'warning' => 'text-accent-amber-600',
        'info' => 'text-sky-600',
        default => 'text-forest-600'
    };

    $progressClasses = match($type) {
        'success' => 'bg-forest-600',
        'error' => 'bg-red-600',
        'warning' => 'bg-accent-amber-600',
        'info' => 'bg-sky-600',
        default => 'bg-forest-600'
    };

    $toastId = 'toast-' . uniqid();
@endphp

<div 
    x-data="toast({{ $duration }}, '{{ $toastId }}')"
    x-show="show"
    x-transition:enter="transition ease-out duration-300"
    x-transition:enter-start="opacity-0 transform translate-x-full"
    x-transition:enter-end="opacity-100 transform translate-x-0"
    x-transition:leave="transition ease-in duration-200"
    x-transition:leave-start="opacity-100 transform translate-x-0"
    x-transition:leave-end="opacity-0 transform translate-x-full"
    class="relative max-w-sm w-full {{ $typeClasses }} border rounded-xl shadow-medium overflow-hidden"
    style="display: none;"
    id="{{ $toastId }}"
    role="alert"
    aria-live="polite"
    aria-atomic="true"
>
    <!-- Content -->
    <div class="p-4">
        <div class="flex items-start space-x-3">
            <!-- Icon -->
            <div class="flex-shrink-0">
                @if($type === 'success')
                    <svg class="h-5 w-5 {{ $iconClasses }}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                @elseif($type === 'error')
                    <svg class="h-5 w-5 {{ $iconClasses }}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                @elseif($type === 'warning')
                    <svg class="h-5 w-5 {{ $iconClasses }}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
                    </svg>
                @elseif($type === 'info')
                    <svg class="h-5 w-5 {{ $iconClasses }}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                @endif
            </div>

            <!-- Text Content -->
            <div class="flex-1 min-w-0">
                @if($title)
                    <h4 class="text-sm font-semibold mb-1">
                        {{ $title }}
                    </h4>
                @endif
                
                <p class="text-sm {{ $title ? '' : 'font-medium' }}">
                    {{ $message }}
                </p>

                <!-- Additional Content Slot -->
                @if(isset($slot) && !$slot->isEmpty())
                    <div class="mt-2">
                        {{ $slot }}
                    </div>
                @endif
            </div>

            <!-- Close Button -->
            @if($closable)
                <button
                    type="button"
                    @click="close()"
                    class="flex-shrink-0 {{ $iconClasses }} hover:opacity-75 transition-opacity duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-current rounded"
                    aria-label="Close notification"
                >
                    <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            @endif
        </div>
    </div>

    <!-- Progress Bar -->
    @if($duration > 0)
        <div class="absolute bottom-0 left-0 right-0 h-1 bg-black/10">
            <div 
                class="h-full {{ $progressClasses }} transition-all ease-linear"
                :style="{ width: progressWidth + '%' }"
            ></div>
        </div>
    @endif
</div>

@once
    @push('scripts')
    <script>
        function toast(duration = 5000, toastId = null) {
            return {
                show: false,
                progressWidth: 100,
                progressInterval: null,
                
                init() {
                    this.show = true;
                    
                    if (duration > 0) {
                        this.startProgress();
                        setTimeout(() => {
                            this.close();
                        }, duration);
                    }
                },
                
                startProgress() {
                    const step = 100 / (duration / 100);
                    this.progressInterval = setInterval(() => {
                        this.progressWidth -= step;
                        if (this.progressWidth <= 0) {
                            clearInterval(this.progressInterval);
                        }
                    }, 100);
                },
                
                close() {
                    this.show = false;
                    if (this.progressInterval) {
                        clearInterval(this.progressInterval);
                    }
                    
                    // Remove from DOM after animation
                    setTimeout(() => {
                        const element = document.getElementById(toastId);
                        if (element) {
                            element.remove();
                        }
                    }, 200);
                }
            };
        }

        // Global toast function for easy usage
        window.showToast = function(type, message, title = null, duration = 5000) {
            const container = document.getElementById('toast-container');
            if (!container) return;

            const toastId = 'toast-' + Date.now();
            const toast = document.createElement('div');
            
            // Build toast HTML
            let iconSvg = '';
            let typeClasses = '';
            let iconClasses = '';
            let progressClasses = '';
            
            switch(type) {
                case 'success':
                    typeClasses = 'bg-forest-50 border-forest-200 text-forest-800';
                    iconClasses = 'text-forest-600';
                    progressClasses = 'bg-forest-600';
                    iconSvg = '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>';
                    break;
                case 'error':
                    typeClasses = 'bg-red-50 border-red-200 text-red-800';
                    iconClasses = 'text-red-600';
                    progressClasses = 'bg-red-600';
                    iconSvg = '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>';
                    break;
                case 'warning':
                    typeClasses = 'bg-accent-amber-50 border-accent-amber-200 text-accent-amber-800';
                    iconClasses = 'text-accent-amber-600';
                    progressClasses = 'bg-accent-amber-600';
                    iconSvg = '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>';
                    break;
                case 'info':
                    typeClasses = 'bg-sky-50 border-sky-200 text-sky-800';
                    iconClasses = 'text-sky-600';
                    progressClasses = 'bg-sky-600';
                    iconSvg = '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>';
                    break;
                default:
                    typeClasses = 'bg-forest-50 border-forest-200 text-forest-800';
                    iconClasses = 'text-forest-600';
                    progressClasses = 'bg-forest-600';
                    iconSvg = '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>';
            }

            toast.innerHTML = `
                <div 
                    x-data="toast(${duration}, '${toastId}')"
                    x-show="show"
                    x-transition:enter="transition ease-out duration-300"
                    x-transition:enter-start="opacity-0 transform translate-x-full"
                    x-transition:enter-end="opacity-100 transform translate-x-0"
                    x-transition:leave="transition ease-in duration-200"
                    x-transition:leave-start="opacity-100 transform translate-x-0"
                    x-transition:leave-end="opacity-0 transform translate-x-full"
                    class="relative max-w-sm w-full ${typeClasses} border rounded-xl shadow-medium overflow-hidden"
                    id="${toastId}"
                    role="alert"
                    aria-live="polite"
                    aria-atomic="true"
                >
                    <div class="p-4">
                        <div class="flex items-start space-x-3">
                            <div class="flex-shrink-0">
                                <svg class="h-5 w-5 ${iconClasses}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    ${iconSvg}
                                </svg>
                            </div>
                            <div class="flex-1 min-w-0">
                                ${title ? `<h4 class="text-sm font-semibold mb-1">${title}</h4>` : ''}
                                <p class="text-sm ${title ? '' : 'font-medium'}">${message}</p>
                            </div>
                            <button
                                type="button"
                                @click="close()"
                                class="flex-shrink-0 ${iconClasses} hover:opacity-75 transition-opacity duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-current rounded"
                                aria-label="Close notification"
                            >
                                <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                </svg>
                            </button>
                        </div>
                    </div>
                    ${duration > 0 ? `
                        <div class="absolute bottom-0 left-0 right-0 h-1 bg-black/10">
                            <div 
                                class="h-full ${progressClasses} transition-all ease-linear"
                                :style="{ width: progressWidth + '%' }"
                            ></div>
                        </div>
                    ` : ''}
                </div>
            `;

            container.appendChild(toast);
        };
    </script>
    @endpush
@endonce
