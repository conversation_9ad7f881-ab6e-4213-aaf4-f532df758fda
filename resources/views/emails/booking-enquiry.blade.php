@component('mail::message')
# New Booking Enquiry

We've received a new booking enquiry from **{{ $enquiry->name }}**.

## Guest Information
- **Name:** {{ $enquiry->name }}
- **Email:** {{ $enquiry->email }}
@if($formData['phone'])
- **Phone:** {{ $formData['phone'] }}
@endif
@if($formData['country'])
- **Country:** {{ $formData['country'] }}
@endif

## Travel Details
@if($enquiry->check_in && $enquiry->check_out)
- **Arrival:** {{ \Carbon\Carbon::parse($enquiry->check_in)->format('l, F j, Y') }}
- **Departure:** {{ \Carbon\Carbon::parse($enquiry->check_out)->format('l, F j, Y') }}
- **Duration:** {{ $nights }} {{ $nights === 1 ? 'night' : 'nights' }}
@endif

## Group Details
- **Adults:** {{ $enquiry->adults }}
@if($enquiry->children > 0)
- **Children:** {{ $enquiry->children }}
@endif

## Preferences
@if($formData['accommodation_preference'])
- **Preferred Accommodation:** {{ $formData['accommodation_preference'] }}
@endif
@if($activityNames)
- **Activities of Interest:** {{ $activityNames }}
@endif
@if($formData['budget_range'])
- **Budget Range:** {{ ucfirst(str_replace('-', ' ', $formData['budget_range'])) }}
@endif

@if($formData['special_requirements'])
## Special Requirements
{{ $formData['special_requirements'] }}
@endif

@if($formData['message'])
## Additional Message
{{ $formData['message'] }}
@endif

@if($formData['how_did_you_hear'])
**How they heard about us:** {{ ucfirst(str_replace('_', ' ', $formData['how_did_you_hear'])) }}
@endif

---

**Enquiry Reference:** ENQ-{{ $enquiry->id }}  
**Submitted:** {{ $enquiry->created_at->format('F j, Y \a\t g:i A') }}

@component('mail::button', ['url' => route('filament.admin.resources.enquiries.view', $enquiry->id)])
View in Admin Panel
@endcomponent

Thanks,<br>
{{ config('app.name') }} Booking System
@endcomponent
