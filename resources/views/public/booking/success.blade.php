@extends('layouts.app')

@section('title', 'Booking Enquiry Submitted')
@section('meta_description', 'Thank you for your safari booking enquiry. Our team will contact you within 24 hours.')

@section('content')
<div class="min-h-screen bg-gray-50 py-16">
    <div class="max-w-4xl mx-auto px-6">
        {{-- Success Header --}}
        <div class="text-center mb-12">
            <div class="inline-flex items-center justify-center w-20 h-20 bg-green-100 rounded-full mb-6">
                <svg class="w-10 h-10 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
            </div>
            <h1 class="text-4xl font-bold text-gray-900 mb-4">Enquiry Submitted Successfully!</h1>
            <p class="text-xl text-gray-600 max-w-2xl mx-auto">
                Thank you for your interest in Malombo Private Game Reserve. Our safari specialists will contact you within 24 hours.
            </p>
        </div>

        @if($enquiry)
            {{-- Enquiry Details --}}
            <div class="bg-white rounded-xl shadow-lg p-8 mb-8">
                <div class="border-b border-gray-200 pb-6 mb-6">
                    <h2 class="text-2xl font-bold text-gray-900 mb-2">Your Enquiry Details</h2>
                    <p class="text-sm text-gray-500">Reference: <span class="font-mono font-medium">ENQ-{{ $enquiry->id }}</span></p>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                    {{-- Guest Information --}}
                    <div>
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">Guest Information</h3>
                        <div class="space-y-3">
                            <div class="flex justify-between">
                                <span class="text-gray-600">Name:</span>
                                <span class="font-medium">{{ $enquiry->name }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">Email:</span>
                                <span class="font-medium">{{ $enquiry->email }}</span>
                            </div>
                            @if($enquiry->phone)
                                <div class="flex justify-between">
                                    <span class="text-gray-600">Phone:</span>
                                    <span class="font-medium">{{ $enquiry->phone }}</span>
                                </div>
                            @endif
                            @if($enquiry->country)
                                <div class="flex justify-between">
                                    <span class="text-gray-600">Country:</span>
                                    <span class="font-medium">{{ $enquiry->country }}</span>
                                </div>
                            @endif
                        </div>
                    </div>

                    {{-- Travel Details --}}
                    <div>
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">Travel Details</h3>
                        <div class="space-y-3">
                            @if($enquiry->check_in)
                                <div class="flex justify-between">
                                    <span class="text-gray-600">Arrival:</span>
                                    <span class="font-medium">{{ \Carbon\Carbon::parse($enquiry->check_in)->format('F j, Y') }}</span>
                                </div>
                            @endif
                            @if($enquiry->check_out)
                                <div class="flex justify-between">
                                    <span class="text-gray-600">Departure:</span>
                                    <span class="font-medium">{{ \Carbon\Carbon::parse($enquiry->check_out)->format('F j, Y') }}</span>
                                </div>
                            @endif
                            <div class="flex justify-between">
                                <span class="text-gray-600">Guests:</span>
                                <span class="font-medium">{{ $enquiry->adults }} adults{{ $enquiry->children > 0 ? ', ' . $enquiry->children . ' children' : '' }}</span>
                            </div>
                        </div>
                    </div>
                </div>

                @if($enquiry->accommodation_type || $enquiry->message)
                    <div class="border-t border-gray-200 pt-6 mt-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">Preferences</h3>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            @if($enquiry->accommodation_type)
                                <div class="flex justify-between">
                                    <span class="text-gray-600">Accommodation:</span>
                                    <span class="font-medium">{{ $enquiry->accommodation_type }}</span>
                                </div>
                            @endif
                        </div>
                    </div>
                @endif
            </div>
        @endif

        {{-- Next Steps --}}
        <div class="bg-white rounded-xl shadow-lg p-8 mb-8">
            <h2 class="text-2xl font-bold text-gray-900 mb-6">What Happens Next?</h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div class="text-center">
                    <div class="inline-flex items-center justify-center w-12 h-12 bg-blue-100 rounded-full mb-4">
                        <span class="text-lg font-bold text-blue-600">1</span>
                    </div>
                    <h3 class="font-semibold text-gray-900 mb-2">Review</h3>
                    <p class="text-sm text-gray-600">Our team reviews your enquiry within 24 hours</p>
                </div>
                <div class="text-center">
                    <div class="inline-flex items-center justify-center w-12 h-12 bg-green-100 rounded-full mb-4">
                        <span class="text-lg font-bold text-green-600">2</span>
                    </div>
                    <h3 class="font-semibold text-gray-900 mb-2">Proposal</h3>
                    <p class="text-sm text-gray-600">We create a personalized safari package for you</p>
                </div>
                <div class="text-center">
                    <div class="inline-flex items-center justify-center w-12 h-12 bg-purple-100 rounded-full mb-4">
                        <span class="text-lg font-bold text-purple-600">3</span>
                    </div>
                    <h3 class="font-semibold text-gray-900 mb-2">Contact</h3>
                    <p class="text-sm text-gray-600">A safari specialist contacts you directly</p>
                </div>
            </div>
        </div>

        {{-- Quick Actions --}}
        <div class="bg-gradient-to-r from-green-600 to-green-700 rounded-xl shadow-lg p-8 text-white text-center">
            <h2 class="text-2xl font-bold mb-4">Need Immediate Assistance?</h2>
            <p class="text-green-100 mb-6">Our team is available to answer any questions about your safari adventure.</p>
            
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                @php
                    $whatsappNumber = setting('whatsapp_number', '+27123456789');
                    $whatsappMessage = $enquiry 
                        ? "Hi! I've just submitted booking enquiry ENQ-{$enquiry->id} for {$enquiry->name}. I'd like to discuss my safari plans."
                        : "Hi! I've just submitted a booking enquiry and would like to discuss my safari plans.";
                @endphp
                
                <a href="https://wa.me/{{ str_replace(['+', ' ', '-'], '', $whatsappNumber) }}?text={{ urlencode($whatsappMessage) }}" 
                   class="inline-flex items-center justify-center px-6 py-3 bg-white text-green-600 font-semibold rounded-lg hover:bg-green-50 transition-colors duration-200">
                    <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488"/>
                    </svg>
                    WhatsApp Us
                </a>
                
                <a href="{{ route('public.lodges') }}" 
                   class="inline-flex items-center justify-center px-6 py-3 border-2 border-white text-white font-semibold rounded-lg hover:bg-white hover:text-green-600 transition-colors duration-200">
                    View Our Lodges
                </a>
            </div>
        </div>

        {{-- Return Home --}}
        <div class="text-center mt-8">
            <a href="{{ route('public.home') }}" 
               class="inline-flex items-center text-gray-600 hover:text-gray-900 font-medium">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"/>
                </svg>
                Return to Homepage
            </a>
        </div>
    </div>
</div>
@endsection
