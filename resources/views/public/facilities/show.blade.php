@extends('layouts.app')

@section('title', $seoData['title'])
@section('meta_description', $seoData['description'])
@section('canonical', $seoData['canonical'])

@section('content')
<div class="relative">
    <!-- Hero Section -->
    <section class="relative bg-gradient-to-b from-gray-900 to-gray-800 text-white py-20">
        <div class="absolute inset-0 bg-black opacity-50"></div>
        @if($facility->getFirstMediaUrl('featured_image'))
            <div class="absolute inset-0 bg-cover bg-center" style="background-image: url('{{ $facility->getFirstMediaUrl('featured_image') }}')"></div>
        @else
            <div class="absolute inset-0 bg-cover bg-center" style="background-image: url('{{ asset('images/malombo_logo.jpg') }}')"></div>
        @endif
        
        <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <!-- Breadcrumb -->
            <nav class="mb-8">
                <ol class="flex items-center space-x-2 text-sm">
                    <li><a href="{{ route('home') }}" class="hover:text-blue-300">Home</a></li>
                    <li class="text-gray-300">/</li>
                    <li><a href="{{ route('facilities.index') }}" class="hover:text-blue-300">Facilities</a></li>
                    <li class="text-gray-300">/</li>
                    <li class="text-blue-300">{{ $facility->title }}</li>
                </ol>
            </nav>

            <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                <div>
                    @if($facility->category)
                        <span class="bg-blue-600 text-white px-4 py-2 rounded-full text-sm font-medium mb-4 inline-block">
                            {{ ucfirst($facility->category) }}
                        </span>
                    @endif
                    
                    <h1 class="text-4xl md:text-5xl font-bold mb-6">
                        {{ $facility->title }}
                    </h1>
                    
                    <p class="text-xl mb-8 leading-relaxed">
                        {{ $facility->description }}
                    </p>

                    @if($facility->operating_hours)
                        <div class="flex items-center text-lg mb-4">
                            <svg class="w-6 h-6 mr-3 text-blue-300" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z"></path>
                            </svg>
                            Operating Hours: {{ $facility->operating_hours }}
                        </div>
                    @endif

                    @if($facility->booking_required)
                        <div class="flex items-center text-lg mb-8">
                            <svg class="w-6 h-6 mr-3 text-yellow-300" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                            </svg>
                            Reservation Required
                        </div>
                    @endif

                    <div class="flex flex-col sm:flex-row gap-4">
                        <a href="{{ route('contact.index') }}" 
                           class="bg-blue-600 text-white px-8 py-3 rounded-lg hover:bg-blue-700 transition-colors duration-200 text-center flex items-center justify-center">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                            </svg>
                            Get Information
                        </a>
                        
                        <a href="{{ route('booking.index') }}" 
                           class="bg-transparent border-2 border-white text-white px-8 py-3 rounded-lg hover:bg-white hover:text-gray-900 transition-colors duration-200 text-center">
                            Book Your Stay
                        </a>
                    </div>
                </div>

                <div class="lg:order-first">
                    @if($facility->getFirstMediaUrl('featured_image'))
                        <img src="{{ $facility->getFirstMediaUrl('featured_image') }}" 
                             alt="{{ $facility->title }}"
                             class="w-full h-96 object-cover rounded-lg shadow-2xl">
                    @endif
                </div>
            </div>
        </div>
    </section>

    <!-- Facility Details -->
    <section class="py-16 bg-white">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            @if($facility->features && is_array($facility->features))
                <div class="bg-blue-50 border-l-4 border-blue-400 p-6 mb-8">
                    <h3 class="text-lg font-semibold text-blue-800 mb-4">Features & Amenities</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
                        @foreach($facility->features as $feature)
                            <div class="flex items-center text-blue-700">
                                <svg class="w-5 h-5 mr-2 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                </svg>
                                {{ $feature }}
                            </div>
                        @endforeach
                    </div>
                </div>
            @endif

            @if($facility->additional_info)
                <div class="prose prose-lg max-w-none">
                    <h3 class="text-xl font-semibold text-gray-900 mb-4">Additional Information</h3>
                    <div class="text-gray-700">
                        {!! nl2br(e($facility->additional_info)) !!}
                    </div>
                </div>
            @endif
        </div>
    </section>

    <!-- Gallery -->
    @if($facility->getMedia('gallery')->count() > 0)
        <section class="py-16 bg-gray-50">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <h2 class="text-3xl font-bold text-center text-gray-900 mb-12">Gallery</h2>
                
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6" x-data="{ lightbox: null }">
                    @foreach($facility->getMedia('gallery') as $media)
                        <div class="relative group cursor-pointer" 
                             @click="lightbox = '{{ $media->getUrl() }}'">
                            <img src="{{ $media->getUrl('preview') }}" 
                                 alt="{{ $facility->title }} - Image {{ $loop->iteration }}"
                                 class="w-full h-64 object-cover rounded-lg shadow-lg group-hover:shadow-xl transition-shadow duration-300">
                            
                            <div class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-opacity duration-300 rounded-lg flex items-center justify-center">
                                <svg class="w-12 h-12 text-white opacity-0 group-hover:opacity-100 transition-opacity duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                </svg>
                            </div>
                        </div>
                    @endforeach

                    <!-- Lightbox -->
                    <div x-show="lightbox" 
                         x-transition:enter="transition ease-out duration-300" 
                         x-transition:enter-start="opacity-0" 
                         x-transition:enter-end="opacity-100"
                         x-transition:leave="transition ease-in duration-200" 
                         x-transition:leave-start="opacity-100" 
                         x-transition:leave-end="opacity-0"
                         class="fixed inset-0 bg-black bg-opacity-90 z-50 flex items-center justify-center p-4"
                         @click="lightbox = null">
                        
                        <img :src="lightbox" 
                             alt="{{ $facility->title }}"
                             class="max-w-full max-h-full object-contain"
                             @click.stop>
                        
                        <button @click="lightbox = null" 
                                class="absolute top-4 right-4 text-white hover:text-gray-300">
                            <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
        </section>
    @endif

    <!-- Related Facilities -->
    @if($relatedFacilities->count() > 0)
        <section class="py-16 bg-white">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <h2 class="text-3xl font-bold text-center text-gray-900 mb-12">Other Facilities</h2>
                
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                    @foreach($relatedFacilities as $relatedFacility)
                        <div class="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300">
                            <div class="relative h-48 bg-gray-200">
                                @if($relatedFacility->getFirstMediaUrl('featured_image'))
                                    <img src="{{ $relatedFacility->getFirstMediaUrl('featured_image', 'preview') }}" 
                                         alt="{{ $relatedFacility->title }}"
                                         class="w-full h-full object-cover">
                                @else
                                    <div class="w-full h-full bg-gradient-to-br from-blue-100 to-blue-200 flex items-center justify-center">
                                        <svg class="w-12 h-12 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M10.394 2.08a1 1 0 00-.788 0l-7 3a1 1 0 000 1.84L5.25 8.051a.999.999 0 01.356-.257l4-1.714a1 1 0 11.788 1.838L7.667 9.088l1.94.831a1 1 0 00.787 0l7-3a1 1 0 000-1.838l-7-3z"></path>
                                        </svg>
                                    </div>
                                @endif
                            </div>

                            <div class="p-6">
                                <h3 class="text-xl font-bold text-gray-900 mb-2">
                                    {{ $relatedFacility->title }}
                                </h3>
                                
                                <p class="text-gray-600 mb-4 line-clamp-2">
                                    {{ Str::limit($relatedFacility->description, 100) }}
                                </p>

                                <a href="{{ route('facilities.show', $relatedFacility->slug) }}" 
                                   class="inline-block bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors duration-200">
                                    Learn More
                                </a>
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
        </section>
    @endif

    <!-- Call to Action -->
    <section class="py-16 bg-blue-600 text-white">
        <div class="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
            <h2 class="text-3xl font-bold mb-4">
                Ready to Experience {{ $facility->title }}?
            </h2>
            <p class="text-xl mb-8">
                Contact our team to learn more about this facility and plan your perfect safari experience.
            </p>
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <a href="{{ route('contact.index') }}" 
                   class="bg-white text-blue-600 px-8 py-3 rounded-lg hover:bg-gray-100 transition-colors duration-200">
                    Contact Us
                </a>
                <a href="{{ route('booking.index') }}" 
                   class="bg-blue-700 text-white px-8 py-3 rounded-lg hover:bg-blue-800 transition-colors duration-200">
                    Book Now
                </a>
            </div>
        </div>
    </section>
</div>
@endsection
