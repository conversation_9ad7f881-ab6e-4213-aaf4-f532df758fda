@extends('layouts.app')

@section('title', $seoData['title'])
@section('meta_description', $seoData['description'])
@section('canonical', $seoData['canonical'])

@section('content')
<div class="relative">
    <!-- Hero Section -->
    <section class="relative bg-gradient-to-b from-gray-900 to-gray-800 text-white py-20">
        <div class="absolute inset-0 bg-black opacity-50"></div>
        <div class="absolute inset-0 bg-cover bg-center" style="background-image: url('{{ asset('images/malombo_logo.jpg') }}')"></div>
        
        <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h1 class="text-4xl md:text-6xl font-bold mb-6">
                Facilities & Amenities
            </h1>
            <p class="text-xl md:text-2xl mb-8 max-w-3xl mx-auto">
                Experience luxury and comfort in the heart of the African wilderness
            </p>
        </div>
    </section>

    <!-- Facilities Grid -->
    <section class="py-16 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            @if($facilities->count() > 0)
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                    @foreach($facilities as $facility)
                        <div class="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300">
                            <!-- Facility Image -->
                            <div class="relative h-64 bg-gray-200">
                                @if($facility->getFirstMediaUrl('featured_image'))
                                    <img src="{{ $facility->getFirstMediaUrl('featured_image', 'preview') }}" 
                                         alt="{{ $facility->title }}"
                                         class="w-full h-full object-cover">
                                @else
                                    <div class="w-full h-full bg-gradient-to-br from-blue-100 to-blue-200 flex items-center justify-center">
                                        <svg class="w-16 h-16 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M10.394 2.08a1 1 0 00-.788 0l-7 3a1 1 0 000 1.84L5.25 8.051a.999.999 0 01.356-.257l4-1.714a1 1 0 11.788 1.838L7.667 9.088l1.94.831a1 1 0 00.787 0l7-3a1 1 0 000-1.838l-7-3zM3.31 9.397L5 10.12v4.102a8.969 8.969 0 00-1.05-.174 1 1 0 01-.89-.89 11.115 11.115 0 01.25-3.762zM9.3 16.573A9.026 9.026 0 007 14.935v-3.957l1.818.78a3 3 0 002.364 0l5.508-2.361a11.026 11.026 0 01.25 3.762 1 1 0 01-.89.89 8.968 8.968 0 00-5.35 2.524 1 1 0 01-1.4 0zM6 18a1 1 0 001-1v-2.065a8.935 8.935 0 00-2-.712V17a1 1 0 001 1z"></path>
                                        </svg>
                                    </div>
                                @endif
                                
                                <!-- Category Badge -->
                                @if($facility->category)
                                    <div class="absolute top-4 left-4">
                                        <span class="bg-blue-600 text-white px-3 py-1 rounded-full text-sm font-medium">
                                            {{ ucfirst($facility->category) }}
                                        </span>
                                    </div>
                                @endif

                                @if($facility->is_featured)
                                    <div class="absolute top-4 right-4">
                                        <span class="bg-yellow-500 text-white px-2 py-1 rounded-full text-xs font-medium">
                                            Featured
                                        </span>
                                    </div>
                                @endif
                            </div>

                            <!-- Facility Content -->
                            <div class="p-6">
                                <h3 class="text-xl font-bold text-gray-900 mb-3">
                                    {{ $facility->title }}
                                </h3>
                                
                                <p class="text-gray-600 mb-4 line-clamp-3">
                                    {{ $facility->description }}
                                </p>

                                @if($facility->operating_hours)
                                    <div class="flex items-center text-sm text-gray-500 mb-4">
                                        <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z"></path>
                                        </svg>
                                        {{ $facility->operating_hours }}
                                    </div>
                                @endif

                                <div class="flex justify-between items-center">
                                    <a href="{{ route('facilities.show', $facility->slug) }}" 
                                       class="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors duration-200">
                                        Learn More
                                    </a>
                                    
                                    @if($facility->booking_required)
                                        <span class="text-sm text-orange-600 font-medium">
                                            Reservation Required
                                        </span>
                                    @endif
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>
            @else
                <div class="text-center py-16">
                    <svg class="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                    </svg>
                    <h3 class="text-xl font-medium text-gray-900 mb-2">No Facilities Listed</h3>
                    <p class="text-gray-500">Facility information will be added soon. Please check back later.</p>
                </div>
            @endif
        </div>
    </section>

    <!-- Categories Overview -->
    @if($facilities->count() > 0)
        <section class="py-16 bg-gray-50">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <h2 class="text-3xl font-bold text-center text-gray-900 mb-12">
                    Facility Categories
                </h2>
                
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    @php
                        $categories = $facilities->whereNotNull('category')->groupBy('category');
                    @endphp
                    
                    @foreach($categories as $category => $categoryFacilities)
                        <div class="bg-white rounded-lg p-6 text-center shadow-md hover:shadow-lg transition-shadow duration-300">
                            <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                <svg class="w-8 h-8 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M10.394 2.08a1 1 0 00-.788 0l-7 3a1 1 0 000 1.84L5.25 8.051a.999.999 0 01.356-.257l4-1.714a1 1 0 11.788 1.838L7.667 9.088l1.94.831a1 1 0 00.787 0l7-3a1 1 0 000-1.838l-7-3z"></path>
                                </svg>
                            </div>
                            <h3 class="text-lg font-semibold text-gray-900 mb-2">
                                {{ ucfirst($category) }}
                            </h3>
                            <p class="text-gray-600 text-sm">
                                {{ $categoryFacilities->count() }} {{ Str::plural('facility', $categoryFacilities->count()) }}
                            </p>
                        </div>
                    @endforeach
                </div>
            </div>
        </section>
    @endif

    <!-- Call to Action -->
    <section class="py-16 bg-blue-600 text-white">
        <div class="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
            <h2 class="text-3xl font-bold mb-4">
                Experience Luxury in the Wild
            </h2>
            <p class="text-xl mb-8">
                Our world-class facilities ensure your comfort while you enjoy the authentic African safari experience.
            </p>
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <a href="{{ route('contact.index') }}" 
                   class="bg-white text-blue-600 px-8 py-3 rounded-lg hover:bg-gray-100 transition-colors duration-200">
                    Contact Us
                </a>
                <a href="{{ route('booking.index') }}" 
                   class="bg-blue-700 text-white px-8 py-3 rounded-lg hover:bg-blue-800 transition-colors duration-200">
                    Book Your Stay
                </a>
            </div>
        </div>
    </section>
</div>
@endsection
