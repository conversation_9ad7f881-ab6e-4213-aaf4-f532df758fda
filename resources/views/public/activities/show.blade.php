@extends('layouts.app')

@section('title', $seoData['title'])
@section('meta_description', $seoData['description'])
@section('canonical', $seoData['canonical'])

@section('content')
<div class="relative">
    <!-- Hero Section -->
    <section class="relative bg-gradient-to-b from-gray-900 to-gray-800 text-white py-20">
        <div class="absolute inset-0 bg-black opacity-50"></div>
        @if($activity->getFirstMediaUrl('featured_image'))
            <div class="absolute inset-0 bg-cover bg-center" style="background-image: url('{{ $activity->getFirstMediaUrl('featured_image') }}')"></div>
        @else
            <div class="absolute inset-0 bg-cover bg-center" style="background-image: url('{{ asset('images/malombo_logo.jpg') }}')"></div>
        @endif
        
        <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <!-- Breadcrumb -->
            <nav class="mb-8">
                <ol class="flex items-center space-x-2 text-sm">
                    <li><a href="{{ route('home') }}" class="hover:text-green-300">Home</a></li>
                    <li class="text-gray-300">/</li>
                    <li><a href="{{ route('activities.index') }}" class="hover:text-green-300">Activities</a></li>
                    <li class="text-gray-300">/</li>
                    <li class="text-green-300">{{ $activity->title }}</li>
                </ol>
            </nav>

            <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                <div>
                    @if($activity->category)
                        <span class="bg-green-600 text-white px-4 py-2 rounded-full text-sm font-medium mb-4 inline-block">
                            {{ ucfirst($activity->category) }}
                        </span>
                    @endif
                    
                    <h1 class="text-4xl md:text-5xl font-bold mb-6">
                        {{ $activity->title }}
                    </h1>
                    
                    <p class="text-xl mb-8 leading-relaxed">
                        {{ $activity->description }}
                    </p>

                    @if($activity->duration)
                        <div class="flex items-center text-lg mb-8">
                            <svg class="w-6 h-6 mr-3 text-green-300" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z"></path>
                            </svg>
                            Duration: {{ $activity->duration }}
                        </div>
                    @endif

                    <div class="flex flex-col sm:flex-row gap-4" x-data="{ showModal: false }">
                        <button @click="showModal = true" 
                                class="bg-green-600 text-white px-8 py-3 rounded-lg hover:bg-green-700 transition-colors duration-200 flex items-center justify-center">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                            </svg>
                            Book This Activity
                        </button>
                        
                        <a href="{{ route('contact.index') }}" 
                           class="bg-transparent border-2 border-white text-white px-8 py-3 rounded-lg hover:bg-white hover:text-gray-900 transition-colors duration-200 text-center">
                            Ask Questions
                        </a>

                        <!-- Booking Modal -->
                        <div x-show="showModal" 
                             x-transition:enter="transition ease-out duration-300" 
                             x-transition:enter-start="opacity-0" 
                             x-transition:enter-end="opacity-100"
                             x-transition:leave="transition ease-in duration-200" 
                             x-transition:leave-start="opacity-100" 
                             x-transition:leave-end="opacity-0"
                             class="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4"
                             @click="showModal = false">
                            
                            <div class="bg-white rounded-lg max-w-md w-full p-6" @click.stop>
                                <div class="flex justify-between items-center mb-4">
                                    <h3 class="text-xl font-bold text-gray-900">Book {{ $activity->title }}</h3>
                                    <button @click="showModal = false" class="text-gray-400 hover:text-gray-600">
                                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                        </svg>
                                    </button>
                                </div>
                                
                                <p class="text-gray-600 mb-6">
                                    Contact us to book this amazing safari experience and get personalized assistance.
                                </p>
                                
                                <div class="flex gap-3">
                                    <a href="{{ route('contact.index') }}" 
                                       class="flex-1 bg-green-600 text-white text-center py-2 px-4 rounded-lg hover:bg-green-700 transition-colors duration-200">
                                        Contact Us
                                    </a>
                                    <a href="{{ route('booking.index') }}" 
                                       class="flex-1 bg-blue-600 text-white text-center py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors duration-200">
                                        Booking Form
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="lg:order-first">
                    @if($activity->getFirstMediaUrl('featured_image'))
                        <img src="{{ $activity->getFirstMediaUrl('featured_image') }}" 
                             alt="{{ $activity->title }}"
                             class="w-full h-96 object-cover rounded-lg shadow-2xl">
                    @endif
                </div>
            </div>
        </div>
    </section>

    <!-- Activity Details -->
    <section class="py-16 bg-white">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="prose prose-lg max-w-none">
                @if($activity->tips)
                    <div class="bg-green-50 border-l-4 border-green-400 p-6 mb-8">
                        <h3 class="text-lg font-semibold text-green-800 mb-2">Tips & Information</h3>
                        <div class="text-green-700">
                            {!! nl2br(e($activity->tips)) !!}
                        </div>
                    </div>
                @endif

                @if($activity->includes && is_array($activity->includes))
                    <div class="bg-blue-50 border-l-4 border-blue-400 p-6 mb-8">
                        <h3 class="text-lg font-semibold text-blue-800 mb-4">What's Included</h3>
                        <ul class="list-disc list-inside text-blue-700 space-y-2">
                            @foreach($activity->includes as $include)
                                <li>{{ $include }}</li>
                            @endforeach
                        </ul>
                    </div>
                @endif
            </div>
        </div>
    </section>

    <!-- Gallery -->
    @if($activity->getMedia('gallery')->count() > 0)
        <section class="py-16 bg-gray-50">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <h2 class="text-3xl font-bold text-center text-gray-900 mb-12">Gallery</h2>
                
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    @foreach($activity->getMedia('gallery') as $media)
                        <div class="relative group cursor-pointer" 
                             @click="lightbox = '{{ $media->getUrl() }}'"
                             x-data="{ lightbox: null }">
                            <img src="{{ $media->getUrl('preview') }}" 
                                 alt="{{ $activity->title }} - Image {{ $loop->iteration }}"
                                 class="w-full h-64 object-cover rounded-lg shadow-lg group-hover:shadow-xl transition-shadow duration-300">
                            
                            <div class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-opacity duration-300 rounded-lg flex items-center justify-center">
                                <svg class="w-12 h-12 text-white opacity-0 group-hover:opacity-100 transition-opacity duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                </svg>
                            </div>

                            <!-- Lightbox -->
                            <div x-show="lightbox" 
                                 x-transition:enter="transition ease-out duration-300" 
                                 x-transition:enter-start="opacity-0" 
                                 x-transition:enter-end="opacity-100"
                                 x-transition:leave="transition ease-in duration-200" 
                                 x-transition:leave-start="opacity-100" 
                                 x-transition:leave-end="opacity-0"
                                 class="fixed inset-0 bg-black bg-opacity-90 z-50 flex items-center justify-center p-4"
                                 @click="lightbox = null">
                                
                                <img :src="lightbox" 
                                     alt="{{ $activity->title }}"
                                     class="max-w-full max-h-full object-contain"
                                     @click.stop>
                                
                                <button @click="lightbox = null" 
                                        class="absolute top-4 right-4 text-white hover:text-gray-300">
                                    <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                    </svg>
                                </button>
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
        </section>
    @endif

    <!-- Related Activities -->
    @if($relatedActivities->count() > 0)
        <section class="py-16 bg-white">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <h2 class="text-3xl font-bold text-center text-gray-900 mb-12">Other Activities</h2>
                
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                    @foreach($relatedActivities as $relatedActivity)
                        <div class="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300">
                            <div class="relative h-48 bg-gray-200">
                                @if($relatedActivity->getFirstMediaUrl('featured_image'))
                                    <img src="{{ $relatedActivity->getFirstMediaUrl('featured_image', 'preview') }}" 
                                         alt="{{ $relatedActivity->title }}"
                                         class="w-full h-full object-cover">
                                @else
                                    <div class="w-full h-full bg-gradient-to-br from-green-100 to-green-200 flex items-center justify-center">
                                        <svg class="w-12 h-12 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M2 6a2 2 0 012-2h5l2 2h5a2 2 0 012 2v6a2 2 0 01-2 2H4a2 2 0 01-2-2V6z"></path>
                                        </svg>
                                    </div>
                                @endif
                            </div>

                            <div class="p-6">
                                <h3 class="text-xl font-bold text-gray-900 mb-2">
                                    {{ $relatedActivity->title }}
                                </h3>
                                
                                <p class="text-gray-600 mb-4 line-clamp-2">
                                    {{ Str::limit($relatedActivity->description, 100) }}
                                </p>

                                <a href="{{ route('activities.show', $relatedActivity->slug) }}" 
                                   class="inline-block bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors duration-200">
                                    Learn More
                                </a>
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
        </section>
    @endif

    <!-- Call to Action -->
    <section class="py-16 bg-green-600 text-white">
        <div class="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
            <h2 class="text-3xl font-bold mb-4">
                Ready to Experience {{ $activity->title }}?
            </h2>
            <p class="text-xl mb-8">
                Contact our team to customize your safari adventure and create unforgettable memories.
            </p>
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <a href="{{ route('contact.index') }}" 
                   class="bg-white text-green-600 px-8 py-3 rounded-lg hover:bg-gray-100 transition-colors duration-200">
                    Contact Us
                </a>
                <a href="{{ route('booking.index') }}" 
                   class="bg-green-700 text-white px-8 py-3 rounded-lg hover:bg-green-800 transition-colors duration-200">
                    Book Now
                </a>
            </div>
        </div>
    </section>
</div>
@endsection
