@extends('layouts.app')

@section('title', $seoData['title'])
@section('meta_description', $seoData['description'])
@section('canonical', $seoData['canonical'])

@section('content')
<div class="relative">
    <!-- Hero Section -->
    <section class="relative bg-gradient-to-b from-gray-900 to-gray-800 text-white py-20">
        <div class="absolute inset-0 bg-black opacity-50"></div>
        <div class="absolute inset-0 bg-cover bg-center" style="background-image: url('{{ asset('images/malombo_logo.jpg') }}')"></div>
        
        <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h1 class="text-4xl md:text-6xl font-bold mb-6">
                Safari Activities
            </h1>
            <p class="text-xl md:text-2xl mb-8 max-w-3xl mx-auto">
                Discover the wild heart of Tanzania through our carefully curated safari experiences
            </p>
        </div>
    </section>

    <!-- Activities Grid -->
    <section class="py-16 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            @if($activities->count() > 0)
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                    @foreach($activities as $activity)
                        <div class="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300" 
                             x-data="{ showModal: false }">
                            <!-- Activity Image -->
                            <div class="relative h-64 bg-gray-200">
                                @if($activity->getFirstMediaUrl('featured_image'))
                                    <img src="{{ $activity->getFirstMediaUrl('featured_image', 'preview') }}" 
                                         alt="{{ $activity->title }}"
                                         class="w-full h-full object-cover">
                                @else
                                    <div class="w-full h-full bg-gradient-to-br from-green-100 to-green-200 flex items-center justify-center">
                                        <svg class="w-16 h-16 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M2 6a2 2 0 012-2h5l2 2h5a2 2 0 012 2v6a2 2 0 01-2 2H4a2 2 0 01-2-2V6z"></path>
                                        </svg>
                                    </div>
                                @endif
                                
                                <!-- Category Badge -->
                                @if($activity->category)
                                    <div class="absolute top-4 left-4">
                                        <span class="bg-green-600 text-white px-3 py-1 rounded-full text-sm font-medium">
                                            {{ ucfirst($activity->category) }}
                                        </span>
                                    </div>
                                @endif
                            </div>

                            <!-- Activity Content -->
                            <div class="p-6">
                                <h3 class="text-xl font-bold text-gray-900 mb-3">
                                    {{ $activity->title }}
                                </h3>
                                
                                <p class="text-gray-600 mb-4 line-clamp-3">
                                    {{ $activity->description }}
                                </p>

                                @if($activity->duration)
                                    <div class="flex items-center text-sm text-gray-500 mb-4">
                                        <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z"></path>
                                        </svg>
                                        Duration: {{ $activity->duration }}
                                    </div>
                                @endif

                                <div class="flex justify-between items-center">
                                    <a href="{{ route('activities.show', $activity->slug) }}" 
                                       class="bg-green-600 text-white px-6 py-2 rounded-lg hover:bg-green-700 transition-colors duration-200">
                                        Learn More
                                    </a>
                                    
                                    <button @click="showModal = true" 
                                            class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors duration-200">
                                        Book Now
                                    </button>
                                </div>
                            </div>

                            <!-- Booking Modal -->
                            <div x-show="showModal" 
                                 x-transition:enter="transition ease-out duration-300" 
                                 x-transition:enter-start="opacity-0" 
                                 x-transition:enter-end="opacity-100"
                                 x-transition:leave="transition ease-in duration-200" 
                                 x-transition:leave-start="opacity-100" 
                                 x-transition:leave-end="opacity-0"
                                 class="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4"
                                 @click="showModal = false">
                                
                                <div class="bg-white rounded-lg max-w-md w-full p-6" @click.stop>
                                    <div class="flex justify-between items-center mb-4">
                                        <h3 class="text-xl font-bold">Book {{ $activity->title }}</h3>
                                        <button @click="showModal = false" class="text-gray-400 hover:text-gray-600">
                                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                            </svg>
                                        </button>
                                    </div>
                                    
                                    <p class="text-gray-600 mb-6">
                                        Contact us to book this amazing safari experience.
                                    </p>
                                    
                                    <div class="flex gap-3">
                                        <a href="{{ route('contact.index') }}" 
                                           class="flex-1 bg-green-600 text-white text-center py-2 px-4 rounded-lg hover:bg-green-700 transition-colors duration-200">
                                            Contact Us
                                        </a>
                                        <a href="{{ route('booking.index') }}" 
                                           class="flex-1 bg-blue-600 text-white text-center py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors duration-200">
                                            Booking Form
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>
            @else
                <div class="text-center py-16">
                    <svg class="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                    </svg>
                    <h3 class="text-xl font-medium text-gray-900 mb-2">No Activities Available</h3>
                    <p class="text-gray-500">Activities will be added soon. Please check back later.</p>
                </div>
            @endif
        </div>
    </section>

    <!-- Call to Action -->
    <section class="py-16 bg-green-50">
        <div class="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
            <h2 class="text-3xl font-bold text-gray-900 mb-4">
                Ready for Your Safari Adventure?
            </h2>
            <p class="text-xl text-gray-600 mb-8">
                Contact us to plan your perfect wildlife experience in Nyerere National Park.
            </p>
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <a href="{{ route('contact.index') }}" 
                   class="bg-green-600 text-white px-8 py-3 rounded-lg hover:bg-green-700 transition-colors duration-200">
                    Contact Us
                </a>
                <a href="{{ route('booking.index') }}" 
                   class="bg-blue-600 text-white px-8 py-3 rounded-lg hover:bg-blue-700 transition-colors duration-200">
                    Book Now
                </a>
            </div>
        </div>
    </section>
</div>
@endsection
