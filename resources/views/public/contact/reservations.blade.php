@extends('layouts.app')

@section('title', $seoData['title'])
@section('meta_description', $seoData['description'])
@section('canonical', $seoData['canonical'])

@section('content')
<div class="relative">
    <!-- Hero Section -->
    <section class="relative bg-gradient-to-b from-gray-900 to-gray-800 text-white py-20">
        <div class="absolute inset-0 bg-black opacity-50"></div>
        <div class="absolute inset-0 bg-cover bg-center" style="background-image: url('{{ asset('images/malombo_logo.jpg') }}')"></div>
        
        <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h1 class="text-4xl md:text-6xl font-bold mb-6">
                Make a Reservation
            </h1>
            <p class="text-xl md:text-2xl mb-8 max-w-3xl mx-auto">
                Start planning your unforgettable safari adventure at Malombo Selous Forest Camp
            </p>
        </div>
    </section>

    <!-- Reservation Form -->
    <section class="py-16 bg-white">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            @if(session('success'))
                <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-8">
                    <div class="flex">
                        <svg class="w-5 h-5 mr-2 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                        </svg>
                        {{ session('success') }}
                    </div>
                </div>
            @endif

            <div class="bg-white rounded-lg shadow-lg p-8">
                <h2 class="text-2xl font-bold text-gray-900 mb-6 text-center">
                    Reservation Enquiry Form
                </h2>
                
                <form action="{{ route('contact.submitReservation') }}" method="POST" class="space-y-6" x-data="reservationForm()">
                    @csrf
                    
                    <!-- Personal Information -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="name" class="block text-sm font-medium text-gray-700 mb-2">Full Name *</label>
                            <input type="text" 
                                   id="name" 
                                   name="name" 
                                   value="{{ old('name') }}"
                                   required
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent @error('name') border-red-500 @enderror">
                            @error('name')
                                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>

                        <div>
                            <label for="email" class="block text-sm font-medium text-gray-700 mb-2">Email Address *</label>
                            <input type="email" 
                                   id="email" 
                                   name="email" 
                                   value="{{ old('email') }}"
                                   required
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent @error('email') border-red-500 @enderror">
                            @error('email')
                                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>

                    <div>
                        <label for="phone" class="block text-sm font-medium text-gray-700 mb-2">Phone Number</label>
                        <input type="tel" 
                               id="phone" 
                               name="phone" 
                               value="{{ old('phone') }}"
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent @error('phone') border-red-500 @enderror">
                        @error('phone')
                            <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Travel Dates -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="arrival_date" class="block text-sm font-medium text-gray-700 mb-2">Arrival Date *</label>
                            <input type="date" 
                                   id="arrival_date" 
                                   name="arrival_date" 
                                   value="{{ old('arrival_date') }}"
                                   required
                                   x-model="arrivalDate"
                                   :min="minDate"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent @error('arrival_date') border-red-500 @enderror">
                            @error('arrival_date')
                                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>

                        <div>
                            <label for="departure_date" class="block text-sm font-medium text-gray-700 mb-2">Departure Date *</label>
                            <input type="date" 
                                   id="departure_date" 
                                   name="departure_date" 
                                   value="{{ old('departure_date') }}"
                                   required
                                   x-model="departureDate"
                                   :min="minDepartureDate"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent @error('departure_date') border-red-500 @enderror">
                            @error('departure_date')
                                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>

                    <!-- Guest Numbers -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="adults" class="block text-sm font-medium text-gray-700 mb-2">Number of Adults *</label>
                            <select id="adults" 
                                    name="adults" 
                                    required
                                    class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent @error('adults') border-red-500 @enderror">
                                <option value="">Select number of adults</option>
                                @for($i = 1; $i <= 20; $i++)
                                    <option value="{{ $i }}" {{ old('adults') == $i ? 'selected' : '' }}>{{ $i }}</option>
                                @endfor
                            </select>
                            @error('adults')
                                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>

                        <div>
                            <label for="children" class="block text-sm font-medium text-gray-700 mb-2">Number of Children</label>
                            <select id="children" 
                                    name="children" 
                                    class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent @error('children') border-red-500 @enderror">
                                <option value="0">No children</option>
                                @for($i = 1; $i <= 10; $i++)
                                    <option value="{{ $i }}" {{ old('children') == $i ? 'selected' : '' }}>{{ $i }}</option>
                                @endfor
                            </select>
                            @error('children')
                                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>

                    <!-- Accommodation Preference -->
                    <div>
                        <label for="accommodation_preference" class="block text-sm font-medium text-gray-700 mb-2">Accommodation Preference</label>
                        <select id="accommodation_preference" 
                                name="accommodation_preference" 
                                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent @error('accommodation_preference') border-red-500 @enderror">
                            <option value="">No preference</option>
                            <option value="banda" {{ old('accommodation_preference') == 'banda' ? 'selected' : '' }}>Banda</option>
                            <option value="room" {{ old('accommodation_preference') == 'room' ? 'selected' : '' }}>Room</option>
                            <option value="tent" {{ old('accommodation_preference') == 'tent' ? 'selected' : '' }}>Tent</option>
                            <option value="tree_house" {{ old('accommodation_preference') == 'tree_house' ? 'selected' : '' }}>Tree House</option>
                            <option value="inter_leading" {{ old('accommodation_preference') == 'inter_leading' ? 'selected' : '' }}>Inter-leading Room</option>
                            <option value="campsite" {{ old('accommodation_preference') == 'campsite' ? 'selected' : '' }}>Campsite</option>
                        </select>
                        @error('accommodation_preference')
                            <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Special Requests -->
                    <div>
                        <label for="special_requests" class="block text-sm font-medium text-gray-700 mb-2">Special Requests or Dietary Requirements</label>
                        <textarea id="special_requests" 
                                  name="special_requests" 
                                  rows="4"
                                  placeholder="Please let us know about any special requests, dietary requirements, mobility needs, or celebration occasions..."
                                  class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent @error('special_requests') border-red-500 @enderror">{{ old('special_requests') }}</textarea>
                        @error('special_requests')
                            <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Submit Button -->
                    <div class="text-center">
                        <button type="submit" 
                                class="bg-green-600 text-white px-8 py-3 rounded-lg hover:bg-green-700 transition-colors duration-200 font-medium">
                            Submit Reservation Enquiry
                        </button>
                    </div>
                </form>
            </div>

            <!-- Contact Information -->
            <div class="mt-12 text-center">
                <h3 class="text-xl font-bold text-gray-900 mb-4">Need immediate assistance?</h3>
                <p class="text-gray-600 mb-6">
                    Contact us directly for urgent enquiries or personalized assistance.
                </p>
                
                <div class="flex flex-col sm:flex-row gap-4 justify-center">
                    <a href="{{ route('contact.index') }}" 
                       class="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors duration-200">
                        General Contact
                    </a>
                    <a href="tel:+255784123456" 
                       class="bg-gray-600 text-white px-6 py-3 rounded-lg hover:bg-gray-700 transition-colors duration-200">
                        Call Us Now
                    </a>
                </div>
            </div>
        </div>
    </section>
</div>

<script>
function reservationForm() {
    return {
        arrivalDate: '',
        departureDate: '',
        
        get minDate() {
            const tomorrow = new Date();
            tomorrow.setDate(tomorrow.getDate() + 1);
            return tomorrow.toISOString().split('T')[0];
        },
        
        get minDepartureDate() {
            if (this.arrivalDate) {
                const arrival = new Date(this.arrivalDate);
                arrival.setDate(arrival.getDate() + 1);
                return arrival.toISOString().split('T')[0];
            }
            return this.minDate;
        }
    }
}
</script>
@endsection
