@extends('layouts.public')

@section('title', 'Contact Us - ' . config('malombo.name'))
@section('description', 'Get in touch with Malombo Selous Forest Camp for bookings, inquiries, and information about our luxury safari experiences.')

@php
    $contactFormFields = [
        'inquiry_type' => [
            'type' => 'select',
            'label' => 'Type of Inquiry',
            'required' => true,
            'options' => [
                'booking' => 'Booking Inquiry',
                'information' => 'General Information',
                'group' => 'Group Booking',
                'special' => 'Special Occasions',
                'other' => 'Other'
            ],
            'placeholder' => 'Select inquiry type'
        ],
        'travel_dates' => [
            'type' => 'text',
            'label' => 'Preferred Travel Dates',
            'required' => false,
            'placeholder' => 'e.g., March 2024 or flexible',
            'help' => 'Let us know your preferred travel dates or if you are flexible'
        ],
        'party_size' => [
            'type' => 'number',
            'label' => 'Number of Guests',
            'required' => false,
            'min' => 1,
            'max' => 20,
            'placeholder' => '2'
        ]
    ];

    $faqItems = [
        [
            'question' => 'How do I make a reservation?',
            'answer' => 'You can make a reservation by filling out our contact form, calling us directly, or sending an email to our reservations team. We recommend booking at least 3-6 months in advance, especially for peak season (June-October).',
            'actions' => [
                [
                    'label' => 'Make a Booking',
                    'url' => route('booking.index')
                ]
            ]
        ],
        [
            'question' => 'What is included in the safari package?',
            'answer' => 'Our safari packages include accommodation, all meals, game drives, boat safaris, park fees, transfers from/to the airstrip, and services of professional guides. Alcoholic beverages and personal items are not included unless specified.',
            'actions' => [
                [
                    'label' => 'View Packages',
                    'url' => route('rates.index')
                ]
            ]
        ],
        [
            'question' => 'How do I get to the camp?',
            'answer' => 'The camp is accessible by chartered flight to Mtemere Airstrip (6.8km from camp) or by road. We can arrange transfers and provide detailed directions. The nearest major airport is Julius Nyerere International Airport in Dar es Salaam.',
            'actions' => [
                [
                    'label' => 'Getting Here',
                    'url' => route('location.getting-here')
                ]
            ]
        ],
        [
            'question' => 'What should I pack for my safari?',
            'answer' => 'Pack light, neutral-colored clothing, comfortable walking shoes, hat, sunglasses, sunscreen, insect repellent, camera, and any personal medications. We provide a detailed packing list upon booking confirmation.',
            'actions' => [
                [
                    'label' => 'Packing Guide',
                    'url' => route('contact.index')
                ]
            ]
        ],
        [
            'question' => 'Is the camp suitable for children?',
            'answer' => 'Yes! We welcome families with children. Children aged 5-13 receive discounted rates. We can arrange family-friendly activities and provide special meal options. Please inform us of any specific requirements when booking.'
        ],
        [
            'question' => 'What is your cancellation policy?',
            'answer' => 'Cancellations made 60+ days before arrival receive full refund minus processing fee. Cancellations 30-59 days before arrival receive 50% refund. Cancellations within 30 days are non-refundable. We recommend travel insurance.',
            'actions' => [
                [
                    'label' => 'Full Terms',
                    'url' => route('legal.terms')
                ]
            ]
        ]
    ];
@endphp

<x-slot name="title">Contact Us - {{ config('malombo.name') }}</x-slot>

<!-- Page Header -->
<section class="bg-gradient-to-br from-forest-600 to-forest-800 text-white py-16 lg:py-24">
    <div class="container mx-auto text-center">
        <h1 class="text-4xl lg:text-5xl font-display font-bold mb-4">
            Contact Us
        </h1>
        <p class="text-xl text-forest-100 max-w-3xl mx-auto leading-relaxed">
            Ready to embark on your safari adventure? Get in touch with our team for bookings, 
            inquiries, and personalized assistance in planning your perfect stay.
        </p>
    </div>
</section>

<!-- Contact Information Cards -->
<section class="py-16 bg-stone-50">
    <div class="container mx-auto">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
            
            <!-- Phone Contact -->
            <div class="bg-white rounded-xl p-6 shadow-soft border border-stone-200 text-center">
                <div class="bg-forest-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg class="h-8 w-8 text-forest-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                    </svg>
                </div>
                <h3 class="font-display font-semibold text-stone-900 mb-2">Call Us</h3>
                <p class="text-stone-600 mb-4">Speak directly with our reservations team</p>
                <div class="space-y-2">
                    @foreach(config('malombo.contact.phones') as $phone)
                        <a href="tel:{{ str_replace(' ', '', $phone) }}" class="block text-forest-600 hover:text-forest-700 font-medium transition-colors duration-200">
                            {{ $phone }}
                        </a>
                    @endforeach
                </div>
            </div>

            <!-- Email Contact -->
            <div class="bg-white rounded-xl p-6 shadow-soft border border-stone-200 text-center">
                <div class="bg-forest-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg class="h-8 w-8 text-forest-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                    </svg>
                </div>
                <h3 class="font-display font-semibold text-stone-900 mb-2">Email Us</h3>
                <p class="text-stone-600 mb-4">Send us your inquiries and questions</p>
                <div class="space-y-2">
                    <a href="mailto:{{ config('malombo.contact.emails.reservations') }}" class="block text-forest-600 hover:text-forest-700 font-medium transition-colors duration-200">
                        {{ config('malombo.contact.emails.reservations') }}
                    </a>
                    <a href="mailto:{{ config('malombo.contact.emails.info') }}" class="block text-forest-600 hover:text-forest-700 font-medium transition-colors duration-200">
                        {{ config('malombo.contact.emails.info') }}
                    </a>
                </div>
            </div>

            <!-- Location -->
            <div class="bg-white rounded-xl p-6 shadow-soft border border-stone-200 text-center">
                <div class="bg-forest-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg class="h-8 w-8 text-forest-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    </svg>
                </div>
                <h3 class="font-display font-semibold text-stone-900 mb-2">Visit Us</h3>
                <p class="text-stone-600 mb-4">{{ config('malombo.location.park') }}</p>
                <p class="text-sm text-stone-500">
                    {{ config('malombo.location.distance_to_gate') }}<br>
                    Near {{ config('malombo.location.nearby_village') }}
                </p>
            </div>
        </div>
    </div>
</section>

<!-- Contact Form and Map Section -->
<section class="py-16">
    <div class="container mx-auto">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-16">
            
            <!-- Contact Form -->
            <div>
                <x-contact-form 
                    title="Send Us a Message"
                    description="Fill out the form below and we'll get back to you within 24 hours."
                    :fields="$contactFormFields"
                    size="large"
                />
            </div>

            <!-- Map and Additional Info -->
            <div>
                <x-map-section 
                    title="Find Our Location"
                    description="Malombo Selous Forest Camp is located in the heart of Nyerere National Park, Tanzania's largest national park."
                    latitude="-7.8767"
                    longitude="37.8767"
                    height="500px"
                    :contactInfo="false"
                />

                <!-- Additional Contact Information -->
                <div class="mt-8 space-y-6">
                    <div class="bg-white rounded-xl p-6 shadow-soft border border-stone-200">
                        <h3 class="font-display font-semibold text-stone-900 mb-4">Operating Hours</h3>
                        <div class="space-y-2 text-stone-600">
                            <div class="flex justify-between">
                                <span>Reservations Office:</span>
                                <span class="font-medium">8:00 AM - 6:00 PM EAT</span>
                            </div>
                            <div class="flex justify-between">
                                <span>Emergency Contact:</span>
                                <span class="font-medium">24/7 Available</span>
                            </div>
                            <div class="flex justify-between">
                                <span>Check-in:</span>
                                <span class="font-medium">{{ config('malombo.policies.check_in_start') }} - {{ config('malombo.policies.check_in_end') }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span>Check-out:</span>
                                <span class="font-medium">{{ config('malombo.policies.check_out') }}</span>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white rounded-xl p-6 shadow-soft border border-stone-200">
                        <h3 class="font-display font-semibold text-stone-900 mb-4">Response Times</h3>
                        <div class="space-y-3">
                            <div class="flex items-center space-x-3">
                                <svg class="h-5 w-5 text-forest-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                <span class="text-stone-600">Email inquiries: Within 24 hours</span>
                            </div>
                            <div class="flex items-center space-x-3">
                                <svg class="h-5 w-5 text-forest-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                <span class="text-stone-600">Phone calls: Immediate response</span>
                            </div>
                            <div class="flex items-center space-x-3">
                                <svg class="h-5 w-5 text-forest-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                <span class="text-stone-600">Booking confirmations: Within 48 hours</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- FAQ Section -->
<section class="py-16 bg-stone-50">
    <div class="container mx-auto">
        <x-accordion 
            :items="$faqItems"
            title="Frequently Asked Questions"
            description="Find answers to common questions about staying at Malombo Selous Forest Camp"
            variant="default"
            :allowMultiple="false"
            size="default"
        />
        
        <div class="text-center mt-8">
            <p class="text-stone-600 mb-4">Can't find what you're looking for?</p>
            <a href="{{ route('contact.index') }}" class="inline-flex items-center text-forest-600 hover:text-forest-700 font-semibold transition-colors duration-200">
                View Complete FAQ
                <svg class="ml-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                </svg>
            </a>
        </div>
    </div>
</section>

<!-- CTA Section -->
<section class="py-16">
    <div class="container mx-auto">
        <x-cta-banner 
            title="Ready to Book Your Safari?"
            subtitle="Don't wait - secure your spot at Malombo Selous Forest Camp and experience the adventure of a lifetime in Tanzania's premier safari destination."
            ctaText="Book Now"
            :ctaUrl="route('booking.index')"
            secondaryCtaText="Request Quote"
            :secondaryCtaUrl="route('contact.index')"
            variant="minimal"
            size="default"
        />
    </div>
</section>
