@extends('layouts.public')

@section('title', config('malombo.name') . ' - ' . config('malombo.taglines.primary'))
@section('description', config('malombo.seo.description'))

@php
    $heroImage = asset('images/hero-safari.jpg'); // Placeholder - would be actual safari image
    
    // Sample accommodation data
    $accommodations = [
        [
            'title' => 'Presidential Suite',
            'description' => 'Our most luxurious accommodation with panoramic views of the Rufiji River and exclusive amenities.',
            'image' => asset('images/presidential-suite.jpg'),
            'imageAlt' => 'Presidential Suite with river view',
            'url' => route('accommodation.show', 'presidential-suite'),
            'price' => 1000,
            'priceLabel' => 'From',
            'features' => [
                'Private balcony with river view',
                'King-size bed with premium linens',
                'Spacious living area',
                'Private bathroom with bathtub',
                'Complimentary minibar'
            ]
        ],
        [
            'title' => 'Forest View Tents',
            'description' => 'Comfortable safari tents with stunning forest views and modern amenities.',
            'image' => asset('images/forest-tent.jpg'),
            'imageAlt' => 'Forest View Tent accommodation',
            'url' => route('accommodation.show', 'forest-view-tents'),
            'price' => 380,
            'priceLabel' => 'From',
            'features' => [
                'Canvas tent with solid flooring',
                'Private ensuite bathroom',
                'Forest canopy views',
                'Comfortable twin or double beds',
                'Writing desk and seating area'
            ]
        ],
        [
            'title' => 'River Safari Tents',
            'description' => 'Experience the sounds of the African wilderness in our comfortable river-facing tents.',
            'image' => asset('images/river-tent.jpg'),
            'imageAlt' => 'River Safari Tent accommodation',
            'url' => route('accommodation.show', 'river-safari-tents'),
            'price' => 280,
            'priceLabel' => 'From',
            'features' => [
                'Tented accommodation',
                'Shared bathroom facilities',
                'River proximity',
                'Authentic safari experience',
                'Comfortable bedding'
            ]
        ]
    ];

    // Sample activities
    $activities = [
        [
            'title' => 'Game Drives',
            'description' => 'Explore the vast wilderness of Nyerere National Park with our expert guides.',
            'image' => asset('images/game-drive.jpg'),
            'imageAlt' => 'Safari game drive experience',
            'url' => route('activities.show', 'game-drives'),
            'features' => [
                'Morning and evening drives',
                'Expert local guides',
                'Open safari vehicles',
                'Wildlife photography opportunities'
            ]
        ],
        [
            'title' => 'Boat Safaris',
            'description' => 'Experience wildlife from the water on the mighty Rufiji River.',
            'image' => asset('images/boat-safari.jpg'),
            'imageAlt' => 'Boat safari on Rufiji River',
            'url' => route('activities.show', 'boat-safaris'),
            'features' => [
                'Rufiji River exploration',
                'Hippo and crocodile viewing',
                'Bird watching',
                'Sunset cruises'
            ]
        ]
    ];

    // Sample testimonials
    $testimonials = [
        [
            'quote' => 'An absolutely magical experience! The accommodation was luxurious and the wildlife viewing was beyond our expectations. The staff made us feel like family.',
            'author' => 'Sarah Johnson',
            'title' => 'Wildlife Photographer',
            'location' => 'United Kingdom',
            'rating' => 5,
            'date' => '2024-07-15'
        ],
        [
            'quote' => 'Malombo exceeded all our expectations. The location is perfect for both game drives and boat safaris. A truly authentic African safari experience.',
            'author' => 'Michael Chen',
            'title' => 'Travel Blogger',
            'location' => 'Canada',
            'rating' => 5,
            'date' => '2024-06-20'
        ]
    ];

    // Sample gallery images
    $galleryImages = [
        [
            'src' => asset('images/gallery/elephants.jpg'),
            'alt' => 'African elephants in Nyerere National Park',
            'title' => 'Majestic Elephants'
        ],
        [
            'src' => asset('images/gallery/sunset.jpg'),
            'alt' => 'Sunset over the Rufiji River',
            'title' => 'African Sunset'
        ],
        [
            'src' => asset('images/gallery/lions.jpg'),
            'alt' => 'Pride of lions resting',
            'title' => 'Lion Pride'
        ],
        [
            'src' => asset('images/gallery/camp-view.jpg'),
            'alt' => 'Malombo camp overlooking the forest',
            'title' => 'Camp Overview'
        ]
    ];

    // Sample features
    $features = [
        [
            'title' => 'Prime Location',
            'description' => 'Located just 6.8km from Mtemere Gate in the heart of Nyerere National Park'
        ],
        [
            'title' => 'Expert Guides',
            'description' => 'Our experienced local guides provide incredible wildlife viewing opportunities'
        ],
        [
            'title' => 'Luxury Comfort',
            'description' => 'Modern amenities and comfortable accommodations in the African wilderness'
        ],
        [
            'title' => 'Authentic Experience',
            'description' => 'True safari experience with close encounters with African wildlife'
        ],
        [
            'title' => 'Sustainable Tourism',
            'description' => 'Committed to conservation and supporting local communities'
        ],
        [
            'title' => 'All-Inclusive',
            'description' => 'Meals, activities, and transfers included in your safari package'
        ]
    ];
@endphp

<x-slot name="title">{{ config('malombo.name') }} - {{ config('malombo.taglines.primary') }}</x-slot>

<!-- Hero Section -->
<x-hero 
    :backgroundImage="$heroImage"
    :title="'Experience the<br><span class=\"text-accent-amber-300\">Ultimate Safari</span><br>Adventure'"
    :subtitle="config('malombo.taglines.secondary') . ' in the heart of ' . config('malombo.location.park')"
    :tagline="config('malombo.taglines.primary')"
    ctaText="Book Your Adventure"
    :ctaUrl="route('booking.index')"
    height="screen"
    overlay="dark"
    textAlign="center"
/>

<!-- Main Content -->
<main id="content" class="py-16 lg:py-24">
    
    <!-- Introduction Section -->
    <section class="container mx-auto mb-20">
        <div class="max-w-4xl mx-auto text-center">
            <h2 class="text-3xl lg:text-4xl font-display font-bold text-stone-900 mb-6">
                Welcome to {{ config('malombo.name') }}
            </h2>
            <p class="text-lg lg:text-xl text-stone-600 leading-relaxed mb-8">
                Nestled in the pristine wilderness of {{ config('malombo.location.park') }}, 
                our luxury safari camp offers an unparalleled African adventure. 
                Experience the magic of Tanzania's largest game reserve with world-class 
                accommodation and authentic safari experiences.
            </p>
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <a href="{{ route('about') }}" class="inline-flex items-center bg-forest-600 hover:bg-forest-700 text-white px-8 py-3 rounded-xl font-semibold transition-all duration-200 shadow-soft hover:shadow-medium">
                    Learn More About Us
                    <svg class="ml-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                    </svg>
                </a>
                <a href="{{ route('gallery') }}" class="inline-flex items-center border-2 border-forest-600 text-forest-600 hover:bg-forest-600 hover:text-white px-8 py-3 rounded-xl font-semibold transition-all duration-200">
                    View Gallery
                </a>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section class="container mx-auto mb-20">
        <x-feature-list 
            :features="$features"
            title="Why Choose Malombo?"
            description="Discover what makes our safari camp the perfect choice for your African adventure"
            layout="grid"
            columns="3"
            iconStyle="check"
        />
    </section>

    <!-- Accommodation Section -->
    <section class="container mx-auto mb-20">
        <div class="text-center mb-12">
            <h2 class="text-3xl lg:text-4xl font-display font-bold text-stone-900 mb-4">
                Luxury Safari Accommodation
            </h2>
            <p class="text-lg text-stone-600 max-w-3xl mx-auto">
                Choose from our range of comfortable and well-appointed accommodations, 
                each designed to provide the perfect balance of luxury and authentic safari experience.
            </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-8">
            @foreach($accommodations as $accommodation)
                <x-card 
                    :title="$accommodation['title']"
                    :description="$accommodation['description']"
                    :image="$accommodation['image']"
                    :imageAlt="$accommodation['imageAlt']"
                    :url="$accommodation['url']"
                    :price="$accommodation['price']"
                    :priceLabel="$accommodation['priceLabel']"
                    :features="$accommodation['features']"
                    variant="{{ $loop->first ? 'featured' : 'default' }}"
                />
            @endforeach
        </div>

        <div class="text-center">
            <a href="{{ route('accommodation') }}" class="inline-flex items-center text-forest-600 hover:text-forest-700 font-semibold text-lg transition-colors duration-200">
                View All Accommodation Options
                <svg class="ml-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                </svg>
            </a>
        </div>
    </section>

    <!-- Activities Section -->
    <section class="bg-stone-50 py-16 lg:py-20 -mx-4 sm:-mx-6 lg:-mx-8">
        <div class="container mx-auto">
            <div class="text-center mb-12">
                <h2 class="text-3xl lg:text-4xl font-display font-bold text-stone-900 mb-4">
                    Safari Activities & Experiences
                </h2>
                <p class="text-lg text-stone-600 max-w-3xl mx-auto">
                    Immerse yourself in the wonders of African wildlife with our carefully curated 
                    safari activities led by expert local guides.
                </p>
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
                @foreach($activities as $activity)
                    <x-card 
                        :title="$activity['title']"
                        :description="$activity['description']"
                        :image="$activity['image']"
                        :imageAlt="$activity['imageAlt']"
                        :url="$activity['url']"
                        :features="$activity['features']"
                        orientation="horizontal"
                    />
                @endforeach
            </div>

            <div class="text-center">
                <a href="{{ route('activities') }}" class="inline-flex items-center bg-forest-600 hover:bg-forest-700 text-white px-8 py-3 rounded-xl font-semibold transition-all duration-200 shadow-soft hover:shadow-medium">
                    Explore All Activities
                    <svg class="ml-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                    </svg>
                </a>
            </div>
        </div>
    </section>

    <!-- Gallery Section -->
    <section class="container mx-auto my-20">
        <x-image-gallery 
            :images="$galleryImages"
            title="Capture the Magic"
            description="Experience the beauty and wonder of African wildlife through our photo gallery"
            layout="masonry"
            columns="3"
        />
        
        <div class="text-center mt-8">
            <a href="{{ route('gallery') }}" class="inline-flex items-center text-forest-600 hover:text-forest-700 font-semibold text-lg transition-colors duration-200">
                View Complete Gallery
                <svg class="ml-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                </svg>
            </a>
        </div>
    </section>

    <!-- Testimonials Section -->
    <section class="bg-gradient-to-br from-forest-50 to-stone-50 py-16 lg:py-20 -mx-4 sm:-mx-6 lg:-mx-8">
        <div class="container mx-auto">
            <div class="text-center mb-12">
                <h2 class="text-3xl lg:text-4xl font-display font-bold text-stone-900 mb-4">
                    What Our Guests Say
                </h2>
                <p class="text-lg text-stone-600 max-w-3xl mx-auto">
                    Read testimonials from our satisfied guests who have experienced the magic of Malombo.
                </p>
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                @foreach($testimonials as $testimonial)
                    <x-testimonial 
                        :quote="$testimonial['quote']"
                        :author="$testimonial['author']"
                        :title="$testimonial['title']"
                        :location="$testimonial['location']"
                        :rating="$testimonial['rating']"
                        :date="$testimonial['date']"
                        variant="{{ $loop->first ? 'featured' : 'default' }}"
                    />
                @endforeach
            </div>

            <div class="text-center mt-8">
                <a href="{{ route('testimonials') }}" class="inline-flex items-center text-forest-600 hover:text-forest-700 font-semibold text-lg transition-colors duration-200">
                    Read More Reviews
                    <svg class="ml-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                    </svg>
                </a>
            </div>
        </div>
    </section>

    <!-- CTA Section -->
    <section class="container mx-auto my-20">
        <x-cta-banner 
            title="Ready for Your Safari Adventure?"
            subtitle="Book your stay at Malombo Selous Forest Camp and create memories that will last a lifetime. Our team is ready to help you plan the perfect African safari experience."
            ctaText="Book Now"
            :ctaUrl="route('booking.index')"
            secondaryCtaText="Get Quote"
            :secondaryCtaUrl="route('contact.index')"
            variant="gradient"
            size="large"
        />
    </section>

</main>

@push('scripts')
<script>
    // Initialize any page-specific functionality
    document.addEventListener('DOMContentLoaded', function() {
        // Example: Show welcome toast for first-time visitors
        if (!localStorage.getItem('visited_before')) {
            setTimeout(() => {
                if (window.showToast) {
                    showToast('info', 'Welcome to Malombo Selous Forest Camp! Explore our luxury safari experiences.', 'Welcome!');
                }
                localStorage.setItem('visited_before', 'true');
            }, 2000);
        }
    });
</script>
@endpush
