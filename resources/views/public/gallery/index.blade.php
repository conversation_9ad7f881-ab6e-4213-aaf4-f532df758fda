@extends('layouts.public')

@section('title', 'Photo Gallery - Malombo Safari Lodge')
@section('description', 'Explore our stunning collection of photos showcasing the beauty of Malombo Safari Lodge, wildlife, accommodations, and unforgettable safari experiences.')

@section('content')
<div class="min-h-screen bg-gradient-to-b from-amber-50 to-white">
    <!-- Hero Section -->
    <section class="relative py-20 bg-gradient-to-r from-amber-900 via-amber-800 to-amber-900">
        <div class="absolute inset-0 bg-black/30"></div>
        <div class="container mx-auto px-4 relative z-10">
            <div class="text-center text-white">
                <h1 class="text-5xl font-bold mb-6">Photo Gallery</h1>
                <p class="text-xl max-w-3xl mx-auto leading-relaxed">
                    Immerse yourself in the beauty of Malombo Safari Lodge through our stunning collection of photographs
                </p>
            </div>
        </div>
    </section>

    <!-- Gallery Content -->
    <section class="py-16">
        <div class="container mx-auto px-4">
            <!-- Filter Tabs -->
            <div class="flex flex-wrap justify-center mb-12" x-data="{ activeCategory: 'All' }">
                @foreach($categories as $category => $count)
                <button 
                    @click="activeCategory = '{{ $category }}'"
                    :class="{ 'bg-amber-600 text-white': activeCategory === '{{ $category }}', 'bg-white text-gray-700 hover:bg-amber-50': activeCategory !== '{{ $category }}' }"
                    class="px-6 py-3 mx-2 mb-2 rounded-full border border-amber-200 transition-all duration-300 font-medium">
                    {{ $category }} ({{ $count }})
                </button>
                @endforeach
            </div>

            <!-- Gallery Grid -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6" 
                 x-data="galleryData()" 
                 x-init="init()">
                
                @foreach($galleryImages as $index => $image)
                <div class="gallery-item group cursor-pointer"
                     data-category="{{ $image['category'] }}"
                     :class="{ 'hidden': activeCategory !== 'All' && activeCategory !== '{{ $image['category'] }}' }"
                     @click="openLightbox({{ $index }})">
                    
                    <div class="relative overflow-hidden rounded-lg shadow-lg hover:shadow-xl transition-all duration-300">
                        <img 
                            src="{{ $image['thumbnail'] }}" 
                            alt="{{ $image['title'] }}"
                            class="w-full h-64 object-cover transform group-hover:scale-110 transition-transform duration-500"
                            loading="lazy">
                        
                        <!-- Overlay -->
                        <div class="absolute inset-0 bg-gradient-to-t from-black/70 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                            <div class="absolute bottom-0 left-0 right-0 p-4 text-white">
                                <h3 class="font-semibold text-lg mb-1">{{ $image['title'] }}</h3>
                                <p class="text-sm text-gray-200">{{ $image['category'] }}</p>
                            </div>
                        </div>

                        <!-- View Icon -->
                        <div class="absolute top-4 right-4 bg-white/20 backdrop-blur-sm rounded-full p-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                            </svg>
                        </div>
                    </div>
                </div>
                @endforeach
            </div>

            <!-- Lightbox -->
            <div x-show="lightboxOpen" 
                 x-transition:enter="transition ease-out duration-300"
                 x-transition:enter-start="opacity-0"
                 x-transition:enter-end="opacity-100"
                 x-transition:leave="transition ease-in duration-200"
                 x-transition:leave-start="opacity-100"
                 x-transition:leave-end="opacity-0"
                 class="fixed inset-0 z-50 bg-black/90 flex items-center justify-center p-4"
                 @click="closeLightbox()"
                 style="display: none;">
                
                <!-- Close Button -->
                <button @click="closeLightbox()" class="absolute top-6 right-6 text-white hover:text-gray-300 transition-colors z-60">
                    <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>

                <!-- Previous Button -->
                <button @click.stop="previousImage()" class="absolute left-6 top-1/2 transform -translate-y-1/2 text-white hover:text-gray-300 transition-colors">
                    <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                    </svg>
                </button>

                <!-- Next Button -->
                <button @click.stop="nextImage()" class="absolute right-6 top-1/2 transform -translate-y-1/2 text-white hover:text-gray-300 transition-colors">
                    <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                    </svg>
                </button>

                <!-- Image Container -->
                <div class="max-w-5xl max-h-full flex flex-col items-center" @click.stop>
                    <img :src="currentImage?.url" 
                         :alt="currentImage?.title"
                         class="max-w-full max-h-[80vh] object-contain rounded-lg shadow-2xl">
                    
                    <!-- Image Info -->
                    <div class="mt-6 text-center text-white max-w-2xl">
                        <h3 class="text-2xl font-bold mb-2" x-text="currentImage?.title"></h3>
                        <p class="text-gray-300 mb-2" x-text="currentImage?.category"></p>
                        <p class="text-gray-400" x-text="currentImage?.description"></p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Call to Action -->
    <section class="py-16 bg-amber-900 text-white">
        <div class="container mx-auto px-4 text-center">
            <h2 class="text-3xl font-bold mb-6">Ready to Experience Malombo?</h2>
            <p class="text-xl mb-8 max-w-2xl mx-auto">
                Book your safari adventure and create your own unforgettable memories at Malombo Safari Lodge
            </p>
            <a href="{{ route('accommodations.index') }}" 
               class="inline-block bg-white text-amber-900 px-8 py-4 rounded-full font-semibold hover:bg-gray-100 transition-colors duration-300">
                Book Your Stay
            </a>
        </div>
    </section>
</div>

<script>
function galleryData() {
    return {
        lightboxOpen: false,
        currentImageIndex: 0,
        currentImage: null,
        images: @json($galleryImages->values()),
        activeCategory: 'All',
        
        init() {
            // Initialize images
            this.currentImage = this.images[0] || null;
        },
        
        openLightbox(index) {
            this.currentImageIndex = index;
            this.currentImage = this.images[index];
            this.lightboxOpen = true;
            document.body.style.overflow = 'hidden';
        },
        
        closeLightbox() {
            this.lightboxOpen = false;
            document.body.style.overflow = 'auto';
        },
        
        nextImage() {
            this.currentImageIndex = (this.currentImageIndex + 1) % this.images.length;
            this.currentImage = this.images[this.currentImageIndex];
        },
        
        previousImage() {
            this.currentImageIndex = this.currentImageIndex === 0 ? this.images.length - 1 : this.currentImageIndex - 1;
            this.currentImage = this.images[this.currentImageIndex];
        }
    }
}

// Keyboard navigation
document.addEventListener('keydown', function(e) {
    if (Alpine.store === undefined) return;
    
    const gallery = document.querySelector('[x-data*="galleryData"]')?.__x;
    if (!gallery || !gallery.$data.lightboxOpen) return;
    
    switch(e.key) {
        case 'Escape':
            gallery.$data.closeLightbox();
            break;
        case 'ArrowLeft':
            gallery.$data.previousImage();
            break;
        case 'ArrowRight':
            gallery.$data.nextImage();
            break;
    }
});
</script>
@endsection
