@extends('layouts.public')

@section('title', 'Location & Map - Malombo Safari Lodge')
@section('description', 'Find Malombo Safari Lodge located in the heart of the Kruger area. View our location on the map and get directions to our luxury safari lodge.')

@section('content')
<div class="min-h-screen bg-gradient-to-b from-amber-50 to-white">
    <!-- Hero Section -->
    <section class="relative py-20 bg-gradient-to-r from-green-900 via-green-800 to-amber-900">
        <div class="absolute inset-0 bg-black/30"></div>
        <div class="container mx-auto px-4 relative z-10">
            <div class="text-center text-white">
                <h1 class="text-5xl font-bold mb-6">Our Location</h1>
                <p class="text-xl max-w-3xl mx-auto leading-relaxed">
                    Nestled in the heart of the Greater Kruger area, Malombo Safari Lodge offers the perfect gateway to South Africa's premier wildlife destination
                </p>
            </div>
        </div>
    </section>

    <!-- Location Details -->
    <section class="py-16">
        <div class="container mx-auto px-4">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                <!-- Map Container -->
                <div class="order-2 lg:order-1">
                    <div class="bg-gray-200 rounded-lg h-96 flex items-center justify-center">
                        <!-- Interactive Map Placeholder -->
                        <div class="text-center text-gray-600">
                            <svg class="w-16 h-16 mx-auto mb-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                            </svg>
                            <h3 class="text-lg font-semibold mb-2">Interactive Map</h3>
                            <p class="text-sm">Google Maps integration will be loaded here</p>
                            <p class="text-xs mt-2 text-gray-500">Coordinates: {{ $coordinates['latitude'] }}, {{ $coordinates['longitude'] }}</p>
                        </div>
                    </div>
                </div>

                <!-- Location Info -->
                <div class="order-1 lg:order-2">
                    <h2 class="text-3xl font-bold text-gray-800 mb-6">Find Us in Paradise</h2>
                    <div class="space-y-6">
                        <div class="flex items-start space-x-4">
                            <div class="bg-amber-100 p-3 rounded-lg">
                                <svg class="w-6 h-6 text-amber-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                </svg>
                            </div>
                            <div>
                                <h3 class="font-semibold text-lg mb-2">Physical Address</h3>
                                <p class="text-gray-600">
                                    Malombo Safari Lodge<br>
                                    Private Game Reserve<br>
                                    Greater Kruger Area<br>
                                    Mpumalanga, South Africa
                                </p>
                            </div>
                        </div>

                        <div class="flex items-start space-x-4">
                            <div class="bg-green-100 p-3 rounded-lg">
                                <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                            </div>
                            <div>
                                <h3 class="font-semibold text-lg mb-2">Conservation Area</h3>
                                <p class="text-gray-600">
                                    Located in a 15,000 hectare private game reserve adjacent to Kruger National Park, 
                                    offering unfenced access to the greater Kruger ecosystem.
                                </p>
                            </div>
                        </div>

                        <div class="flex items-start space-x-4">
                            <div class="bg-blue-100 p-3 rounded-lg">
                                <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L15 4m0 13V4m-6 3l6-3"></path>
                                </svg>
                            </div>
                            <div>
                                <h3 class="font-semibold text-lg mb-2">GPS Coordinates</h3>
                                <p class="text-gray-600">
                                    Latitude: {{ $coordinates['latitude'] }}<br>
                                    Longitude: {{ $coordinates['longitude'] }}
                                </p>
                                <button class="mt-2 text-amber-600 hover:text-amber-800 font-medium">
                                    Copy Coordinates
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Nearby Airports -->
    <section class="py-16 bg-gray-50">
        <div class="container mx-auto px-4">
            <div class="text-center mb-12">
                <h2 class="text-3xl font-bold text-gray-800 mb-4">Nearby Airports</h2>
                <p class="text-gray-600 max-w-2xl mx-auto">
                    Multiple airport options make it convenient to reach Malombo Safari Lodge from anywhere in the world
                </p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                @foreach($nearbyAirports as $airport)
                <div class="bg-white rounded-lg shadow-lg p-6 hover:shadow-xl transition-shadow duration-300">
                    <div class="flex items-center mb-4">
                        <div class="bg-amber-100 p-3 rounded-lg mr-4">
                            <svg class="w-6 h-6 text-amber-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path>
                            </svg>
                        </div>
                        <div>
                            <h3 class="text-xl font-semibold text-gray-800">{{ $airport['name'] }}</h3>
                            <p class="text-amber-600 font-medium">{{ $airport['code'] }}</p>
                        </div>
                    </div>
                    <div class="space-y-2">
                        <div class="flex justify-between">
                            <span class="text-gray-600">Distance:</span>
                            <span class="font-medium">{{ $airport['distance'] }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Drive Time:</span>
                            <span class="font-medium">{{ $airport['drive_time'] }}</span>
                        </div>
                    </div>
                </div>
                @endforeach
            </div>
        </div>
    </section>

    <!-- Transportation Options -->
    <section class="py-16">
        <div class="container mx-auto px-4">
            <div class="text-center mb-12">
                <h2 class="text-3xl font-bold text-gray-800 mb-4">Transportation Options</h2>
                <p class="text-gray-600 max-w-2xl mx-auto">
                    Choose from various transportation methods to reach our lodge comfortably and conveniently
                </p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                @foreach($transportOptions as $index => $option)
                <div class="bg-white rounded-lg shadow-lg p-6 hover:shadow-xl transition-shadow duration-300 border-t-4 {{ $index === 0 ? 'border-blue-500' : ($index === 1 ? 'border-green-500' : 'border-amber-500') }}">
                    <div class="text-center mb-4">
                        <div class="inline-flex items-center justify-center w-16 h-16 {{ $index === 0 ? 'bg-blue-100' : ($index === 1 ? 'bg-green-100' : 'bg-amber-100') }} rounded-full mb-4">
                            @if($index === 0)
                                <svg class="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 17a2 2 0 11-4 0 2 2 0 014 0zM19 17a2 2 0 11-4 0 2 2 0 014 0z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16V6a1 1 0 00-1-1H4a1 1 0 00-1 1v10a1 1 0 001 1h1m8-1a1 1 0 01-1 1H9m4-1V8a1 1 0 011-1h2.586a1 1 0 01.707.293l2.414 2.414a1 1 0 01.293.707V16a1 1 0 01-1 1h-1m-6-1a1 1 0 001 1h1M5 17a2 2 0 104 0m-4 0a2 2 0 114 0m6 0a2 2 0 104 0m-4 0a2 2 0 114 0"></path>
                                </svg>
                            @elseif($index === 1)
                                <svg class="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                                </svg>
                            @else
                                <svg class="w-8 h-8 text-amber-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                </svg>
                            @endif
                        </div>
                        <h3 class="text-xl font-semibold text-gray-800 mb-2">{{ $option['type'] }}</h3>
                    </div>
                    <p class="text-gray-600 mb-4">{{ $option['description'] }}</p>
                    <div class="space-y-2 text-sm">
                        <div class="flex justify-between">
                            <span class="text-gray-500">Duration:</span>
                            <span class="font-medium">{{ $option['duration'] }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-500">Cost:</span>
                            <span class="font-medium text-amber-600">{{ $option['cost'] }}</span>
                        </div>
                    </div>
                </div>
                @endforeach
            </div>
        </div>
    </section>

    <!-- Contact for Directions -->
    <section class="py-16 bg-amber-900 text-white">
        <div class="container mx-auto px-4 text-center">
            <h2 class="text-3xl font-bold mb-6">Need Detailed Directions?</h2>
            <p class="text-xl mb-8 max-w-2xl mx-auto">
                Our team is happy to provide detailed driving directions and help arrange transportation
            </p>
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <a href="{{ route('contact.index') }}" 
                   class="inline-block bg-white text-amber-900 px-8 py-4 rounded-full font-semibold hover:bg-gray-100 transition-colors duration-300">
                    Contact Us
                </a>
                <a href="{{ route('location.getting-here') }}" 
                   class="inline-block border-2 border-white text-white px-8 py-4 rounded-full font-semibold hover:bg-white hover:text-amber-900 transition-colors duration-300">
                    Getting Here Guide
                </a>
            </div>
        </div>
    </section>
</div>
@endsection
