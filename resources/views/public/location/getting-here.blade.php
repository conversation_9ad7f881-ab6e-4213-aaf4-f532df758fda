@extends('layouts.public')

@section('title', 'Getting Here - Malombo Safari Lodge')
@section('description', 'Detailed directions and travel guide for reaching Malombo Safari Lodge. Find the best routes, transportation options, and travel tips.')

@section('content')
<div class="min-h-screen bg-gradient-to-b from-amber-50 to-white">
    <!-- Hero Section -->
    <section class="relative py-20 bg-gradient-to-r from-blue-900 via-blue-800 to-green-900">
        <div class="absolute inset-0 bg-black/30"></div>
        <div class="container mx-auto px-4 relative z-10">
            <div class="text-center text-white">
                <h1 class="text-5xl font-bold mb-6">Getting Here</h1>
                <p class="text-xl max-w-3xl mx-auto leading-relaxed">
                    Your comprehensive guide to reaching Malombo Safari Lodge with ease and comfort
                </p>
            </div>
        </div>
    </section>

    <!-- Driving Directions -->
    <section class="py-16">
        <div class="container mx-auto px-4">
            <div class="max-w-4xl mx-auto">
                <h2 class="text-3xl font-bold text-gray-800 mb-8 text-center">Driving Directions</h2>
                
                <!-- From Johannesburg -->
                <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
                    <h3 class="text-2xl font-semibold text-amber-600 mb-4 flex items-center">
                        <svg class="w-6 h-6 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        </svg>
                        From O.R. Tambo International Airport / Johannesburg
                    </h3>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                        <div class="text-center p-4 bg-blue-50 rounded-lg">
                            <div class="text-2xl font-bold text-blue-600">420 km</div>
                            <div class="text-sm text-gray-600">Total Distance</div>
                        </div>
                        <div class="text-center p-4 bg-green-50 rounded-lg">
                            <div class="text-2xl font-bold text-green-600">4.5 hours</div>
                            <div class="text-sm text-gray-600">Drive Time</div>
                        </div>
                        <div class="text-center p-4 bg-amber-50 rounded-lg">
                            <div class="text-2xl font-bold text-amber-600">Easy</div>
                            <div class="text-sm text-gray-600">Difficulty</div>
                        </div>
                    </div>
                    <div class="space-y-3">
                        <div class="flex items-start space-x-3">
                            <div class="bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold">1</div>
                            <p class="text-gray-700">Exit O.R. Tambo Airport and follow signs to N12 East towards Witbank</p>
                        </div>
                        <div class="flex items-start space-x-3">
                            <div class="bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold">2</div>
                            <p class="text-gray-700">Continue on N12 for approximately 60km to Witbank</p>
                        </div>
                        <div class="flex items-start space-x-3">
                            <div class="bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold">3</div>
                            <p class="text-gray-700">Take N4 East towards Nelspruit/Mbombela (Toll road)</p>
                        </div>
                        <div class="flex items-start space-x-3">
                            <div class="bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold">4</div>
                            <p class="text-gray-700">Continue on N4 for approximately 280km to Nelspruit</p>
                        </div>
                        <div class="flex items-start space-x-3">
                            <div class="bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold">5</div>
                            <p class="text-gray-700">Take R40 North towards White River/Hazyview</p>
                        </div>
                        <div class="flex items-start space-x-3">
                            <div class="bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold">6</div>
                            <p class="text-gray-700">Follow signs to our private game reserve entrance</p>
                        </div>
                    </div>
                </div>

                <!-- From Kruger Airport -->
                <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
                    <h3 class="text-2xl font-semibold text-green-600 mb-4 flex items-center">
                        <svg class="w-6 h-6 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path>
                        </svg>
                        From Kruger Mpumalanga International Airport (MQP)
                    </h3>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                        <div class="text-center p-4 bg-blue-50 rounded-lg">
                            <div class="text-2xl font-bold text-blue-600">45 km</div>
                            <div class="text-sm text-gray-600">Total Distance</div>
                        </div>
                        <div class="text-center p-4 bg-green-50 rounded-lg">
                            <div class="text-2xl font-bold text-green-600">45 min</div>
                            <div class="text-sm text-gray-600">Drive Time</div>
                        </div>
                        <div class="text-center p-4 bg-amber-50 rounded-lg">
                            <div class="text-2xl font-bold text-amber-600">Very Easy</div>
                            <div class="text-sm text-gray-600">Difficulty</div>
                        </div>
                    </div>
                    <div class="space-y-3">
                        <div class="flex items-start space-x-3">
                            <div class="bg-green-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold">1</div>
                            <p class="text-gray-700">Exit airport and head towards R538</p>
                        </div>
                        <div class="flex items-start space-x-3">
                            <div class="bg-green-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold">2</div>
                            <p class="text-gray-700">Turn left onto R538 towards Hazyview</p>
                        </div>
                        <div class="flex items-start space-x-3">
                            <div class="bg-green-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold">3</div>
                            <p class="text-gray-700">Continue for 35km following signs to private reserves</p>
                        </div>
                        <div class="flex items-start space-x-3">
                            <div class="bg-green-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold">4</div>
                            <p class="text-gray-700">Turn right at Malombo Safari Lodge sign</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Important Information -->
    <section class="py-16 bg-gray-50">
        <div class="container mx-auto px-4">
            <div class="max-w-4xl mx-auto">
                <h2 class="text-3xl font-bold text-gray-800 mb-8 text-center">Important Travel Information</h2>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                    <!-- Vehicle Requirements -->
                    <div class="bg-white rounded-lg shadow-lg p-6">
                        <h3 class="text-xl font-semibold text-amber-600 mb-4 flex items-center">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 17a2 2 0 11-4 0 2 2 0 014 0zM19 17a2 2 0 11-4 0 2 2 0 014 0z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16V6a1 1 0 00-1-1H4a1 1 0 00-1 1v10a1 1 0 001 1h1m8-1a1 1 0 01-1 1H9m4-1V8a1 1 0 011-1h2.586a1 1 0 01.707.293l2.414 2.414a1 1 0 01.293.707V16a1 1 0 01-1 1h-1m-6-1a1 1 0 001 1h1M5 17a2 2 0 104 0m-4 0a2 2 0 114 0m6 0a2 2 0 104 0m-4 0a2 2 0 114 0"></path>
                            </svg>
                            Vehicle Requirements
                        </h3>
                        <ul class="space-y-2 text-gray-700">
                            <li class="flex items-start space-x-2">
                                <svg class="w-4 h-4 text-green-500 mt-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                <span>Standard sedan vehicles are suitable</span>
                            </li>
                            <li class="flex items-start space-x-2">
                                <svg class="w-4 h-4 text-green-500 mt-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                <span>4x4 not required for access road</span>
                            </li>
                            <li class="flex items-start space-x-2">
                                <svg class="w-4 h-4 text-green-500 mt-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                <span>Paved roads all the way to lodge</span>
                            </li>
                            <li class="flex items-start space-x-2">
                                <svg class="w-4 h-4 text-amber-500 mt-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.464 0L3.34 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                                </svg>
                                <span>Speed limit: 40km/h in reserve</span>
                            </li>
                        </ul>
                    </div>

                    <!-- What to Bring -->
                    <div class="bg-white rounded-lg shadow-lg p-6">
                        <h3 class="text-xl font-semibold text-green-600 mb-4 flex items-center">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                            </svg>
                            What to Bring
                        </h3>
                        <ul class="space-y-2 text-gray-700">
                            <li class="flex items-start space-x-2">
                                <svg class="w-4 h-4 text-green-500 mt-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                <span>Valid driver's license</span>
                            </li>
                            <li class="flex items-start space-x-2">
                                <svg class="w-4 h-4 text-green-500 mt-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                <span>Vehicle registration documents</span>
                            </li>
                            <li class="flex items-start space-x-2">
                                <svg class="w-4 h-4 text-green-500 mt-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                <span>GPS device or smartphone</span>
                            </li>
                            <li class="flex items-start space-x-2">
                                <svg class="w-4 h-4 text-green-500 mt-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                <span>Emergency contact details</span>
                            </li>
                        </ul>
                    </div>

                    <!-- Toll Roads -->
                    <div class="bg-white rounded-lg shadow-lg p-6">
                        <h3 class="text-xl font-semibold text-blue-600 mb-4 flex items-center">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v2a2 2 0 00-2 2z"></path>
                            </svg>
                            Toll Road Information
                        </h3>
                        <div class="space-y-3">
                            <div class="flex justify-between items-center p-3 bg-gray-50 rounded">
                                <span class="text-gray-700">Johannesburg to Nelspruit</span>
                                <span class="font-semibold text-blue-600">±R150</span>
                            </div>
                            <div class="flex justify-between items-center p-3 bg-gray-50 rounded">
                                <span class="text-gray-700">Number of toll gates</span>
                                <span class="font-semibold text-blue-600">6 gates</span>
                            </div>
                            <p class="text-sm text-gray-600">
                                <strong>Tip:</strong> Keep cash handy for toll payments. Most gates accept credit cards but cash is faster.
                            </p>
                        </div>
                    </div>

                    <!-- Fuel Stops -->
                    <div class="bg-white rounded-lg shadow-lg p-6">
                        <h3 class="text-xl font-semibold text-red-600 mb-4 flex items-center">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                            </svg>
                            Recommended Fuel Stops
                        </h3>
                        <ul class="space-y-2 text-gray-700">
                            <li class="flex items-start space-x-2">
                                <svg class="w-4 h-4 text-green-500 mt-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                <span><strong>Witbank:</strong> Multiple service stations</span>
                            </li>
                            <li class="flex items-start space-x-2">
                                <svg class="w-4 h-4 text-green-500 mt-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                <span><strong>Middelburg:</strong> Shell Ultra City</span>
                            </li>
                            <li class="flex items-start space-x-2">
                                <svg class="w-4 h-4 text-green-500 mt-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                <span><strong>Nelspruit:</strong> Various stations before R40</span>
                            </li>
                            <li class="flex items-start space-x-2">
                                <svg class="w-4 h-4 text-amber-500 mt-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.464 0L3.34 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                                </svg>
                                <span><strong>Last chance:</strong> White River (recommended)</span>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Emergency Contacts -->
    <section class="py-16 bg-red-50">
        <div class="container mx-auto px-4">
            <div class="max-w-2xl mx-auto text-center">
                <h2 class="text-3xl font-bold text-gray-800 mb-8">Emergency Contacts</h2>
                <div class="bg-white rounded-lg shadow-lg p-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="text-center">
                            <div class="bg-red-100 p-3 rounded-full w-16 h-16 mx-auto mb-4 flex items-center justify-center">
                                <svg class="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                                </svg>
                            </div>
                            <h3 class="font-semibold text-lg mb-2">Lodge Emergency</h3>
                            <p class="text-gray-600">+27 13 123 4567</p>
                            <p class="text-sm text-gray-500">Available 24/7</p>
                        </div>
                        <div class="text-center">
                            <div class="bg-blue-100 p-3 rounded-full w-16 h-16 mx-auto mb-4 flex items-center justify-center">
                                <svg class="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192L5.636 18.364M12 2.25a9.75 9.75 0 100 19.5 9.75 9.75 0 000-19.5z"></path>
                                </svg>
                            </div>
                            <h3 class="font-semibold text-lg mb-2">General Emergency</h3>
                            <p class="text-gray-600">10177</p>
                            <p class="text-sm text-gray-500">National emergency line</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Contact Us -->
    <section class="py-16 bg-amber-900 text-white">
        <div class="container mx-auto px-4 text-center">
            <h2 class="text-3xl font-bold mb-6">Need More Help?</h2>
            <p class="text-xl mb-8 max-w-2xl mx-auto">
                Our team is ready to assist with detailed directions and travel arrangements
            </p>
            <a href="{{ route('contact.index') }}" 
               class="inline-block bg-white text-amber-900 px-8 py-4 rounded-full font-semibold hover:bg-gray-100 transition-colors duration-300">
                Contact Our Travel Team
            </a>
        </div>
    </section>
</div>
@endsection
