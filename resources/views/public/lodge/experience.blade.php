@extends('layouts.app')

@section('title', $seoData['title'])
@section('meta_description', $seoData['description'])
@section('canonical', $seoData['canonical'])

@section('content')
<div class="relative">
    <!-- Hero Section -->
    <section class="relative bg-gradient-to-b from-gray-900 to-gray-800 text-white py-20">
        <div class="absolute inset-0 bg-black opacity-50"></div>
        <div class="absolute inset-0 bg-cover bg-center" style="background-image: url('{{ asset('images/malombo_logo.jpg') }}')"></div>
        
        <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <!-- Breadcrumb -->
            <nav class="mb-8">
                <ol class="flex items-center space-x-2 text-sm">
                    <li><a href="{{ route('home') }}" class="hover:text-green-300">Home</a></li>
                    <li class="text-gray-300">/</li>
                    <li><a href="{{ route('lodge.index') }}" class="hover:text-green-300">The Lodge</a></li>
                    <li class="text-gray-300">/</li>
                    <li class="text-green-300">Safari Experience</li>
                </ol>
            </nav>

            <div class="max-w-4xl">
                <h1 class="text-4xl md:text-6xl font-bold mb-6">
                    Safari Experience
                </h1>
                <p class="text-xl md:text-2xl mb-8 leading-relaxed">
                    Immerse yourself in authentic African wilderness. From sunrise game drives to starlit dinners by the river.
                </p>
            </div>
        </div>
    </section>

    <!-- Experience Overview -->
    <section class="py-16 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-3xl font-bold text-gray-900 mb-4">
                    Your Complete Safari Experience
                </h2>
                <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                    At Malombo Selous Forest Camp, every moment is crafted to connect you with the raw beauty and wonder of Africa's wilderness.
                </p>
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center mb-16">
                <div>
                    <h3 class="text-2xl font-bold text-gray-900 mb-6">Authentic Wilderness Adventures</h3>
                    <p class="text-lg text-gray-700 mb-6">
                        Wake up to the sounds of the African bush and embark on adventures that will stay with you forever. Our experienced guides will take you deep into Nyerere National Park, where elephants roam free and predators hunt under the vast African sky.
                    </p>
                    <p class="text-lg text-gray-700 mb-6">
                        From thrilling game drives to peaceful boat safaris along the Rufiji River, every activity is designed to showcase the incredible diversity of wildlife and landscapes that make this region so special.
                    </p>
                </div>
                <div class="bg-green-100 p-8 rounded-lg">
                    <h4 class="text-xl font-semibold text-green-800 mb-4">What Makes Us Different</h4>
                    <ul class="space-y-3 text-green-700">
                        <li class="flex items-start">
                            <svg class="w-5 h-5 mr-3 mt-1 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                            </svg>
                            Expert local guides with decades of experience
                        </li>
                        <li class="flex items-start">
                            <svg class="w-5 h-5 mr-3 mt-1 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                            </svg>
                            Small groups for personalized experiences
                        </li>
                        <li class="flex items-start">
                            <svg class="w-5 h-5 mr-3 mt-1 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                            </svg>
                            Access to remote, less crowded areas
                        </li>
                        <li class="flex items-start">
                            <svg class="w-5 h-5 mr-3 mt-1 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                            </svg>
                            Sustainable tourism practices
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </section>

    <!-- Activities Grid -->
    @if($activities->count() > 0)
        <section class="py-16 bg-gray-50">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="text-center mb-12">
                    <h2 class="text-3xl font-bold text-gray-900 mb-4">
                        Safari Activities
                    </h2>
                    <p class="text-xl text-gray-600">
                        Choose from our range of exciting wildlife experiences
                    </p>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                    @foreach($activities as $activity)
                        <div class="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300">
                            <!-- Activity Image -->
                            <div class="relative h-48 bg-gray-200">
                                @if($activity->getFirstMediaUrl('featured_image'))
                                    <img src="{{ $activity->getFirstMediaUrl('featured_image', 'preview') }}" 
                                         alt="{{ $activity->title }}"
                                         class="w-full h-full object-cover">
                                @else
                                    <div class="w-full h-full bg-gradient-to-br from-green-100 to-green-200 flex items-center justify-center">
                                        <svg class="w-12 h-12 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M2 6a2 2 0 012-2h5l2 2h5a2 2 0 012 2v6a2 2 0 01-2 2H4a2 2 0 01-2-2V6z"></path>
                                        </svg>
                                    </div>
                                @endif
                                
                                <!-- Category Badge -->
                                @if($activity->category)
                                    <div class="absolute top-4 left-4">
                                        <span class="bg-green-600 text-white px-3 py-1 rounded-full text-sm font-medium">
                                            {{ ucfirst($activity->category) }}
                                        </span>
                                    </div>
                                @endif
                            </div>

                            <!-- Activity Content -->
                            <div class="p-6">
                                <h3 class="text-xl font-bold text-gray-900 mb-3">
                                    {{ $activity->title }}
                                </h3>
                                
                                <p class="text-gray-600 mb-4 line-clamp-3">
                                    {{ Str::limit($activity->description, 120) }}
                                </p>

                                @if($activity->duration)
                                    <div class="flex items-center text-sm text-gray-500 mb-4">
                                        <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z"></path>
                                        </svg>
                                        Duration: {{ $activity->duration }}
                                    </div>
                                @endif

                                <div class="flex justify-between items-center">
                                    <a href="{{ route('activities.show', $activity->slug) }}" 
                                       class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors duration-200">
                                        Learn More
                                    </a>
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>

                <div class="text-center mt-12">
                    <a href="{{ route('activities.index') }}" 
                       class="bg-green-600 text-white px-8 py-3 rounded-lg hover:bg-green-700 transition-colors duration-200 inline-block">
                        View All Activities
                    </a>
                </div>
            </div>
        </section>
    @endif

    <!-- Sample Itinerary -->
    <section class="py-16 bg-white">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12">
                <h2 class="text-3xl font-bold text-gray-900 mb-4">
                    A Day in the Life
                </h2>
                <p class="text-xl text-gray-600">
                    Experience a typical day at Malombo Selous Forest Camp
                </p>
            </div>

            <div class="space-y-8">
                <div class="flex items-start">
                    <div class="flex-shrink-0 w-20 h-20 bg-orange-100 rounded-full flex items-center justify-center">
                        <svg class="w-8 h-8 text-orange-600" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M10 2L3 7v11a2 2 0 002 2h4v-6h2v6h4a2 2 0 002-2V7l-7-5z"></path>
                        </svg>
                    </div>
                    <div class="ml-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">5:30 AM - Wake up Call</h3>
                        <p class="text-gray-700">Start your day with fresh coffee or tea as the African dawn breaks over the bush. The early morning offers the best wildlife viewing opportunities.</p>
                    </div>
                </div>

                <div class="flex items-start">
                    <div class="flex-shrink-0 w-20 h-20 bg-green-100 rounded-full flex items-center justify-center">
                        <svg class="w-8 h-8 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M8 16.5a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0zM15 16.5a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0z"></path>
                            <path d="M3 4a1 1 0 00-1 1v10a1 1 0 001 1h1.05a2.5 2.5 0 014.9 0H10a1 1 0 001-1V5a1 1 0 00-1-1H3zM14 7a1 1 0 00-1 1v6.05A2.5 2.5 0 0115.95 16H17a1 1 0 001-1V8a1 1 0 00-1-1h-3z"></path>
                        </svg>
                    </div>
                    <div class="ml-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">6:00 AM - Morning Game Drive</h3>
                        <p class="text-gray-700">Embark on your morning safari adventure. This is when predators are most active and the lighting is perfect for photography.</p>
                    </div>
                </div>

                <div class="flex items-start">
                    <div class="flex-shrink-0 w-20 h-20 bg-blue-100 rounded-full flex items-center justify-center">
                        <svg class="w-8 h-8 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"></path>
                        </svg>
                    </div>
                    <div class="ml-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">9:30 AM - Bush Breakfast</h3>
                        <p class="text-gray-700">Enjoy a delicious breakfast in the wilderness, surrounded by the sights and sounds of the African bush.</p>
                    </div>
                </div>

                <div class="flex items-start">
                    <div class="flex-shrink-0 w-20 h-20 bg-purple-100 rounded-full flex items-center justify-center">
                        <svg class="w-8 h-8 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <div class="ml-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">12:00 PM - Lunch & Rest</h3>
                        <p class="text-gray-700">Return to camp for lunch and relaxation during the heat of the day. Take a nap, read a book, or enjoy the pool.</p>
                    </div>
                </div>

                <div class="flex items-start">
                    <div class="flex-shrink-0 w-20 h-20 bg-yellow-100 rounded-full flex items-center justify-center">
                        <svg class="w-8 h-8 text-yellow-600" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z"></path>
                        </svg>
                    </div>
                    <div class="ml-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">4:00 PM - Afternoon Activity</h3>
                        <p class="text-gray-700">Choose from boat safari, walking safari, or another game drive. Each offers unique perspectives on the wildlife.</p>
                    </div>
                </div>

                <div class="flex items-start">
                    <div class="flex-shrink-0 w-20 h-20 bg-red-100 rounded-full flex items-center justify-center">
                        <svg class="w-8 h-8 text-red-600" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z"></path>
                        </svg>
                    </div>
                    <div class="ml-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">7:30 PM - Sundowner & Dinner</h3>
                        <p class="text-gray-700">Watch the African sunset with drinks in hand, followed by a gourmet dinner under the stars with traditional entertainment.</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Call to Action -->
    <section class="py-16 bg-green-600 text-white">
        <div class="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
            <h2 class="text-3xl font-bold mb-4">
                Ready for Your Safari Adventure?
            </h2>
            <p class="text-xl mb-8">
                Join us at Malombo Selous Forest Camp for an unforgettable African wilderness experience.
            </p>
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <a href="{{ route('contact.index') }}" 
                   class="bg-white text-green-600 px-8 py-3 rounded-lg hover:bg-gray-100 transition-colors duration-200">
                    Contact Us
                </a>
                <a href="{{ route('booking.index') }}" 
                   class="bg-green-700 text-white px-8 py-3 rounded-lg hover:bg-green-800 transition-colors duration-200">
                    Book Your Safari
                </a>
            </div>
        </div>
    </section>
</div>
@endsection
