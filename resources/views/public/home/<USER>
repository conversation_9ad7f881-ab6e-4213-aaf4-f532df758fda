@extends('layouts.app')

@section('title', $seoData['title'] ?? setting('site_name', 'Malombo Selous Forest Camp'))
@section('meta_description', $seoData['description'] ?? setting('seo_meta_description', 'Experience luxury eco-lodge accommodation in Nyerere National Park (Selous), Tanzania.'))
@section('meta_keywords', $seoData['keywords'] ?? setting('seo_keywords', 'Selous Game Reserve, Nyerere National Park, Tanzania Safari, Eco Lodge, Wildlife, Rufiji River'))

@push('head')
    {{-- Structured Data --}}
    @if(isset($structuredData))
        @foreach($structuredData as $data)
            <x-structured-data :data="$data" />
        @endforeach
    @endif
@endpush

@section('content')
{{-- Hero Section --}}
<section class="relative min-h-screen flex items-center justify-center bg-gradient-to-r from-amber-600 to-amber-800 text-white">
    <div class="absolute inset-0 bg-black opacity-40"></div>
    <div class="relative z-10 text-center max-w-4xl mx-auto px-4">
        <h1 class="text-5xl md:text-7xl font-bold font-serif mb-6">
            {{ setting('site_name', 'Malombo Selous Forest Camp') }}
        </h1>
        <p class="text-xl md:text-2xl mb-8">
            {{ setting('site_tagline', 'Where Wildlife Meets Luxury in the Heart of Africa') }}
        </p>
        <p class="text-lg mb-12 max-w-2xl mx-auto opacity-90">
            Experience the authentic African wilderness in Nyerere National Park (formerly Selous Game Reserve). 
            Luxury eco-lodge accommodation with unparalleled wildlife experiences.
        </p>
        <div class="space-y-4 sm:space-y-0 sm:space-x-4 sm:flex sm:justify-center">
            <button onclick="openBookingModal()" 
                    class="bg-amber-600 hover:bg-amber-700 text-white px-8 py-4 rounded-full text-lg font-semibold transition-all duration-300">
                Book Your Safari
            </button>
            <a href="{{ route('lodge.index') }}" 
               class="bg-transparent border-2 border-white hover:bg-white hover:text-gray-900 text-white px-8 py-4 rounded-full text-lg font-semibold transition-all duration-300 inline-block">
                Discover The Lodge
            </a>
        </div>
    </div>
</section>

{{-- Quick Introduction --}}
<section class="py-20 bg-white">
    <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16">
            <h2 class="text-4xl md:text-5xl font-bold text-gray-900 mb-6 font-serif">
                Welcome to Paradise
            </h2>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
                Nestled on the banks of the mighty Rufiji River in the heart of Nyerere National Park, 
                Malombo Selous Forest Camp offers an exclusive gateway to one of Africa's last great wilderness areas.
            </p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div class="text-center">
                <div class="bg-amber-100 rounded-full w-20 h-20 flex items-center justify-center mx-auto mb-4">
                    <svg class="w-10 h-10 text-amber-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 mb-2">Remote & Exclusive</h3>
                <p class="text-gray-600">Experience true wilderness in one of Tanzania's most pristine national parks.</p>
            </div>
            
            <div class="text-center">
                <div class="bg-green-100 rounded-full w-20 h-20 flex items-center justify-center mx-auto mb-4">
                    <svg class="w-10 h-10 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"/>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 mb-2">Luxury Eco-Lodge</h3>
                <p class="text-gray-600">Sustainable luxury accommodation that harmonizes with the natural environment.</p>
            </div>
            
            <div class="text-center">
                <div class="bg-blue-100 rounded-full w-20 h-20 flex items-center justify-center mx-auto mb-4">
                    <svg class="w-10 h-10 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h8m-5-15S3 4 3 9s3 6 3 6 3-1 3-6-3-9-3-9z"/>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 mb-2">Unforgettable Wildlife</h3>
                <p class="text-gray-600">Encounter elephants, lions, hippos, and diverse bird species in their natural habitat.</p>
            </div>
        </div>
    </div>
</section>

{{-- Featured Accommodations --}}
@if($featuredAccommodations->count() > 0)
<section class="py-20 bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16">
            <h2 class="text-4xl md:text-5xl font-bold text-gray-900 mb-6 font-serif">
                Luxury Accommodation
            </h2>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                Choose from our carefully designed accommodations, each offering unique perspectives of the African wilderness.
            </p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-{{ $featuredAccommodations->count() == 2 ? '2' : '3' }} gap-8">
            @foreach($featuredAccommodations as $accommodation)
                <div class="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300">
                    @if($accommodation->getFirstMediaUrl('featured_image'))
                        <div class="h-64 bg-cover bg-center" style="background-image: url('{{ $accommodation->getFirstMediaUrl('featured_image') }}');">
                        </div>
                    @else
                        <div class="h-64 bg-gradient-to-r from-amber-400 to-amber-600 flex items-center justify-center">
                            <span class="text-white text-2xl font-semibold">{{ $accommodation->display_type }}</span>
                        </div>
                    @endif
                    
                    <div class="p-6">
                        <h3 class="text-2xl font-bold text-gray-900 mb-2">{{ $accommodation->title }}</h3>
                        <p class="text-gray-600 mb-4">{{ $accommodation->short_intro }}</p>
                        
                        <div class="flex justify-between items-center mb-4">
                            <span class="text-sm text-amber-600 font-semibold">{{ $accommodation->display_type }}</span>
                            <span class="text-sm text-gray-500">Max {{ $accommodation->max_occupancy }} guests</span>
                        </div>
                        
                        <a href="{{ route('accommodation.show', $accommodation->slug) }}" 
                           class="inline-block bg-amber-600 hover:bg-amber-700 text-white px-6 py-2 rounded-full font-medium transition-colors duration-300">
                            View Details
                        </a>
                    </div>
                </div>
            @endforeach
        </div>
        
        <div class="text-center mt-12">
            <a href="{{ route('accommodation.index') }}" 
               class="inline-block bg-transparent border-2 border-amber-600 text-amber-600 hover:bg-amber-600 hover:text-white px-8 py-3 rounded-full font-semibold transition-all duration-300">
                View All Accommodation
            </a>
        </div>
    </div>
</section>
@endif

{{-- Activities Grid --}}
@if(isset($topActivities) && $topActivities->count() > 0)
<section class="py-16 bg-stone-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16">
            <h2 class="text-4xl md:text-5xl font-bold text-stone-800 mb-6">
                Safari Adventures Await
            </h2>
            <p class="text-xl text-stone-600 max-w-3xl mx-auto leading-relaxed">
                From thrilling game drives to peaceful boat safaris, discover the diverse experiences that make Selous Game Reserve extraordinary.
            </p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            @foreach($topActivities as $activity)
            <div class="bg-white rounded-xl overflow-hidden shadow-lg hover:shadow-xl transition-shadow duration-300 group">
                @if($activity->getFirstMediaUrl('featured_image'))
                <div class="h-64 bg-stone-300 relative overflow-hidden">
                    <img 
                        src="{{ $activity->getFirstMediaUrl('featured_image') }}" 
                        alt="{{ $activity->title }}"
                        class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                    >
                    @if($activity->duration)
                    <div class="absolute top-4 right-4">
                        <span class="bg-green-600 text-white px-3 py-1 rounded-full text-sm font-medium">
                            {{ $activity->duration }}
                        </span>
                    </div>
                    @endif
                </div>
                @endif
                
                <div class="p-6">
                    <h3 class="text-xl font-bold text-stone-800 mb-3 group-hover:text-green-600 transition-colors">
                        {{ $activity->title }}
                    </h3>
                    
                    @if($activity->short_description)
                    <p class="text-stone-600 mb-4 leading-relaxed">
                        {{ Str::limit($activity->short_description, 120) }}
                    </p>
                    @endif
                    
                    <div class="flex items-center justify-between">
                        @if($activity->difficulty_level)
                        <span class="text-sm font-medium px-3 py-1 rounded-full
                            @if($activity->difficulty_level === 'easy') bg-green-100 text-green-700
                            @elseif($activity->difficulty_level === 'moderate') bg-yellow-100 text-yellow-700
                            @else bg-red-100 text-red-700
                            @endif">
                            {{ ucfirst($activity->difficulty_level) }}
                        </span>
                        @endif
                        
                        <a href="{{ route('activities.show', $activity->slug) }}" 
                           class="text-green-600 hover:text-green-700 font-semibold text-sm group-hover:underline">
                            Learn More →
                        </a>
                    </div>
                </div>
            </div>
            @endforeach
        </div>
        
        <div class="text-center mt-12">
            <a href="{{ route('activities.index') }}" 
               class="inline-flex items-center bg-green-600 hover:bg-green-700 text-white px-8 py-4 rounded-lg font-semibold text-lg transition-colors duration-200 shadow-lg hover:shadow-xl">
                View All Activities
                <svg class="ml-2 w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"/>
                </svg>
            </a>
        </div>
    </div>
</section>
@endif

{{-- Facilities Highlights --}}
@if(isset($keyFacilities) && $keyFacilities->count() > 0)
<section class="py-16 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16">
            <h2 class="text-4xl md:text-5xl font-bold text-stone-800 mb-6">
                Camp Facilities & Amenities
            </h2>
            <p class="text-xl text-stone-600 max-w-3xl mx-auto leading-relaxed">
                Modern comfort meets authentic wilderness. Our carefully designed facilities enhance your safari experience while respecting the natural environment.
            </p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            @foreach($keyFacilities->take(4) as $facility)
            <div class="text-center group">
                <div class="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:bg-green-600 transition-colors duration-300">
                    @if($facility->icon)
                    <span class="text-3xl">{{ $facility->icon }}</span>
                    @else
                    <svg class="w-10 h-10 text-green-600 group-hover:text-white transition-colors duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                    </svg>
                    @endif
                </div>
                <h3 class="text-xl font-bold text-stone-800 mb-3">{{ $facility->title }}</h3>
                @if($facility->description)
                <p class="text-stone-600 leading-relaxed">
                    {{ Str::limit($facility->description, 100) }}
                </p>
                @endif
            </div>
            @endforeach
        </div>
        
        <div class="text-center mt-12">
            <a href="{{ route('facilities.index') }}" 
               class="text-green-600 hover:text-green-700 font-semibold text-lg hover:underline">
                Explore All Facilities →
            </a>
        </div>
    </div>
</section>
@endif

{{-- Include Testimonials --}}
@include('public.home.partials.testimonials')

{{-- Include Location/Getting Here --}}
@include('public.home.partials.location')

{{-- CTA Banner --}}
<section class="py-20 bg-amber-600 text-white">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h2 class="text-4xl md:text-5xl font-bold mb-6 font-serif">
            Ready for Your African Adventure?
        </h2>
        <p class="text-xl mb-8 opacity-90">
            Join us for an extraordinary safari experience in the heart of Tanzania's largest national park. 
            Create memories that will last a lifetime.
        </p>
        <div class="space-y-4 sm:space-y-0 sm:space-x-4 sm:flex sm:justify-center">
            <button onclick="openBookingModal()" 
                    class="bg-white text-amber-600 hover:bg-gray-100 px-8 py-4 rounded-full text-lg font-semibold transition-all duration-300">
                Make a Reservation
            </button>
            <a href="{{ route('contact.index') }}" 
               class="bg-transparent border-2 border-white hover:bg-white hover:text-amber-600 text-white px-8 py-4 rounded-full text-lg font-semibold transition-all duration-300 inline-block">
                Contact Us
            </a>
        </div>
    </div>
</section>
@endsection
