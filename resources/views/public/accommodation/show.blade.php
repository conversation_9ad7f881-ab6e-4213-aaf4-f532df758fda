@extends('layouts.app')

@section('title', $seoData['title'] ?? $accommodation->title . ' | Safari Accommodation')
@section('meta_description', $seoData['description'] ?? $accommodation->short_intro)
@section('meta_keywords', $seoData['keywords'] ?? 'safari accommodation, ' . $accommodation->title)

@push('head')
    @if(isset($structuredData))
        @foreach($structuredData as $data)
            <x-structured-data :data="$data" />
        @endforeach
    @endif
@endpush

@section('content')
{{-- Hero Section with Image Gallery --}}
<section class="relative">
    @if($accommodation->getMedia('gallery')->count() > 0)
        <x-image-gallery 
            :images="$accommodation->getMedia('gallery')" 
            :alt="$accommodation->title"
            class="h-96 lg:h-[500px]"
        />
    @elseif($accommodation->getFirstMediaUrl('featured_image'))
        <div class="h-96 lg:h-[500px] bg-cover bg-center relative" 
             style="background-image: url('{{ $accommodation->getFirstMediaUrl('featured_image') }}')">
            <div class="absolute inset-0 bg-black bg-opacity-30"></div>
        </div>
    @else
        <div class="h-96 lg:h-[500px] bg-gradient-to-r from-forest-600 to-earth-600"></div>
    @endif
    
    {{-- Overlay Content --}}
    <div class="absolute inset-0 flex items-end">
        <div class="w-full bg-black bg-opacity-60 text-white p-6 lg:p-8">
            <div class="max-w-7xl mx-auto">
                <div class="flex flex-col lg:flex-row lg:items-end lg:justify-between">
                    <div>
                        <span class="inline-block bg-forest-600 text-white px-3 py-1 rounded-full text-sm font-medium mb-3">
                            {{ $accommodation->display_type }}
                        </span>
                        <h1 class="text-3xl lg:text-5xl font-bold mb-2 font-serif">
                            {{ $accommodation->title }}
                        </h1>
                        @if($accommodation->short_intro)
                            <p class="text-lg lg:text-xl opacity-90 max-w-2xl">
                                {{ $accommodation->short_intro }}
                            </p>
                        @endif
                    </div>
                    
                    <div class="mt-4 lg:mt-0">
                        <button onclick="openBookingModal()" 
                                class="bg-forest-600 hover:bg-forest-700 text-white px-8 py-4 rounded-full font-semibold text-lg transition-colors duration-200">
                            Book This Room
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

{{-- Main Content --}}
<section class="py-16 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-12">
            {{-- Main Content --}}
            <div class="lg:col-span-2">
                {{-- Description --}}
                @if($accommodation->description)
                    <div class="prose prose-lg prose-stone max-w-none mb-12">
                        {!! $accommodation->description !!}
                    </div>
                @endif
                
                {{-- Features & Amenities --}}
                @if($accommodation->features && count($accommodation->features) > 0)
                    <div class="mb-12">
                        <h2 class="text-2xl font-bold text-stone-800 mb-6">Features & Amenities</h2>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            @foreach($accommodation->features as $feature)
                                <div class="flex items-center p-4 bg-stone-50 rounded-lg">
                                    <svg class="w-5 h-5 text-forest-600 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                                    </svg>
                                    <span class="text-stone-700">{{ $feature }}</span>
                                </div>
                            @endforeach
                        </div>
                    </div>
                @endif
                
                {{-- Special Features --}}
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
                    @if($accommodation->wheelchair_access)
                        <div class="text-center p-4 bg-green-50 rounded-lg">
                            <svg class="w-8 h-8 text-green-600 mx-auto mb-2" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                            </svg>
                            <span class="text-sm font-medium text-green-800">Wheelchair Accessible</span>
                        </div>
                    @endif
                    
                    @if($accommodation->has_balcony)
                        <div class="text-center p-4 bg-blue-50 rounded-lg">
                            <svg class="w-8 h-8 text-blue-600 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-6m-2-5a3 3 0 11-6 0 3 3 0 016 0z"/>
                            </svg>
                            <span class="text-sm font-medium text-blue-800">Private Balcony</span>
                        </div>
                    @endif
                    
                    @if($accommodation->has_heated_shower)
                        <div class="text-center p-4 bg-orange-50 rounded-lg">
                            <svg class="w-8 h-8 text-orange-600 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"/>
                            </svg>
                            <span class="text-sm font-medium text-orange-800">Heated Shower</span>
                        </div>
                    @endif
                    
                    @if($accommodation->has_mini_bar)
                        <div class="text-center p-4 bg-purple-50 rounded-lg">
                            <svg class="w-8 h-8 text-purple-600 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 8h14M5 8a2 2 0 110-4h14a2 2 0 110 4M5 8v10a2 2 0 002 2h10a2 2 0 002-2V8m-9 4h4"/>
                            </svg>
                            <span class="text-sm font-medium text-purple-800">Mini Bar</span>
                        </div>
                    @endif
                </div>
            </div>
            
            {{-- Sidebar --}}
            <div class="lg:col-span-1">
                {{-- Booking Card --}}
                <div class="bg-stone-50 rounded-xl p-6 mb-8 sticky top-8">
                    <h3 class="text-xl font-bold text-stone-800 mb-4">Book This Accommodation</h3>
                    
                    <div class="space-y-4 mb-6">
                        <div class="flex justify-between items-center">
                            <span class="text-stone-600">Max Occupancy:</span>
                            <span class="font-medium text-stone-800">{{ $accommodation->max_occupancy }} guests</span>
                        </div>
                        
                        <div class="flex justify-between items-center">
                            <span class="text-stone-600">Room Type:</span>
                            <span class="font-medium text-stone-800">{{ $accommodation->display_type }}</span>
                        </div>
                        
                        @if($accommodation->is_featured)
                            <div class="bg-amber-100 border border-amber-200 rounded-lg p-3">
                                <div class="flex items-center">
                                    <svg class="w-5 h-5 text-amber-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                                    </svg>
                                    <span class="text-amber-800 font-medium text-sm">Featured Accommodation</span>
                                </div>
                            </div>
                        @endif
                    </div>
                    
                    <button onclick="openBookingModal()" 
                            class="w-full bg-forest-600 hover:bg-forest-700 text-white py-4 rounded-lg font-semibold text-lg transition-colors duration-200 mb-4">
                        Make Reservation
                    </button>
                    
                    <a href="{{ route('contact.index') }}" 
                       class="block w-full text-center border border-stone-300 text-stone-700 hover:bg-stone-100 py-3 rounded-lg font-medium transition-colors duration-200">
                        Ask Questions
                    </a>
                </div>
                
                {{-- Quick Info --}}
                <div class="bg-white border border-stone-200 rounded-xl p-6">
                    <h4 class="font-semibold text-stone-800 mb-4">Need to Know</h4>
                    <div class="space-y-3 text-sm">
                        <div class="flex items-start">
                            <svg class="w-4 h-4 text-green-600 mr-2 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                            </svg>
                            <span class="text-stone-600">24-hour response on all inquiries</span>
                        </div>
                        <div class="flex items-start">
                            <svg class="w-4 h-4 text-green-600 mr-2 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                            </svg>
                            <span class="text-stone-600">Free WiFi in main areas</span>
                        </div>
                        <div class="flex items-start">
                            <svg class="w-4 h-4 text-green-600 mr-2 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                            </svg>
                            <span class="text-stone-600">All meals included</span>
                        </div>
                        <div class="flex items-start">
                            <svg class="w-4 h-4 text-green-600 mr-2 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                            </svg>
                            <span class="text-stone-600">Daily game drives included</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

{{-- Related Accommodations --}}
@php
    $relatedAccommodations = \App\Models\Accommodation::published()
        ->where('id', '!=', $accommodation->id)
        ->take(3)
        ->get();
@endphp

@if($relatedAccommodations->count() > 0)
<section class="py-16 bg-stone-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="text-3xl font-bold text-stone-800 mb-4">Other Accommodations</h2>
            <p class="text-stone-600 max-w-2xl mx-auto">
                Explore our other luxury accommodations, each offering a unique safari experience.
            </p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
            @foreach($relatedAccommodations as $related)
                <div class="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300">
                    @if($related->getFirstMediaUrl('featured_image'))
                        <div class="h-48 bg-cover bg-center" style="background-image: url('{{ $related->getFirstMediaUrl('featured_image') }}')">
                        </div>
                    @else
                        <div class="h-48 bg-gradient-to-r from-forest-400 to-earth-400 flex items-center justify-center">
                            <span class="text-white text-xl font-semibold">{{ $related->display_type }}</span>
                        </div>
                    @endif
                    
                    <div class="p-6">
                        <h3 class="text-xl font-bold text-stone-800 mb-2">{{ $related->title }}</h3>
                        <p class="text-stone-600 mb-4">{{ Str::limit($related->short_intro, 100) }}</p>
                        <a href="{{ route('accommodation.show', $related->slug) }}" 
                           class="text-forest-600 hover:text-forest-700 font-medium">
                            View Details →
                        </a>
                    </div>
                </div>
            @endforeach
        </div>
    </div>
</section>
@endif
@endsection
