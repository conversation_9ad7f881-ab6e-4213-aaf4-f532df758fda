@extends('layouts.app')

@section('title', $seoData['title'] ?? 'Luxury Safari Accommodation | ' . setting('site_name', 'Malombo Selous Forest Camp'))
@section('meta_description', $seoData['description'] ?? 'Discover our exclusive safari accommodation in Nyerere National Park.')
@section('meta_keywords', $seoData['keywords'] ?? 'safari accommodation, luxury tents, eco lodge, Nyerere National Park')

@push('head')
    @if(isset($structuredData))
        @foreach($structuredData as $data)
            <x-structured-data :data="$data" />
        @endforeach
    @endif
@endpush

@section('content')
{{-- Hero Section --}}
<section class="relative h-96 flex items-center justify-center bg-gradient-to-r from-forest-600 to-earth-600">
    <div class="absolute inset-0 bg-black opacity-40"></div>
    <div class="relative z-10 text-center max-w-4xl mx-auto px-4">
        <h1 class="text-4xl md:text-6xl font-bold text-white mb-4 font-serif">
            Safari Accommodation
        </h1>
        <p class="text-xl text-white opacity-90">
            Luxury meets wilderness in our carefully designed accommodations
        </p>
    </div>
</section>

{{-- Accommodation Grid --}}
<section class="py-16 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        @if($accommodations->count() > 0)
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                @foreach($accommodations as $accommodation)
                    <div class="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300 group">
                        {{-- Image --}}
                        @if($accommodation->getFirstMediaUrl('featured_image'))
                            <div class="h-64 bg-cover bg-center relative overflow-hidden">
                                <img 
                                    src="{{ $accommodation->getFirstMediaUrl('featured_image') }}" 
                                    alt="{{ $accommodation->title }}"
                                    class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                                >
                                @if($accommodation->is_featured)
                                    <div class="absolute top-4 left-4">
                                        <span class="bg-amber-600 text-white px-3 py-1 rounded-full text-sm font-medium">
                                            Featured
                                        </span>
                                    </div>
                                @endif
                            </div>
                        @else
                            <div class="h-64 bg-gradient-to-r from-forest-400 to-earth-400 flex items-center justify-center">
                                <span class="text-white text-2xl font-semibold">{{ $accommodation->display_type }}</span>
                            </div>
                        @endif
                        
                        {{-- Content --}}
                        <div class="p-6">
                            <div class="flex justify-between items-start mb-3">
                                <h3 class="text-xl font-bold text-stone-800 group-hover:text-forest-600 transition-colors">
                                    {{ $accommodation->title }}
                                </h3>
                                <span class="text-sm text-forest-600 font-medium px-2 py-1 bg-forest-100 rounded">
                                    {{ $accommodation->display_type }}
                                </span>
                            </div>
                            
                            @if($accommodation->short_intro)
                                <p class="text-stone-600 mb-4 leading-relaxed">
                                    {{ Str::limit($accommodation->short_intro, 120) }}
                                </p>
                            @endif
                            
                            {{-- Features Preview --}}
                            @if($accommodation->features && count($accommodation->features) > 0)
                                <div class="mb-4">
                                    <div class="flex flex-wrap gap-2">
                                        @foreach(array_slice($accommodation->features, 0, 3) as $feature)
                                            <span class="text-xs bg-stone-100 text-stone-700 px-2 py-1 rounded">
                                                {{ $feature }}
                                            </span>
                                        @endforeach
                                        @if(count($accommodation->features) > 3)
                                            <span class="text-xs text-forest-600 font-medium">
                                                +{{ count($accommodation->features) - 3 }} more
                                            </span>
                                        @endif
                                    </div>
                                </div>
                            @endif
                            
                            {{-- Details --}}
                            <div class="flex justify-between items-center mb-4 text-sm text-stone-600">
                                <span class="flex items-center">
                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"/>
                                    </svg>
                                    Max {{ $accommodation->max_occupancy }} guests
                                </span>
                                
                                @if($accommodation->wheelchair_access)
                                    <span class="flex items-center text-green-600">
                                        <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                                        </svg>
                                        Accessible
                                    </span>
                                @endif
                            </div>
                            
                            {{-- Action --}}
                            <a href="{{ route('accommodation.show', $accommodation->slug) }}" 
                               class="block w-full bg-forest-600 hover:bg-forest-700 text-white text-center py-3 rounded-lg font-medium transition-colors duration-200">
                                View Details & Book
                            </a>
                        </div>
                    </div>
                @endforeach
            </div>
        @else
            <div class="text-center py-16">
                <div class="max-w-md mx-auto">
                    <svg class="w-16 h-16 text-stone-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-6m-2-5a3 3 0 11-6 0 3 3 0 016 0z"/>
                    </svg>
                    <h3 class="text-xl font-semibold text-stone-700 mb-2">No Accommodations Available</h3>
                    <p class="text-stone-600">Check back soon for our luxury safari accommodations.</p>
                </div>
            </div>
        @endif
    </div>
</section>

{{-- CTA Section --}}
<section class="py-16 bg-forest-600 text-white">
    <div class="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
        <h2 class="text-3xl md:text-4xl font-bold mb-6">Ready to Experience Luxury in the Wild?</h2>
        <p class="text-xl mb-8 opacity-90">
            Book your stay and immerse yourself in the pristine wilderness of Nyerere National Park.
        </p>
        <div class="space-y-4 sm:space-y-0 sm:space-x-4 sm:flex sm:justify-center">
            <button onclick="openBookingModal()" 
                    class="bg-white text-forest-600 hover:bg-stone-100 px-8 py-4 rounded-full text-lg font-semibold transition-all duration-300">
                Make a Reservation
            </button>
            <a href="{{ route('contact.index') }}" 
               class="bg-transparent border-2 border-white hover:bg-white hover:text-forest-600 text-white px-8 py-4 rounded-full text-lg font-semibold transition-all duration-300 inline-block">
                Contact Us
            </a>
        </div>
    </div>
</section>
@endsection
