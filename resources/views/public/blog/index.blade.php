@extends('layouts.public')

@section('title', 'Safari Blog - Malombo Safari Lodge')
@section('description', 'Read the latest stories, tips, and insights about African safari, wildlife conservation, and adventure travel from Malombo Safari Lodge.')

@section('content')
<div class="min-h-screen bg-gradient-to-b from-amber-50 to-white">
    <!-- Hero Section -->
    <section class="relative py-20 bg-gradient-to-r from-amber-900 via-amber-800 to-green-900">
        <div class="absolute inset-0 bg-black/30"></div>
        <div class="container mx-auto px-4 relative z-10">
            <div class="text-center text-white">
                <h1 class="text-5xl font-bold mb-6">Safari Stories & Insights</h1>
                <p class="text-xl max-w-3xl mx-auto leading-relaxed">
                    Discover the magic of African wildlife through expert insights, conservation stories, and travel tips
                </p>
            </div>
        </div>
    </section>

    <!-- Featured Post -->
    @if($featuredPost)
    <section class="py-16">
        <div class="container mx-auto px-4">
            <div class="text-center mb-12">
                <h2 class="text-3xl font-bold text-gray-800 mb-4">Featured Story</h2>
            </div>
            
            <div class="max-w-6xl mx-auto">
                <div class="bg-white rounded-2xl shadow-xl overflow-hidden">
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-0">
                        <div class="relative h-96 lg:h-auto">
                            <img src="{{ $featuredPost['featured_image'] }}" 
                                 alt="{{ $featuredPost['title'] }}"
                                 class="w-full h-full object-cover">
                            <div class="absolute top-4 left-4">
                                <span class="bg-amber-600 text-white px-3 py-1 rounded-full text-sm font-medium">
                                    {{ $featuredPost['category'] }}
                                </span>
                            </div>
                        </div>
                        <div class="p-8 lg:p-12 flex flex-col justify-center">
                            <div class="flex items-center text-sm text-gray-500 mb-4">
                                <span>{{ $featuredPost['published_at']->format('M d, Y') }}</span>
                                <span class="mx-2">•</span>
                                <span>{{ $featuredPost['reading_time'] }} min read</span>
                            </div>
                            <h3 class="text-3xl font-bold text-gray-800 mb-4 leading-tight">
                                {{ $featuredPost['title'] }}
                            </h3>
                            <p class="text-gray-600 mb-6 leading-relaxed">
                                {{ $featuredPost['excerpt'] }}
                            </p>
                            <div class="flex items-center justify-between">
                                <div class="flex items-center">
                                    <div class="w-10 h-10 bg-amber-200 rounded-full flex items-center justify-center mr-3">
                                        <span class="font-semibold text-amber-800">{{ substr($featuredPost['author'], 0, 1) }}</span>
                                    </div>
                                    <span class="text-gray-700 font-medium">{{ $featuredPost['author'] }}</span>
                                </div>
                                <a href="{{ route('blog.show', $featuredPost['slug']) }}" 
                                   class="bg-amber-600 text-white px-6 py-3 rounded-full font-medium hover:bg-amber-700 transition-colors duration-300">
                                    Read More
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    @endif

    <!-- Blog Posts Grid -->
    <section class="py-16 bg-gray-50">
        <div class="container mx-auto px-4">
            <!-- Category Filter -->
            <div class="flex flex-wrap justify-center mb-12" x-data="{ activeCategory: 'All' }">
                @foreach($categories as $category => $count)
                <button 
                    @click="activeCategory = '{{ $category }}'"
                    :class="{ 'bg-amber-600 text-white': activeCategory === '{{ $category }}', 'bg-white text-gray-700 hover:bg-amber-50': activeCategory !== '{{ $category }}' }"
                    class="px-6 py-3 mx-2 mb-2 rounded-full border border-amber-200 transition-all duration-300 font-medium">
                    {{ $category }} ({{ $count }})
                </button>
                @endforeach
            </div>

            <!-- Posts Grid -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8" x-data="{ activeCategory: 'All' }">
                @foreach($blogPosts as $post)
                <article class="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300 blog-post"
                         data-category="{{ $post['category'] }}"
                         :class="{ 'hidden': activeCategory !== 'All' && activeCategory !== '{{ $post['category'] }}' }">
                    <div class="relative">
                        <img src="{{ $post['featured_image'] }}" 
                             alt="{{ $post['title'] }}"
                             class="w-full h-48 object-cover">
                        <div class="absolute top-4 left-4">
                            <span class="bg-amber-600 text-white px-3 py-1 rounded-full text-sm font-medium">
                                {{ $post['category'] }}
                            </span>
                        </div>
                    </div>
                    
                    <div class="p-6">
                        <div class="flex items-center text-sm text-gray-500 mb-3">
                            <span>{{ $post['published_at']->format('M d, Y') }}</span>
                            <span class="mx-2">•</span>
                            <span>{{ $post['reading_time'] }} min read</span>
                        </div>
                        
                        <h3 class="text-xl font-bold text-gray-800 mb-3 leading-tight">
                            <a href="{{ route('blog.show', $post['slug']) }}" 
                               class="hover:text-amber-600 transition-colors duration-300">
                                {{ $post['title'] }}
                            </a>
                        </h3>
                        
                        <p class="text-gray-600 mb-4 leading-relaxed">
                            {{ $post['excerpt'] }}
                        </p>
                        
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div class="w-8 h-8 bg-amber-200 rounded-full flex items-center justify-center mr-2">
                                    <span class="text-sm font-semibold text-amber-800">{{ substr($post['author'], 0, 1) }}</span>
                                </div>
                                <span class="text-gray-700 text-sm">{{ $post['author'] }}</span>
                            </div>
                            <a href="{{ route('blog.show', $post['slug']) }}" 
                               class="text-amber-600 hover:text-amber-800 font-medium text-sm transition-colors duration-300">
                                Read More →
                            </a>
                        </div>
                        
                        <!-- Tags -->
                        <div class="mt-4 pt-4 border-t border-gray-100">
                            <div class="flex flex-wrap gap-2">
                                @foreach($post['tags'] as $tag)
                                <span class="bg-gray-100 text-gray-600 px-2 py-1 rounded text-xs">
                                    {{ $tag }}
                                </span>
                                @endforeach
                            </div>
                        </div>
                    </div>
                </article>
                @endforeach
            </div>
        </div>
    </section>

    <!-- Newsletter Subscription -->
    <section class="py-16 bg-amber-900 text-white">
        <div class="container mx-auto px-4">
            <div class="max-w-2xl mx-auto text-center">
                <h2 class="text-3xl font-bold mb-6">Stay Updated</h2>
                <p class="text-xl mb-8">
                    Subscribe to our newsletter for the latest safari stories, wildlife updates, and travel tips
                </p>
                
                <form class="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
                    <input type="email" 
                           placeholder="Enter your email address"
                           class="flex-1 px-4 py-3 rounded-full text-gray-800 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-white">
                    <button type="submit" 
                            class="bg-white text-amber-900 px-8 py-3 rounded-full font-semibold hover:bg-gray-100 transition-colors duration-300">
                        Subscribe
                    </button>
                </form>
                
                <p class="text-sm mt-4 text-amber-200">
                    We respect your privacy. Unsubscribe at any time.
                </p>
            </div>
        </div>
    </section>

    <!-- Call to Action -->
    <section class="py-16">
        <div class="container mx-auto px-4 text-center">
            <div class="max-w-3xl mx-auto">
                <h2 class="text-3xl font-bold text-gray-800 mb-6">Ready for Your Own Safari Adventure?</h2>
                <p class="text-xl text-gray-600 mb-8">
                    Experience the magic of African wildlife firsthand at Malombo Safari Lodge
                </p>
                <div class="flex flex-col sm:flex-row gap-4 justify-center">
                    <a href="{{ route('accommodations.index') }}" 
                       class="bg-amber-600 text-white px-8 py-4 rounded-full font-semibold hover:bg-amber-700 transition-colors duration-300">
                        Book Your Stay
                    </a>
                    <a href="{{ route('activities.index') }}" 
                       class="border-2 border-amber-600 text-amber-600 px-8 py-4 rounded-full font-semibold hover:bg-amber-600 hover:text-white transition-colors duration-300">
                        Explore Activities
                    </a>
                </div>
            </div>
        </div>
    </section>
</div>
@endsection
