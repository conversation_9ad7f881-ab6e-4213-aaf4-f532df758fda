<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" class="scroll-smooth">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    
    {{-- SEO Meta Tags --}}
    <title>@yield('title', 'Home') | {{ setting('site_name', 'Malombo Selous Forest Camp') }}</title>
    <meta name="description" content="@yield('meta_description', setting('seo_meta_description', 'Experience luxury eco-lodge accommodation in Nyerere National Park (Selous), Tanzania. Wildlife safaris, boat excursions, and authentic African wilderness adventures.'))">
    <meta name="keywords" content="@yield('meta_keywords', setting('seo_keywords', 'Selous Game Reserve, Nyerere National Park, Tanzania Safari, Eco Lodge, Wildlife, Rufiji River'))">
    <meta name="author" content="{{ setting('site_name', 'Malombo Selous Forest Camp') }}">
    
    {{-- Canonical URL --}}
    <link rel="canonical" href="{{ url()->current() }}">
    
    {{-- Open Graph Tags --}}
    <meta property="og:title" content="@yield('title', 'Home') | {{ setting('site_name', 'Malombo Selous Forest Camp') }}">
    <meta property="og:description" content="@yield('meta_description', setting('seo_meta_description', 'Experience luxury eco-lodge accommodation in Nyerere National Park (Selous), Tanzania.'))">
    <meta property="og:image" content="@yield('og_image', asset(setting('seo_og_image', 'images/malombo_logo.jpg')))">
    <meta property="og:url" content="{{ url()->current() }}">
    <meta property="og:type" content="website">
    <meta property="og:site_name" content="{{ setting('site_name', 'Malombo Selous Forest Camp') }}">
    
    {{-- Twitter Card Tags --}}
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="@yield('title', 'Home') | {{ setting('site_name', 'Malombo Selous Forest Camp') }}">
    <meta name="twitter:description" content="@yield('meta_description', setting('seo_meta_description', 'Experience luxury eco-lodge accommodation in Nyerere National Park (Selous), Tanzania.'))">
    <meta name="twitter:image" content="@yield('og_image', asset(setting('seo_og_image', 'images/malombo_logo.jpg')))">>
    
    {{-- Favicon --}}
    <link rel="icon" type="image/x-icon" href="{{ asset(setting('site_favicon', 'favicon.ico')) }}">
    <link rel="apple-touch-icon" href="{{ asset(setting('site_favicon', 'favicon.ico')) }}">

    {{-- Fonts with display swap for performance --}}
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Playfair+Display:wght@400;500;600;700&family=Crimson+Text:ital,wght@0,400;0,600;1,400&display=swap" rel="stylesheet">

    {{-- Critical CSS for above-the-fold content --}}
    <x-critical-css :page="request()->route()?->getName() ?? 'default'" />

    {{-- Scripts --}}
    @vite(['resources/css/app.css', 'resources/js/app.js'])
    @livewireStyles
    
    {{-- Additional Head Content --}}
    @stack('head')
    @stack('styles')
    
    {{-- Analytics --}}
    @if(setting('analytics_google_id'))
        <!-- Google Analytics -->
        <script async src="https://www.googletagmanager.com/gtag/js?id={{ setting('analytics_google_id') }}"></script>
        <script>
            window.dataLayer = window.dataLayer || [];
            function gtag(){dataLayer.push(arguments);}
            gtag('js', new Date());
            gtag('config', '{{ setting('analytics_google_id') }}');
        </script>
    @endif
    
    @if(setting('analytics_facebook_pixel'))
        <!-- Facebook Pixel -->
        <script>
            !function(f,b,e,v,n,t,s)
            {if(f.fbq)return;n=f.fbq=function(){n.callMethod?
            n.callMethod.apply(n,arguments):n.queue.push(arguments)};
            if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
            n.queue=[];t=b.createElement(e);t.async=!0;
            t.src=v;s=b.getElementsByTagName(e)[0];
            s.parentNode.insertBefore(t,s)}(window, document,'script',
            'https://connect.facebook.net/en_US/fbevents.js');
            fbq('init', '{{ setting('analytics_facebook_pixel') }}');
            fbq('track', 'PageView');
        </script>
        <noscript><img height="1" width="1" style="display:none" src="https://www.facebook.com/tr?id={{ setting('analytics_facebook_pixel') }}&ev=PageView&noscript=1"/></noscript>
    @endif
    
    @if(setting('analytics_google_tag_manager'))
        <!-- Google Tag Manager -->
        <script>(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
        new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
        j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
        'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
        })(window,document,'script','dataLayer','{{ setting('analytics_google_tag_manager') }}');</script>
    @endif
</head>

<body class="font-sans antialiased bg-stone-50 text-stone-900">
    {{-- Skip Navigation Links --}}
    <div class="skip-links">
        <a href="#main-content" class="skip-link">Skip to main content</a>
        <a href="#main-navigation" class="skip-link">Skip to navigation</a>
        <a href="#footer" class="skip-link">Skip to footer</a>
    </div>

    @if(setting('analytics_google_tag_manager'))
        <!-- Google Tag Manager (noscript) -->
        <noscript><iframe src="https://www.googletagmanager.com/ns.html?id={{ setting('analytics_google_tag_manager') }}" height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
    @endif

    <div class="min-h-screen">
        {{-- Navigation --}}
        @include('partials.navigation')

        {{-- Page Heading --}}
        @isset($header)
            <header class="bg-white shadow-soft">
                <div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
                    {{ $header }}
                </div>
            </header>
        @endisset

        {{-- Page Content --}}
        <main id="main-content" class="pt-20">
            @if(isset($slot))
                {{ $slot }}
            @else
                @yield('content')
            @endif
        </main>

        {{-- Footer --}}
        @include('partials.footer')
    </div>

    {{-- WhatsApp Float Button --}}
    @if(setting('contact_phone'))
        <div class="fixed bottom-6 right-6 z-40">
            <a 
                href="https://wa.me/{{ preg_replace('/[^0-9]/', '', setting('contact_phone')) }}?text=Hello, I would like to make an enquiry about Malombo Selous Forest Camp."
                target="_blank"
                rel="noopener noreferrer"
                class="bg-green-500 hover:bg-green-600 text-white rounded-full p-4 shadow-lg transition-all duration-300 hover:scale-110 block"
                aria-label="Contact us on WhatsApp"
            >
                <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488"/>
                </svg>
            </a>
        </div>
    @endif

    {{-- Toast Container --}}
    <div id="toast-container" class="fixed top-4 right-4 z-100 space-y-2"></div>
    
    {{-- Booking/Enquiry Modal --}}
    @include('partials.booking-modal')

    {{-- Scripts --}}
    @stack('scripts')
    @livewireScripts
</body>
</html>
