<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" class="scroll-smooth">
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <meta name="csrf-token" content="{{ csrf_token() }}">

        <!-- SEO Meta Tags -->
        <title>@yield('title', config('malombo.name'))</title>
        <meta name="description" content="@yield('description', config('malombo.seo.description'))">
        <meta name="keywords" content="@yield('keywords', config('malombo.seo.keywords'))">
        
        <!-- Open Graph -->
        <meta property="og:title" content="@yield('title', config('malombo.name'))">
        <meta property="og:description" content="@yield('description', config('malombo.seo.description'))">
        <meta property="og:type" content="website">
        <meta property="og:url" content="{{ url()->current() }}">
        <meta property="og:image" content="@yield('ogImage', asset('images/malombo_logo.jpg'))">
        
        <!-- Twitter Card -->
        <meta name="twitter:card" content="summary_large_image">
        <meta name="twitter:title" content="@yield('title', config('malombo.name'))">
        <meta name="twitter:description" content="@yield('description', config('malombo.seo.description'))">
        <meta name="twitter:image" content="@yield('ogImage', asset('images/malombo_logo.jpg'))">

        <!-- Canonical URL -->
        <link rel="canonical" href="@yield('canonical', url()->current())">

        <!-- Fonts -->
        <link rel="preconnect" href="https://fonts.googleapis.com">
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
        <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Playfair+Display:wght@400;500;600;700&family=Crimson+Text:ital,wght@0,400;0,600;1,400&display=swap" rel="stylesheet">

        <!-- Favicon -->
        <link rel="icon" type="image/x-icon" href="{{ asset('favicon.ico') }}">

        <!-- Scripts -->
        @vite(['resources/css/app.css', 'resources/js/app.js'])

        <!-- Additional Head Content -->
        @stack('head')

        <!-- Analytics -->
        @if(config('malombo.analytics.google_analytics_id'))
            <!-- Google Analytics -->
            <script async src="https://www.googletagmanager.com/gtag/js?id={{ config('malombo.analytics.google_analytics_id') }}"></script>
            <script>
                window.dataLayer = window.dataLayer || [];
                function gtag(){dataLayer.push(arguments);}
                gtag('js', new Date());
                gtag('config', '{{ config('malombo.analytics.google_analytics_id') }}');
            </script>
        @endif

        @if(config('malombo.analytics.google_tag_manager_id'))
            <!-- Google Tag Manager -->
            <script>(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
            new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
            j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
            'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
            })(window,document,'script','dataLayer','{{ config('malombo.analytics.google_tag_manager_id') }}');</script>
        @endif
    </head>
    <body class="font-sans antialiased bg-stone-50 text-stone-900 selection:bg-forest-200 selection:text-forest-900">
        <!-- Skip to content link for accessibility -->
        <a href="#main-content" class="sr-only focus:not-sr-only focus:absolute focus:top-0 focus:left-0 bg-forest-600 text-white px-4 py-2 z-100 rounded-br-lg">
            Skip to main content
        </a>

        @if(config('malombo.analytics.google_tag_manager_id'))
            <!-- Google Tag Manager (noscript) -->
            <noscript><iframe src="https://www.googletagmanager.com/ns.html?id={{ config('malombo.analytics.google_tag_manager_id') }}"
            height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
        @endif

        <div class="min-h-screen">
            @include('components.navbar')

            <!-- Page Content -->
            <main id="main-content" role="main">
                @yield('content')
            </main>

            @include('components.footer')
        </div>

        <!-- Toast Container -->
        <div id="toast-container" class="fixed top-4 right-4 z-100 space-y-2" aria-live="polite" aria-atomic="true"></div>

        <!-- Scripts -->
        @stack('scripts')

        <!-- Initialize Alpine.js components -->
        <script>
            document.addEventListener('alpine:init', () => {
                Alpine.data('toast', () => ({
                    show: false,
                    type: 'success',
                    message: '',
                    
                    showToast(type, message, duration = 5000) {
                        this.type = type;
                        this.message = message;
                        this.show = true;
                        
                        setTimeout(() => {
                            this.show = false;
                        }, duration);
                    }
                }));
            });
        </script>
    </body>
</html>
