{"$schema": "https://getcomposer.org/schema.json", "name": "laravel/laravel", "type": "project", "description": "The skeleton application for the Laravel framework.", "keywords": ["laravel", "framework"], "license": "MIT", "require": {"php": "^8.2", "filament/filament": "^4.0", "filament/spatie-laravel-media-library-plugin": "^4.0", "intervention/image": "^3.11", "laravel/breeze": "^2.3", "laravel/framework": "^12.0", "laravel/tinker": "^2.10.1", "livewire/livewire": "^3.6", "spatie/laravel-medialibrary": "^11.13", "spatie/laravel-permission": "^6.21"}, "require-dev": {"barryvdh/laravel-ide-helper": "^3.6", "fakerphp/faker": "^1.23", "larastan/larastan": "^3.6", "laravel/dusk": "^8.3", "laravel/pail": "^1.2.2", "laravel/pint": "^1.13", "laravel/sail": "^1.41", "mockery/mockery": "^1.6", "nunomaduro/collision": "^8.6", "phpunit/phpunit": "^11.5.3"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}, "files": ["app/helpers.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["@php artisan config:clear", "@php artisan clear-compiled", "@php artisan package:discover --ansi", "@php artisan filament:upgrade"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi", "@php -r \"file_exists('database/database.sqlite') || touch('database/database.sqlite');\"", "@php artisan migrate --graceful --ansi"], "dev": ["Composer\\Config::disableProcessTimeout", "npx concurrently -c \"#93c5fd,#c4b5fd,#fb7185,#fdba74\" \"php artisan serve\" \"php artisan queue:listen --tries=1\" \"php artisan pail --timeout=0\" \"npm run dev\" --names=server,queue,logs,vite"], "test": ["@php artisan config:clear --ansi", "@php artisan test"], "analyse": ["@php vendor/bin/phpstan analyse"], "format": ["@php vendor/bin/pint"], "format-check": ["@php vendor/bin/pint --test"], "ide-helper": ["@php artisan ide-helper:generate", "@php artisan ide-helper:models", "@php artisan ide-helper:meta"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true}}, "minimum-stability": "stable", "prefer-stable": true}