<?php

namespace Tests\Unit\Services;

use PHPUnit\Framework\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Cache;
use App\Services\CacheService;
use App\Models\Accommodation;
use App\Models\Activity;

class CacheServiceTest extends TestCase
{
    use RefreshDatabase;

    protected $cacheService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->cacheService = new CacheService();
    }

    /** @test */
    public function it_can_cache_accommodations()
    {
        $accommodations = Accommodation::factory()->count(3)->create();
        
        $cached = $this->cacheService->getAccommodations();
        
        $this->assertCount(3, $cached);
        $this->assertTrue(Cache::has('accommodations.list'));
    }

    /** @test */
    public function it_can_cache_featured_accommodations()
    {
        Accommodation::factory()->count(2)->create(['is_featured' => true]);
        Accommodation::factory()->count(3)->create(['is_featured' => false]);
        
        $featured = $this->cacheService->getFeaturedAccommodations();
        
        $this->assertCount(2, $featured);
        $this->assertTrue(Cache::has('accommodations.featured'));
    }

    /** @test */
    public function it_can_cache_activities()
    {
        Activity::factory()->count(4)->create();
        
        $cached = $this->cacheService->getActivities();
        
        $this->assertCount(4, $cached);
        $this->assertTrue(Cache::has('activities.list'));
    }

    /** @test */
    public function it_can_warm_up_caches()
    {
        Accommodation::factory()->count(2)->create();
        Activity::factory()->count(3)->create();
        
        $this->cacheService->warmUpAccommodationCache();
        $this->cacheService->warmUpActivityCache();
        
        $this->assertTrue(Cache::has('accommodations.list'));
        $this->assertTrue(Cache::has('accommodations.featured'));
        $this->assertTrue(Cache::has('activities.list'));
    }

    /** @test */
    public function it_can_clear_tagged_cache()
    {
        $this->cacheService->getAccommodations();
        $this->assertTrue(Cache::has('accommodations.list'));
        
        $this->cacheService->clearAccommodationCache();
        $this->assertFalse(Cache::has('accommodations.list'));
    }

    /** @test */
    public function it_can_cache_pages()
    {
        $key = 'page.home';
        $content = '<html><body>Home Page</body></html>';
        
        $this->cacheService->cachePage($key, $content);
        
        $cached = $this->cacheService->getPage($key);
        $this->assertEquals($content, $cached);
    }

    /** @test */
    public function it_can_invalidate_model_cache()
    {
        $this->cacheService->getAccommodations();
        $this->assertTrue(Cache::has('accommodations.list'));
        
        $this->cacheService->invalidateModelCache('accommodation');
        $this->assertFalse(Cache::has('accommodations.list'));
    }

    /** @test */
    public function it_generates_correct_cache_keys()
    {
        $accommodation = Accommodation::factory()->create();
        
        $key = $this->cacheService->getModelCacheKey('accommodation', $accommodation->id);
        $this->assertEquals("accommodations.{$accommodation->id}", $key);
        
        $listKey = $this->cacheService->getModelCacheKey('accommodation');
        $this->assertEquals("accommodations.list", $listKey);
    }

    /** @test */
    public function it_respects_cache_ttl()
    {
        $key = 'test.key';
        $value = 'test value';
        $ttl = 60; // 1 minute
        
        $this->cacheService->put($key, $value, $ttl);
        
        $this->assertTrue(Cache::has($key));
        $this->assertEquals($value, Cache::get($key));
    }

    /** @test */
    public function it_can_cache_with_tags()
    {
        $this->cacheService->putWithTags(['accommodations'], 'test.key', 'test value');
        
        $this->assertTrue(Cache::tags(['accommodations'])->has('test.key'));
        $this->assertEquals('test value', Cache::tags(['accommodations'])->get('test.key'));
    }

    /** @test */
    public function it_can_flush_tagged_cache()
    {
        $this->cacheService->putWithTags(['accommodations'], 'test.key1', 'value1');
        $this->cacheService->putWithTags(['accommodations'], 'test.key2', 'value2');
        $this->cacheService->putWithTags(['activities'], 'test.key3', 'value3');
        
        $this->cacheService->flushTags(['accommodations']);
        
        $this->assertFalse(Cache::tags(['accommodations'])->has('test.key1'));
        $this->assertFalse(Cache::tags(['accommodations'])->has('test.key2'));
        $this->assertTrue(Cache::tags(['activities'])->has('test.key3'));
    }
}
