<?php

namespace Tests\Unit\Services;

use PHPUnit\Framework\TestCase;
use Illuminate\Http\Request;
use App\Services\SEOService;
use App\Models\Accommodation;
use App\Models\Activity;

class SEOServiceTest extends TestCase
{
    protected $seoService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->seoService = new SEOService();
    }

    /** @test */
    public function it_generates_hotel_structured_data()
    {
        $data = $this->seoService->generateHotelStructuredData();
        
        $this->assertEquals('https://schema.org', $data['@context']);
        $this->assertEquals('LodgingBusiness', $data['@type']);
        $this->assertArrayHasKey('name', $data);
        $this->assertArrayHasKey('description', $data);
        $this->assertArrayHasKey('address', $data);
        $this->assertArrayHasKey('geo', $data);
        $this->assertArrayHasKey('amenityFeature', $data);
    }

    /** @test */
    public function it_generates_accommodation_structured_data()
    {
        $accommodation = new Accommodation([
            'name' => 'Luxury Safari Tent',
            'description' => 'A beautiful safari tent with modern amenities',
            'slug' => 'luxury-safari-tent',
            'capacity' => 4,
            'amenities' => ['WiFi', 'Air Conditioning', 'Private Bathroom'],
            'price' => 299.99
        ]);

        $data = $this->seoService->generateAccommodationStructuredData($accommodation);
        
        $this->assertEquals('https://schema.org', $data['@context']);
        $this->assertEquals('Hotel', $data['@type']);
        $this->assertEquals('Luxury Safari Tent', $data['name']);
        $this->assertEquals(4, $data['maximumAttendeeCapacity']);
        $this->assertCount(3, $data['amenityFeature']);
        $this->assertArrayHasKey('offers', $data);
        $this->assertEquals(299.99, $data['offers']['price']);
    }

    /** @test */
    public function it_generates_activity_structured_data()
    {
        $activity = new Activity([
            'name' => 'Game Drive Safari',
            'description' => 'Experience wildlife in their natural habitat',
            'slug' => 'game-drive-safari'
        ]);

        $data = $this->seoService->generateActivityStructuredData($activity);
        
        $this->assertEquals('https://schema.org', $data['@context']);
        $this->assertEquals('TouristAttraction', $data['@type']);
        $this->assertEquals('Game Drive Safari', $data['name']);
        $this->assertArrayHasKey('location', $data);
        $this->assertEquals('Safari Enthusiasts', $data['touristType']);
    }

    /** @test */
    public function it_generates_breadcrumb_structured_data()
    {
        $breadcrumbs = [
            ['name' => 'Home', 'url' => 'https://example.com'],
            ['name' => 'Accommodation', 'url' => 'https://example.com/accommodation'],
            ['name' => 'Luxury Tent', 'url' => 'https://example.com/accommodation/luxury-tent']
        ];

        $data = $this->seoService->generateBreadcrumbStructuredData($breadcrumbs);
        
        $this->assertEquals('https://schema.org', $data['@context']);
        $this->assertEquals('BreadcrumbList', $data['@type']);
        $this->assertCount(3, $data['itemListElement']);
        $this->assertEquals(1, $data['itemListElement'][0]['position']);
        $this->assertEquals('Home', $data['itemListElement'][0]['name']);
    }

    /** @test */
    public function it_generates_organization_structured_data()
    {
        $data = $this->seoService->generateOrganizationStructuredData();
        
        $this->assertEquals('https://schema.org', $data['@context']);
        $this->assertEquals('Organization', $data['@type']);
        $this->assertArrayHasKey('name', $data);
        $this->assertArrayHasKey('url', $data);
        $this->assertArrayHasKey('address', $data);
        $this->assertArrayHasKey('sameAs', $data);
    }

    /** @test */
    public function it_gets_meta_tags_with_defaults()
    {
        $request = Request::create('https://example.com/test');
        
        $metaTags = $this->seoService->getMetaTags($request);
        
        $this->assertArrayHasKey('title', $metaTags);
        $this->assertArrayHasKey('description', $metaTags);
        $this->assertArrayHasKey('keywords', $metaTags);
        $this->assertArrayHasKey('og_image', $metaTags);
        $this->assertArrayHasKey('canonical', $metaTags);
        $this->assertArrayHasKey('robots', $metaTags);
        $this->assertEquals('index,follow', $metaTags['robots']);
    }

    /** @test */
    public function it_overrides_meta_tags()
    {
        $request = Request::create('https://example.com/test');
        $overrides = [
            'title' => 'Custom Title',
            'description' => 'Custom Description',
            'robots' => 'noindex,nofollow'
        ];
        
        $metaTags = $this->seoService->getMetaTags($request, $overrides);
        
        $this->assertEquals('Custom Title', $metaTags['title']);
        $this->assertEquals('Custom Description', $metaTags['description']);
        $this->assertEquals('noindex,nofollow', $metaTags['robots']);
    }

    /** @test */
    public function it_generates_sitemap_xml()
    {
        $xml = $this->seoService->generateSitemap();
        
        $this->assertStringContainsString('<?xml version="1.0" encoding="UTF-8"?>', $xml);
        $this->assertStringContainsString('<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">', $xml);
        $this->assertStringContainsString('<url>', $xml);
        $this->assertStringContainsString('<loc>', $xml);
        $this->assertStringContainsString('<priority>', $xml);
        $this->assertStringContainsString('<changefreq>', $xml);
        $this->assertStringContainsString('</urlset>', $xml);
    }

    /** @test */
    public function it_validates_sitemap_structure()
    {
        $xml = $this->seoService->generateSitemap();
        
        // Parse XML to validate structure
        $doc = new \DOMDocument();
        $this->assertTrue($doc->loadXML($xml));
        
        $xpath = new \DOMXPath($doc);
        $urls = $xpath->query('//url');
        
        $this->assertGreaterThan(0, $urls->length);
        
        foreach ($urls as $url) {
            $loc = $xpath->query('loc', $url);
            $priority = $xpath->query('priority', $url);
            $changefreq = $xpath->query('changefreq', $url);
            
            $this->assertEquals(1, $loc->length);
            $this->assertEquals(1, $priority->length);
            $this->assertEquals(1, $changefreq->length);
        }
    }

    /** @test */
    public function it_caches_structured_data()
    {
        $key = 'test.structured.data';
        $generator = function() {
            return ['test' => 'data'];
        };
        
        $result = $this->seoService->getCachedStructuredData($key, $generator);
        
        $this->assertEquals(['test' => 'data'], $result);
        
        // Should return cached version on second call
        $result2 = $this->seoService->getCachedStructuredData($key, function() {
            return ['different' => 'data'];
        });
        
        $this->assertEquals(['test' => 'data'], $result2);
    }
}
