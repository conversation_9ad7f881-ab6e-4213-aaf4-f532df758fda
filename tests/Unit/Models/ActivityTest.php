<?php

namespace Tests\Unit\Models;

use PHPUnit\Framework\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use App\Models\Activity;
use App\Models\User;

class ActivityTest extends TestCase
{
    use RefreshDatabase;

    /** @test */
    public function it_has_required_attributes()
    {
        $activity = new Activity();
        
        $this->assertContains('name', $activity->getFillable());
        $this->assertContains('slug', $activity->getFillable());
        $this->assertContains('description', $activity->getFillable());
        $this->assertContains('difficulty_level', $activity->getFillable());
    }

    /** @test */
    public function it_can_generate_slug_from_name()
    {
        $activity = Activity::factory()->create([
            'name' => 'Game Drive Safari'
        ]);

        $this->assertEquals('game-drive-safari', $activity->slug);
    }

    /** @test */
    public function it_has_published_scope()
    {
        Activity::factory()->create(['is_published' => true]);
        Activity::factory()->create(['is_published' => false]);

        $publishedCount = Activity::published()->count();
        $this->assertEquals(1, $publishedCount);
    }

    /** @test */
    public function it_has_valid_difficulty_levels()
    {
        $validLevels = ['easy', 'moderate', 'challenging'];
        
        foreach ($validLevels as $level) {
            $activity = Activity::factory()->create(['difficulty_level' => $level]);
            $this->assertContains($activity->difficulty_level, $validLevels);
        }
    }

    /** @test */
    public function it_validates_duration()
    {
        $activity = Activity::factory()->create(['duration' => '3 hours']);
        
        $this->assertNotNull($activity->duration);
        $this->assertIsString($activity->duration);
    }

    /** @test */
    public function it_can_have_equipment_list()
    {
        $activity = Activity::factory()->create([
            'equipment' => ['Binoculars', 'Camera', 'Sun hat', 'Water bottle']
        ]);

        $this->assertIsArray($activity->equipment);
        $this->assertContains('Binoculars', $activity->equipment);
        $this->assertCount(4, $activity->equipment);
    }

    /** @test */
    public function it_can_have_price()
    {
        $activity = Activity::factory()->create(['price' => 149.99]);

        $this->assertIsFloat($activity->price);
        $this->assertGreaterThan(0, $activity->price);
    }

    /** @test */
    public function it_has_valid_group_size()
    {
        $activity = Activity::factory()->create([
            'min_group_size' => 2,
            'max_group_size' => 8
        ]);

        $this->assertGreaterThan(0, $activity->min_group_size);
        $this->assertGreaterThanOrEqual($activity->min_group_size, $activity->max_group_size);
    }

    /** @test */
    public function it_belongs_to_user()
    {
        $user = User::factory()->create();
        $activity = Activity::factory()->create(['user_id' => $user->id]);

        $this->assertInstanceOf(User::class, $activity->user);
        $this->assertEquals($user->id, $activity->user->id);
    }

    /** @test */
    public function it_orders_by_priority()
    {
        $activity1 = Activity::factory()->create(['priority' => 2]);
        $activity2 = Activity::factory()->create(['priority' => 1]);
        $activity3 = Activity::factory()->create(['priority' => 3]);

        $ordered = Activity::byPriority()->get();

        $this->assertEquals($activity2->id, $ordered->first()->id);
        $this->assertEquals($activity3->id, $ordered->last()->id);
    }

    /** @test */
    public function it_can_filter_by_difficulty()
    {
        Activity::factory()->create(['difficulty_level' => 'easy']);
        Activity::factory()->create(['difficulty_level' => 'moderate']);
        Activity::factory()->create(['difficulty_level' => 'challenging']);

        $easyActivities = Activity::where('difficulty_level', 'easy')->count();
        $this->assertEquals(1, $easyActivities);
    }

    /** @test */
    public function it_has_route_key_as_slug()
    {
        $activity = Activity::factory()->create(['slug' => 'game-drive-safari']);

        $this->assertEquals('game-drive-safari', $activity->getRouteKey());
    }
}
