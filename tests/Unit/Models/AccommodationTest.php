<?php

namespace Tests\Unit\Models;

use PHPUnit\Framework\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use App\Models\Accommodation;
use App\Models\User;

class AccommodationTest extends TestCase
{
    use RefreshDatabase;

    /** @test */
    public function it_has_required_attributes()
    {
        $accommodation = new Accommodation();
        
        $this->assertTrue($accommodation->getFillable() !== []);
        $this->assertContains('name', $accommodation->getFillable());
        $this->assertContains('slug', $accommodation->getFillable());
        $this->assertContains('description', $accommodation->getFillable());
    }

    /** @test */
    public function it_can_generate_slug_from_name()
    {
        $accommodation = Accommodation::factory()->create([
            'name' => 'Luxury Safari Tent'
        ]);

        $this->assertEquals('luxury-safari-tent', $accommodation->slug);
    }

    /** @test */
    public function it_has_published_scope()
    {
        Accommodation::factory()->create(['is_published' => true]);
        Accommodation::factory()->create(['is_published' => false]);

        $publishedCount = Accommodation::published()->count();
        $this->assertEquals(1, $publishedCount);
    }

    /** @test */
    public function it_has_featured_scope()
    {
        Accommodation::factory()->create(['is_featured' => true]);
        Accommodation::factory()->create(['is_featured' => false]);

        $featuredCount = Accommodation::featured()->count();
        $this->assertEquals(1, $featuredCount);
    }

    /** @test */
    public function it_orders_by_priority()
    {
        $accommodation1 = Accommodation::factory()->create(['priority' => 2]);
        $accommodation2 = Accommodation::factory()->create(['priority' => 1]);
        $accommodation3 = Accommodation::factory()->create(['priority' => 3]);

        $ordered = Accommodation::byPriority()->get();

        $this->assertEquals($accommodation2->id, $ordered->first()->id);
        $this->assertEquals($accommodation3->id, $ordered->last()->id);
    }

    /** @test */
    public function it_can_have_amenities()
    {
        $accommodation = Accommodation::factory()->create([
            'amenities' => ['WiFi', 'Air Conditioning', 'Private Bathroom']
        ]);

        $this->assertIsArray($accommodation->amenities);
        $this->assertContains('WiFi', $accommodation->amenities);
        $this->assertCount(3, $accommodation->amenities);
    }

    /** @test */
    public function it_validates_capacity()
    {
        $accommodation = new Accommodation([
            'name' => 'Test Accommodation',
            'capacity' => -1
        ]);

        $this->assertFalse($accommodation->save());
    }

    /** @test */
    public function it_has_valid_price_format()
    {
        $accommodation = Accommodation::factory()->create([
            'price' => 299.99
        ]);

        $this->assertIsFloat($accommodation->price);
        $this->assertGreaterThan(0, $accommodation->price);
    }

    /** @test */
    public function it_can_have_images()
    {
        $accommodation = Accommodation::factory()->create([
            'featured_image' => 'accommodation/luxury-tent.jpg',
            'gallery' => [
                'accommodation/luxury-tent-1.jpg',
                'accommodation/luxury-tent-2.jpg'
            ]
        ]);

        $this->assertNotNull($accommodation->featured_image);
        $this->assertIsArray($accommodation->gallery);
        $this->assertCount(2, $accommodation->gallery);
    }

    /** @test */
    public function it_belongs_to_user()
    {
        $user = User::factory()->create();
        $accommodation = Accommodation::factory()->create(['user_id' => $user->id]);

        $this->assertInstanceOf(User::class, $accommodation->user);
        $this->assertEquals($user->id, $accommodation->user->id);
    }

    /** @test */
    public function it_has_route_key_as_slug()
    {
        $accommodation = Accommodation::factory()->create(['slug' => 'luxury-safari-tent']);

        $this->assertEquals('luxury-safari-tent', $accommodation->getRouteKey());
    }

    /** @test */
    public function it_generates_excerpt_from_description()
    {
        $description = str_repeat('This is a long description. ', 50);
        $accommodation = Accommodation::factory()->create(['description' => $description]);

        $excerpt = $accommodation->excerpt;
        
        $this->assertNotNull($excerpt);
        $this->assertLessThanOrEqual(160, strlen($excerpt));
    }
}
