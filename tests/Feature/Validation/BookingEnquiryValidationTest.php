<?php

namespace Tests\Feature\Validation;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;
use App\Http\Requests\BookingEnquiryRequest;
use Illuminate\Support\Facades\Validator;

class BookingEnquiryValidationTest extends TestCase
{
    use RefreshDatabase;

    /** @test */
    public function it_validates_required_fields()
    {
        $request = new BookingEnquiryRequest();
        $validator = Validator::make([], $request->rules());

        $this->assertTrue($validator->fails());
        $this->assertArrayHasKey('arrival_date', $validator->errors()->toArray());
        $this->assertArrayHasKey('departure_date', $validator->errors()->toArray());
        $this->assertArrayHasKey('adults', $validator->errors()->toArray());
        $this->assertArrayHasKey('name', $validator->errors()->toArray());
        $this->assertArray<PERSON>as<PERSON>ey('email', $validator->errors()->toArray());
        $this->assertArrayHasKey('phone', $validator->errors()->toArray());
        $this->assertArrayHasKey('privacy_accepted', $validator->errors()->toArray());
    }

    /** @test */
    public function it_validates_date_formats()
    {
        $request = new BookingEnquiryRequest();
        
        $data = [
            'arrival_date' => 'invalid-date',
            'departure_date' => 'another-invalid-date',
        ];
        
        $validator = Validator::make($data, $request->rules());
        
        $this->assertTrue($validator->fails());
        $this->assertArrayHasKey('arrival_date', $validator->errors()->toArray());
        $this->assertArrayHasKey('departure_date', $validator->errors()->toArray());
    }

    /** @test */
    public function it_validates_future_dates()
    {
        $request = new BookingEnquiryRequest();
        
        $data = [
            'arrival_date' => '2023-01-01', // Past date
            'departure_date' => '2023-01-02',
        ];
        
        $validator = Validator::make($data, $request->rules());
        
        $this->assertTrue($validator->fails());
        $this->assertArrayHasKey('arrival_date', $validator->errors()->toArray());
    }

    /** @test */
    public function it_validates_departure_after_arrival()
    {
        $request = new BookingEnquiryRequest();
        
        $data = [
            'arrival_date' => '2025-12-10',
            'departure_date' => '2025-12-08', // Before arrival
        ];
        
        $validator = Validator::make($data, $request->rules());
        
        $this->assertTrue($validator->fails());
        $this->assertArrayHasKey('departure_date', $validator->errors()->toArray());
    }

    /** @test */
    public function it_validates_guest_numbers()
    {
        $request = new BookingEnquiryRequest();
        
        $data = [
            'adults' => 0, // Must be at least 1
            'children' => -1, // Cannot be negative
        ];
        
        $validator = Validator::make($data, $request->rules());
        
        $this->assertTrue($validator->fails());
        $this->assertArrayHasKey('adults', $validator->errors()->toArray());
        $this->assertArrayHasKey('children', $validator->errors()->toArray());
    }

    /** @test */
    public function it_validates_maximum_guests()
    {
        $request = new BookingEnquiryRequest();
        
        $data = [
            'adults' => 15, // Too many
            'children' => 10, // Too many
        ];
        
        $validator = Validator::make($data, $request->rules());
        
        $this->assertTrue($validator->fails());
        $this->assertArrayHasKey('adults', $validator->errors()->toArray());
        $this->assertArrayHasKey('children', $validator->errors()->toArray());
    }

    /** @test */
    public function it_validates_email_format()
    {
        $request = new BookingEnquiryRequest();
        
        $data = [
            'email' => 'invalid-email',
        ];
        
        $validator = Validator::make($data, $request->rules());
        
        $this->assertTrue($validator->fails());
        $this->assertArrayHasKey('email', $validator->errors()->toArray());
    }

    /** @test */
    public function it_validates_phone_format()
    {
        $request = new BookingEnquiryRequest();
        
        $data = [
            'phone' => 'abc123', // Invalid format
        ];
        
        $validator = Validator::make($data, $request->rules());
        
        $this->assertTrue($validator->fails());
        $this->assertArrayHasKey('phone', $validator->errors()->toArray());
    }

    /** @test */
    public function it_accepts_valid_phone_formats()
    {
        $request = new BookingEnquiryRequest();
        
        $validPhones = [
            '+1234567890',
            '+****************',
            '1234567890',
            '+44 20 7946 0958',
        ];
        
        foreach ($validPhones as $phone) {
            $data = ['phone' => $phone];
            $validator = Validator::make($data, ['phone' => $request->rules()['phone']]);
            
            $this->assertFalse(
                $validator->fails(),
                "Phone number {$phone} should be valid but failed validation"
            );
        }
    }

    /** @test */
    public function it_validates_string_lengths()
    {
        $request = new BookingEnquiryRequest();
        
        $data = [
            'name' => str_repeat('a', 101), // Too long (max 100)
            'special_requests' => str_repeat('a', 1001), // Too long (max 1000)
            'dietary_requirements' => str_repeat('a', 501), // Too long (max 500)
        ];
        
        $validator = Validator::make($data, $request->rules());
        
        $this->assertTrue($validator->fails());
        $this->assertArrayHasKey('name', $validator->errors()->toArray());
        $this->assertArrayHasKey('special_requests', $validator->errors()->toArray());
        $this->assertArrayHasKey('dietary_requirements', $validator->errors()->toArray());
    }

    /** @test */
    public function it_detects_malicious_content()
    {
        $request = new BookingEnquiryRequest();
        
        $maliciousInputs = [
            '<script>alert("xss")</script>',
            'javascript:alert("xss")',
            "'; DROP TABLE users; --",
            '<iframe src="javascript:alert(1)"></iframe>',
        ];
        
        foreach ($maliciousInputs as $maliciousInput) {
            $data = ['name' => $maliciousInput];
            $validator = Validator::make($data, ['name' => $request->rules()['name']]);
            
            $this->assertTrue(
                $validator->fails(),
                "Malicious input '{$maliciousInput}' should fail validation but passed"
            );
        }
    }

    /** @test */
    public function it_validates_honeypot_field()
    {
        $request = new BookingEnquiryRequest();
        
        $data = [
            'website' => 'http://spam.com', // Honeypot field should be empty
        ];
        
        $validator = Validator::make($data, $request->rules());
        
        $this->assertTrue($validator->fails());
        $this->assertArrayHasKey('website', $validator->errors()->toArray());
    }

    /** @test */
    public function it_requires_privacy_acceptance()
    {
        $request = new BookingEnquiryRequest();
        
        $data = [
            'privacy_accepted' => false,
        ];
        
        $validator = Validator::make($data, $request->rules());
        
        $this->assertTrue($validator->fails());
        $this->assertArrayHasKey('privacy_accepted', $validator->errors()->toArray());
    }

    /** @test */
    public function it_passes_with_valid_data()
    {
        $request = new BookingEnquiryRequest();
        
        $validData = [
            'arrival_date' => '2025-12-01',
            'departure_date' => '2025-12-07',
            'adults' => 2,
            'children' => 1,
            'name' => 'John Doe',
            'email' => '<EMAIL>',
            'phone' => '+1234567890',
            'country' => 'United States',
            'special_requests' => 'Vegetarian meals please',
            'dietary_requirements' => 'No nuts',
            'marketing_consent' => true,
            'privacy_accepted' => true,
            'website' => '', // Honeypot field empty
        ];
        
        $validator = Validator::make($validData, $request->rules());
        
        $this->assertFalse($validator->fails(), 
            'Valid data should pass validation. Errors: ' . json_encode($validator->errors()->toArray())
        );
    }
}
