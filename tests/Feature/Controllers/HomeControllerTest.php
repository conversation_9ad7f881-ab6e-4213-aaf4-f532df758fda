<?php

namespace Tests\Feature\Controllers;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;
use App\Models\Accommodation;
use App\Models\Activity;
use App\Models\Facility;

class HomeControllerTest extends TestCase
{
    use RefreshDatabase;

    /** @test */
    public function it_displays_home_page()
    {
        $response = $this->get(route('home'));

        $response->assertStatus(200);
        $response->assertViewIs('public.home.index');
    }

    /** @test */
    public function it_includes_featured_accommodations()
    {
        $featured = Accommodation::factory()->count(2)->featured()->published()->create();
        $regular = Accommodation::factory()->count(3)->published()->create(['is_featured' => false]);

        $response = $this->get(route('home'));

        $response->assertStatus(200);
        $response->assertViewHas('featuredAccommodations');
        
        $featuredAccommodations = $response->viewData('featuredAccommodations');
        $this->assertCount(2, $featuredAccommodations);
    }

    /** @test */
    public function it_limits_featured_accommodations_to_three()
    {
        Accommodation::factory()->count(5)->featured()->published()->create();

        $response = $this->get(route('home'));

        $featuredAccommodations = $response->viewData('featuredAccommodations');
        $this->assertLessThanOrEqual(3, $featuredAccommodations->count());
    }

    /** @test */
    public function it_includes_top_activities()
    {
        Activity::factory()->count(8)->published()->create();

        $response = $this->get(route('home'));

        $response->assertStatus(200);
        $response->assertViewHas('topActivities');
        
        $topActivities = $response->viewData('topActivities');
        $this->assertLessThanOrEqual(6, $topActivities->count());
    }

    /** @test */
    public function it_includes_key_facilities()
    {
        Facility::factory()->count(8)->active()->create();

        $response = $this->get(route('home'));

        $response->assertStatus(200);
        $response->assertViewHas('keyFacilities');
        
        $keyFacilities = $response->viewData('keyFacilities');
        $this->assertLessThanOrEqual(6, $keyFacilities->count());
    }

    /** @test */
    public function it_includes_seo_data()
    {
        $response = $this->get(route('home'));

        $response->assertStatus(200);
        $response->assertViewHas('seoData');
        
        $seoData = $response->viewData('seoData');
        $this->assertArrayHasKey('title', $seoData);
        $this->assertArrayHasKey('description', $seoData);
        $this->assertArrayHasKey('keywords', $seoData);
    }

    /** @test */
    public function it_includes_structured_data()
    {
        $response = $this->get(route('home'));

        $response->assertStatus(200);
        $response->assertViewHas('structuredData');
        
        $structuredData = $response->viewData('structuredData');
        $this->assertArrayHasKey('hotel', $structuredData);
        $this->assertArrayHasKey('organization', $structuredData);
        $this->assertArrayHasKey('breadcrumbs', $structuredData);
    }

    /** @test */
    public function it_has_correct_page_title()
    {
        $response = $this->get(route('home'));

        $seoData = $response->viewData('seoData');
        $this->assertStringContainsString('Luxury Safari Lodge', $seoData['title']);
    }

    /** @test */
    public function it_only_shows_published_content()
    {
        Accommodation::factory()->featured()->published()->create(['name' => 'Published Featured']);
        Accommodation::factory()->featured()->create(['is_published' => false, 'name' => 'Unpublished Featured']);
        
        Activity::factory()->published()->create(['name' => 'Published Activity']);
        Activity::factory()->create(['is_published' => false, 'name' => 'Unpublished Activity']);

        $response = $this->get(route('home'));

        $response->assertStatus(200);
        $response->assertSee('Published Featured');
        $response->assertDontSee('Unpublished Featured');
        $response->assertSee('Published Activity');
        $response->assertDontSee('Unpublished Activity');
    }

    /** @test */
    public function it_handles_empty_content_gracefully()
    {
        // No accommodations, activities, or facilities created
        $response = $this->get(route('home'));

        $response->assertStatus(200);
        $response->assertViewHas('featuredAccommodations');
        $response->assertViewHas('topActivities');
        $response->assertViewHas('keyFacilities');
        
        $this->assertCount(0, $response->viewData('featuredAccommodations'));
        $this->assertCount(0, $response->viewData('topActivities'));
        $this->assertCount(0, $response->viewData('keyFacilities'));
    }

    /** @test */
    public function it_orders_accommodations_by_priority()
    {
        $low = Accommodation::factory()->featured()->published()->create(['priority' => 3]);
        $high = Accommodation::factory()->featured()->published()->create(['priority' => 1]);
        $medium = Accommodation::factory()->featured()->published()->create(['priority' => 2]);

        $response = $this->get(route('home'));

        $featuredAccommodations = $response->viewData('featuredAccommodations');
        $this->assertEquals($high->id, $featuredAccommodations->first()->id);
    }

    /** @test */
    public function it_caches_home_page_data()
    {
        Accommodation::factory()->count(3)->featured()->published()->create();
        Activity::factory()->count(3)->published()->create();

        // First request
        $response1 = $this->get(route('home'));
        $response1->assertStatus(200);

        // Second request should use cached data
        $response2 = $this->get(route('home'));
        $response2->assertStatus(200);
        
        // Data should be identical
        $this->assertEquals(
            $response1->viewData('featuredAccommodations')->pluck('id')->toArray(),
            $response2->viewData('featuredAccommodations')->pluck('id')->toArray()
        );
    }

    /** @test */
    public function it_includes_canonical_url()
    {
        $response = $this->get(route('home'));

        $seoData = $response->viewData('seoData');
        $this->assertEquals(route('home'), $seoData['canonical']);
    }

    /** @test */
    public function it_has_proper_meta_description()
    {
        $response = $this->get(route('home'));

        $seoData = $response->viewData('seoData');
        $this->assertStringContainsString('luxury eco-lodge', $seoData['description']);
        $this->assertStringContainsString('Nyerere National Park', $seoData['description']);
    }
}
