<?php

namespace Tests\Feature\Controllers;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;
use App\Models\Accommodation;
use App\Models\User;

class AccommodationControllerTest extends TestCase
{
    use RefreshDatabase;

    /** @test */
    public function it_displays_accommodation_index_page()
    {
        Accommodation::factory()->count(3)->published()->create();

        $response = $this->get(route('accommodation.index'));

        $response->assertStatus(200);
        $response->assertViewIs('public.accommodation.index');
        $response->assertViewHas('accommodations');
        $response->assertSee('Accommodation');
    }

    /** @test */
    public function it_only_shows_published_accommodations()
    {
        $published = Accommodation::factory()->published()->create(['name' => 'Published Accommodation']);
        $unpublished = Accommodation::factory()->create(['is_published' => false, 'name' => 'Unpublished Accommodation']);

        $response = $this->get(route('accommodation.index'));

        $response->assertStatus(200);
        $response->assertSee('Published Accommodation');
        $response->assertDontSee('Unpublished Accommodation');
    }

    /** @test */
    public function it_displays_accommodation_show_page()
    {
        $accommodation = Accommodation::factory()->published()->create([
            'name' => 'Luxury Safari Tent',
            'slug' => 'luxury-safari-tent'
        ]);

        $response = $this->get(route('accommodation.show', $accommodation->slug));

        $response->assertStatus(200);
        $response->assertViewIs('public.accommodation.show');
        $response->assertViewHas('accommodation');
        $response->assertSee('Luxury Safari Tent');
    }

    /** @test */
    public function it_returns_404_for_unpublished_accommodation()
    {
        $accommodation = Accommodation::factory()->create([
            'slug' => 'unpublished-tent',
            'is_published' => false
        ]);

        $response = $this->get(route('accommodation.show', $accommodation->slug));

        $response->assertStatus(404);
    }

    /** @test */
    public function it_returns_404_for_non_existent_accommodation()
    {
        $response = $this->get(route('accommodation.show', 'non-existent-slug'));

        $response->assertStatus(404);
    }

    /** @test */
    public function accommodation_index_includes_seo_data()
    {
        $response = $this->get(route('accommodation.index'));

        $response->assertStatus(200);
        $response->assertViewHas('seoData');
        $response->assertViewHas('structuredData');
        
        $seoData = $response->viewData('seoData');
        $this->assertArrayHasKey('title', $seoData);
        $this->assertArrayHasKey('description', $seoData);
        $this->assertStringContainsString('Accommodation', $seoData['title']);
    }

    /** @test */
    public function accommodation_show_includes_structured_data()
    {
        $accommodation = Accommodation::factory()->published()->create([
            'name' => 'Test Accommodation',
            'slug' => 'test-accommodation'
        ]);

        $response = $this->get(route('accommodation.show', $accommodation->slug));

        $response->assertStatus(200);
        $response->assertViewHas('structuredData');
        
        $structuredData = $response->viewData('structuredData');
        $this->assertArrayHasKey('accommodation', $structuredData);
        $this->assertArrayHasKey('breadcrumbs', $structuredData);
    }

    /** @test */
    public function it_orders_accommodations_by_priority()
    {
        $accommodation1 = Accommodation::factory()->published()->create(['priority' => 3, 'name' => 'Third']);
        $accommodation2 = Accommodation::factory()->published()->create(['priority' => 1, 'name' => 'First']);
        $accommodation3 = Accommodation::factory()->published()->create(['priority' => 2, 'name' => 'Second']);

        $response = $this->get(route('accommodation.index'));

        $accommodations = $response->viewData('accommodations');
        $this->assertEquals('First', $accommodations->first()->name);
        $this->assertEquals('Third', $accommodations->last()->name);
    }

    /** @test */
    public function accommodation_show_has_correct_meta_tags()
    {
        $accommodation = Accommodation::factory()->published()->create([
            'name' => 'Luxury Safari Tent',
            'slug' => 'luxury-safari-tent',
            'description' => 'A beautiful luxury tent in the heart of the wilderness.'
        ]);

        $response = $this->get(route('accommodation.show', $accommodation->slug));

        $seoData = $response->viewData('seoData');
        $this->assertStringContainsString('Luxury Safari Tent', $seoData['title']);
        $this->assertStringContainsString('luxury tent', $seoData['keywords']);
    }

    /** @test */
    public function it_handles_accommodation_with_no_featured_image()
    {
        $accommodation = Accommodation::factory()->published()->create([
            'featured_image' => null,
            'slug' => 'no-image-tent'
        ]);

        $response = $this->get(route('accommodation.show', $accommodation->slug));

        $response->assertStatus(200);
        $seoData = $response->viewData('seoData');
        $this->assertNull($seoData['og_image']);
    }

    /** @test */
    public function it_caches_accommodation_responses()
    {
        $accommodation = Accommodation::factory()->published()->create(['slug' => 'cached-tent']);

        // First request
        $response1 = $this->get(route('accommodation.show', $accommodation->slug));
        $response1->assertStatus(200);

        // Second request should be faster (cached)
        $response2 = $this->get(route('accommodation.show', $accommodation->slug));
        $response2->assertStatus(200);
    }
}
