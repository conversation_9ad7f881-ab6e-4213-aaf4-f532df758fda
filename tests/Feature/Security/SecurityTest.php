<?php

namespace Tests\Feature\Security;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;

class SecurityTest extends TestCase
{
    use RefreshDatabase;

    /** @test */
    public function it_has_csrf_protection_on_forms()
    {
        $response = $this->post(route('contact.submitEnquiry'), [
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'message' => 'Test message'
        ]);

        $response->assertStatus(419); // CSRF token mismatch
    }

    /** @test */
    public function it_validates_file_types_on_upload()
    {
        Storage::fake('public');

        // Test with malicious file
        $maliciousFile = UploadedFile::fake()->create('malicious.php', 100);

        $response = $this->withSession(['_token' => 'test-token'])
                         ->post('/admin/media/upload', [
                             '_token' => 'test-token',
                             'file' => $maliciousFile
                         ]);

        $response->assertSessionHasErrors(['file']);
    }

    /** @test */
    public function it_validates_file_sizes()
    {
        Storage::fake('public');

        // Create file larger than allowed limit (assume 5MB limit)
        $largeFile = UploadedFile::fake()->create('large-image.jpg', 6000); // 6MB

        $response = $this->withSession(['_token' => 'test-token'])
                         ->post('/admin/media/upload', [
                             '_token' => 'test-token',
                             'file' => $largeFile
                         ]);

        $response->assertSessionHasErrors(['file']);
    }

    /** @test */
    public function it_allows_valid_image_uploads()
    {
        Storage::fake('public');

        $validImage = UploadedFile::fake()->image('test-image.jpg', 800, 600);

        $response = $this->actingAs($this->createUser())
                         ->withSession(['_token' => 'test-token'])
                         ->post('/admin/media/upload', [
                             '_token' => 'test-token',
                             'file' => $validImage
                         ]);

        // Should not have file validation errors
        $response->assertSessionDoesntHaveErrors(['file']);
    }

    /** @test */
    public function it_sanitizes_user_input()
    {
        $maliciousInput = '<script>alert("xss")</script>Test Name';

        $response = $this->withSession(['_token' => 'test-token'])
                         ->post(route('contact.submitEnquiry'), [
                             '_token' => 'test-token',
                             'name' => $maliciousInput,
                             'email' => '<EMAIL>',
                             'message' => 'Test message'
                         ]);

        // Check that script tags are not executed or stored
        $this->assertDatabaseMissing('enquiries', [
            'name' => $maliciousInput
        ]);
    }

    /** @test */
    public function it_has_security_headers()
    {
        $response = $this->get(route('home'));

        $response->assertHeader('X-Content-Type-Options', 'nosniff');
        $response->assertHeader('X-Frame-Options', 'SAMEORIGIN');
        $response->assertHeader('X-XSS-Protection', '1; mode=block');
    }

    /** @test */
    public function it_has_content_security_policy()
    {
        $response = $this->get(route('home'));

        // Check for CSP header
        $this->assertTrue($response->headers->has('Content-Security-Policy') || 
                         $response->headers->has('Content-Security-Policy-Report-Only'));
    }

    /** @test */
    public function it_validates_email_format()
    {
        $response = $this->withSession(['_token' => 'test-token'])
                         ->post(route('contact.submitEnquiry'), [
                             '_token' => 'test-token',
                             'name' => 'Test User',
                             'email' => 'invalid-email',
                             'message' => 'Test message'
                         ]);

        $response->assertSessionHasErrors(['email']);
    }

    /** @test */
    public function it_validates_required_fields()
    {
        $response = $this->withSession(['_token' => 'test-token'])
                         ->post(route('contact.submitEnquiry'), [
                             '_token' => 'test-token',
                             'name' => '',
                             'email' => '',
                             'message' => ''
                         ]);

        $response->assertSessionHasErrors(['name', 'email', 'message']);
    }

    /** @test */
    public function it_prevents_sql_injection()
    {
        $sqlInjection = "'; DROP TABLE users; --";

        $response = $this->withSession(['_token' => 'test-token'])
                         ->post(route('contact.submitEnquiry'), [
                             '_token' => 'test-token',
                             'name' => $sqlInjection,
                             'email' => '<EMAIL>',
                             'message' => 'Test message'
                         ]);

        // Database should still exist and be intact
        $this->assertDatabaseMissing('users', []);
        $this->assertTrue(\Schema::hasTable('users'));
    }

    /** @test */
    public function it_rate_limits_form_submissions()
    {
        $data = [
            '_token' => 'test-token',
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'message' => 'Test message'
        ];

        // Submit multiple requests rapidly
        for ($i = 0; $i < 10; $i++) {
            $response = $this->withSession(['_token' => 'test-token'])
                             ->post(route('contact.submitEnquiry'), $data);
        }

        // Should eventually get rate limited
        $response->assertStatus(429);
    }

    /** @test */
    public function it_validates_honeypot_fields()
    {
        // Honeypot field filled (indicates bot)
        $response = $this->withSession(['_token' => 'test-token'])
                         ->post(route('contact.submitEnquiry'), [
                             '_token' => 'test-token',
                             'name' => 'Test User',
                             'email' => '<EMAIL>',
                             'message' => 'Test message',
                             'website' => 'http://spam.com' // Honeypot field
                         ]);

        $response->assertSessionHasErrors(['website']);
    }

    /** @test */
    public function it_blocks_suspicious_user_agents()
    {
        $response = $this->withHeaders([
            'User-Agent' => 'malicious-bot'
        ])->get(route('home'));

        // Should block or handle suspicious user agents
        $this->assertTrue($response->status() >= 400 || $response->status() === 200);
    }

    /** @test */
    public function it_validates_admin_access()
    {
        $response = $this->get('/admin');

        // Should redirect to login for unauthenticated users
        $response->assertRedirect('/login');
    }

    /** @test */
    public function it_enforces_password_rules()
    {
        $response = $this->post('/register', [
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'password' => '123', // Weak password
            'password_confirmation' => '123'
        ]);

        $response->assertSessionHasErrors(['password']);
    }

    /** @test */
    public function it_prevents_mass_assignment()
    {
        $user = $this->createUser();

        $response = $this->actingAs($user)
                         ->withSession(['_token' => 'test-token'])
                         ->patch('/profile', [
                             '_token' => 'test-token',
                             'name' => 'Updated Name',
                             'is_admin' => true // Should not be mass assignable
                         ]);

        $user->refresh();
        $this->assertEquals('Updated Name', $user->name);
        $this->assertFalse($user->is_admin ?? false);
    }

    protected function createUser()
    {
        return \App\Models\User::factory()->create();
    }
}
