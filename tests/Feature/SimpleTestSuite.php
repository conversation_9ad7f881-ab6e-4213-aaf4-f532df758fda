<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;
use App\Models\User;
use App\Models\Accommodation;
use App\Models\Activity;
use App\Models\Facility;
use App\Http\Middleware\SecurityHeadersMiddleware;
use App\Rules\NoMaliciousContent;
use App\Http\Requests\BookingEnquiryRequest;
use Illuminate\Support\Facades\Validator;

class SimpleTestSuite extends TestCase
{
    use RefreshDatabase;

    /** @test */
    public function it_loads_the_home_page()
    {
        $response = $this->get('/');
        $response->assertSuccessful();
    }

    /** @test */
    public function security_middleware_adds_headers()
    {
        $response = $this->get('/');
        
        $response->assertHeader('X-Frame-Options', 'DENY');
        $response->assertHeader('X-Content-Type-Options', 'nosniff');
        $response->assertHeader('X-XSS-Protection', '1; mode=block');
        $response->assertHeader('Referrer-Policy', 'strict-origin-when-cross-origin');
    }

    /** @test */
    public function no_malicious_content_rule_works()
    {
        $rule = new NoMaliciousContent();
        
        // Test malicious content
        $maliciousInputs = [
            '<script>alert("xss")</script>',
            'javascript:alert(1)',
            "'; DROP TABLE users; --",
        ];
        
        foreach ($maliciousInputs as $input) {
            $this->assertFalse(
                $rule->passes('test', $input),
                "Malicious input '{$input}' should be rejected"
            );
        }
        
        // Test clean content
        $cleanInputs = [
            'John Doe',
            'This is a normal message.',
            'I would like to book a room.',
        ];
        
        foreach ($cleanInputs as $input) {
            $this->assertTrue(
                $rule->passes('test', $input),
                "Clean input '{$input}' should be accepted"
            );
        }
    }

    /** @test */
    public function booking_enquiry_validation_works()
    {
        $request = new BookingEnquiryRequest();
        
        // Test invalid data
        $invalidData = [
            'arrival_date' => '2023-01-01', // Past date
            'departure_date' => '2023-01-01', // Same as arrival
            'adults' => 0, // Below minimum
            'children' => -1, // Negative
            'name' => '', // Empty
            'email' => 'invalid-email', // Invalid format
            'phone' => '', // Empty
            'privacy_accepted' => false, // Not accepted
        ];
        
        $validator = Validator::make($invalidData, $request->rules());
        $this->assertTrue($validator->fails());
        
        // Test valid data
        $validData = [
            'arrival_date' => '2025-12-01',
            'departure_date' => '2025-12-07',
            'adults' => 2,
            'children' => 1,
            'name' => 'John Doe',
            'email' => '<EMAIL>',
            'phone' => '+1234567890',
            'country' => 'United States',
            'special_requests' => 'Vegetarian meals',
            'dietary_requirements' => 'No nuts',
            'marketing_consent' => true,
            'privacy_accepted' => true,
            'website' => '', // Honeypot empty
        ];
        
        $validator = Validator::make($validData, $request->rules());
        $this->assertFalse($validator->fails());
    }

    /** @test */
    public function factories_create_valid_models()
    {
        // Test Accommodation factory
        $accommodation = Accommodation::factory()->create();
        $this->assertDatabaseHas('accommodations', [
            'id' => $accommodation->id,
            'title' => $accommodation->title,
        ]);

        // Test Activity factory
        $activity = Activity::factory()->create();
        $this->assertDatabaseHas('activities', [
            'id' => $activity->id,
            'title' => $activity->title,
        ]);

        // Test Facility factory
        $facility = Facility::factory()->create();
        $this->assertDatabaseHas('facilities', [
            'id' => $facility->id,
            'title' => $facility->title,
        ]);
    }

    /** @test */
    public function accommodation_scopes_work()
    {
        // Create test data
        $published = Accommodation::factory()->published()->create();
        $unpublished = Accommodation::factory()->unpublished()->create();
        $featured = Accommodation::factory()->featured()->published()->create();

        // Test published scope
        $publishedAccommodations = Accommodation::published()->get();
        $this->assertTrue($publishedAccommodations->contains($published));
        $this->assertTrue($publishedAccommodations->contains($featured));
        $this->assertFalse($publishedAccommodations->contains($unpublished));

        // Test featured scope
        $featuredAccommodations = Accommodation::featured()->get();
        $this->assertTrue($featuredAccommodations->contains($featured));
        $this->assertFalse($featuredAccommodations->contains($published));
    }

    /** @test */
    public function models_generate_correct_slugs()
    {
        $accommodation = Accommodation::factory()->create(['title' => 'Safari Tree House']);
        $this->assertEquals('safari-tree-house', $accommodation->slug);

        $activity = Activity::factory()->create(['title' => 'Wildlife Game Drive']);
        $this->assertEquals('wildlife-game-drive', $activity->slug);

        $facility = Facility::factory()->create(['title' => 'Swimming Pool']);
        $this->assertEquals('swimming-pool', $facility->slug);
    }

    /** @test */
    public function csrf_protection_is_active()
    {
        // Attempt to submit form without CSRF token
        $response = $this->post('/contact', [
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'subject' => 'Test Subject',
            'message' => 'Test message',
        ]);

        // Should be redirected due to CSRF failure
        $response->assertStatus(419);
    }

    /** @test */
    public function application_handles_errors_gracefully()
    {
        // Test 404 page
        $response = $this->get('/non-existent-page');
        $response->assertStatus(404);

        // Test validation errors
        $response = $this->post('/contact', []);
        $response->assertSessionHasErrors();
    }

    /** @test */
    public function security_headers_middleware_configuration()
    {
        $middleware = new SecurityHeadersMiddleware();
        
        // Create a mock request and response
        $request = $this->app['request'];
        $response = response('Test');
        
        // Test that middleware adds security headers
        $result = $middleware->handle($request, function () use ($response) {
            return $response;
        });
        
        $this->assertNotNull($result->headers->get('X-Frame-Options'));
        $this->assertNotNull($result->headers->get('X-Content-Type-Options'));
        $this->assertNotNull($result->headers->get('X-XSS-Protection'));
    }
}
