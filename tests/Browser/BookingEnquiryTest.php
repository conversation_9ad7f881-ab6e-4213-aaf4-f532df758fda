<?php

namespace Tests\Browser;

use Illuminate\Foundation\Testing\DatabaseMigrations;
use <PERSON><PERSON>\Dusk\Browser;
use Tests\DuskTestCase;
use App\Models\Accommodation;
use App\Models\Activity;

class BookingEnquiryTest extends DuskTestCase
{
    use DatabaseMigrations;

    /** @test */
    public function user_can_open_booking_modal()
    {
        $this->browse(function (Browser $browser) {
            $browser->visit('/')
                    ->waitFor('.hero-section')
                    ->click('button:contains("Book Your Safari")')
                    ->waitFor('[x-show="showModal"]', 5)
                    ->assertVisible('[x-show="showModal"]')
                    ->assertSee('Book Your Safari Experience');
        });
    }

    /** @test */
    public function user_can_complete_booking_enquiry_flow()
    {
        Accommodation::factory()->published()->create([
            'name' => 'Luxury Safari Tent',
            'slug' => 'luxury-safari-tent'
        ]);

        Activity::factory()->published()->create([
            'name' => 'Game Drive',
            'slug' => 'game-drive'
        ]);

        $this->browse(function (Browser $browser) {
            $browser->visit('/')
                    ->click('button:contains("Book Your Safari")')
                    ->waitFor('[x-show="showModal"]', 5)
                    
                    // Step 1: Travel Details
                    ->type('input[name="arrival_date"]', '2025-12-01')
                    ->type('input[name="departure_date"]', '2025-12-07')
                    ->select('select[name="adults"]', '2')
                    ->select('select[name="children"]', '1')
                    ->click('button:contains("Next")')
                    
                    // Step 2: Accommodation Selection
                    ->waitFor('.step-2', 2)
                    ->click('input[value="luxury-safari-tent"]')
                    ->click('button:contains("Next")')
                    
                    // Step 3: Activities Selection
                    ->waitFor('.step-3', 2)
                    ->click('input[value="game-drive"]')
                    ->click('button:contains("Next")')
                    
                    // Step 4: Contact Information
                    ->waitFor('.step-4', 2)
                    ->type('input[name="name"]', 'John Doe')
                    ->type('input[name="email"]', '<EMAIL>')
                    ->type('input[name="phone"]', '+1234567890')
                    ->select('select[name="country"]', 'United States')
                    ->click('button:contains("Next")')
                    
                    // Step 5: Review and Submit
                    ->waitFor('.step-5', 2)
                    ->assertSee('John Doe')
                    ->assertSee('<EMAIL>')
                    ->assertSee('Luxury Safari Tent')
                    ->assertSee('Game Drive')
                    ->type('textarea[name="special_requests"]', 'Vegetarian meals please')
                    ->click('button:contains("Submit Enquiry")')
                    
                    // Success page
                    ->waitFor('.success-message', 10)
                    ->assertSee('Thank you for your enquiry')
                    ->assertSee('ENQ-');
        });
    }

    /** @test */
    public function user_can_navigate_between_steps()
    {
        $this->browse(function (Browser $browser) {
            $browser->visit('/')
                    ->click('button:contains("Book Your Safari")')
                    ->waitFor('[x-show="showModal"]', 5)
                    
                    // Fill step 1 and go to step 2
                    ->type('input[name="arrival_date"]', '2025-12-01')
                    ->type('input[name="departure_date"]', '2025-12-07')
                    ->click('button:contains("Next")')
                    ->waitFor('.step-2', 2)
                    
                    // Go back to step 1
                    ->click('button:contains("Previous")')
                    ->waitFor('.step-1', 2)
                    ->assertValue('input[name="arrival_date"]', '2025-12-01')
                    
                    // Progress indicator should show correct step
                    ->assertAttribute('.step-indicator .step:first-child', 'class', '*active*');
        });
    }

    /** @test */
    public function booking_form_validates_required_fields()
    {
        $this->browse(function (Browser $browser) {
            $browser->visit('/')
                    ->click('button:contains("Book Your Safari")')
                    ->waitFor('[x-show="showModal"]', 5)
                    
                    // Try to proceed without filling required fields
                    ->click('button:contains("Next")')
                    ->waitFor('.error-message', 2)
                    ->assertSee('arrival date is required')
                    ->assertSee('departure date is required');
        });
    }

    /** @test */
    public function user_can_close_booking_modal()
    {
        $this->browse(function (Browser $browser) {
            $browser->visit('/')
                    ->click('button:contains("Book Your Safari")')
                    ->waitFor('[x-show="showModal"]', 5)
                    ->assertVisible('[x-show="showModal"]')
                    
                    // Close with X button
                    ->click('.modal-close')
                    ->waitUntilMissing('[x-show="showModal"]', 3)
                    ->assertMissing('[x-show="showModal"]');
        });
    }

    /** @test */
    public function booking_form_shows_loading_state()
    {
        Accommodation::factory()->published()->create(['name' => 'Test Tent']);

        $this->browse(function (Browser $browser) {
            $browser->visit('/')
                    ->click('button:contains("Book Your Safari")')
                    ->waitFor('[x-show="showModal"]', 5)
                    
                    // Fill all steps quickly
                    ->type('input[name="arrival_date"]', '2025-12-01')
                    ->type('input[name="departure_date"]', '2025-12-07')
                    ->click('button:contains("Next")')
                    ->waitFor('.step-2', 2)
                    ->click('button:contains("Next")') // Skip accommodation
                    ->waitFor('.step-3', 2)
                    ->click('button:contains("Next")') // Skip activities
                    ->waitFor('.step-4', 2)
                    ->type('input[name="name"]', 'John Doe')
                    ->type('input[name="email"]', '<EMAIL>')
                    ->click('button:contains("Next")')
                    ->waitFor('.step-5', 2)
                    ->click('button:contains("Submit Enquiry")')
                    
                    // Should show loading state
                    ->waitFor('.loading-spinner', 2)
                    ->assertSee('Submitting...');
        });
    }

    /** @test */
    public function booking_form_handles_honeypot_spam_protection()
    {
        $this->browse(function (Browser $browser) {
            $browser->visit('/')
                    ->click('button:contains("Book Your Safari")')
                    ->waitFor('[x-show="showModal"]', 5)
                    
                    // Fill honeypot field (this should be hidden from normal users)
                    ->script('document.querySelector(\'input[name="website"]\').value = "http://spam.com"');
                    
            // Continue with normal form submission
            $browser->type('input[name="arrival_date"]', '2025-12-01')
                    ->type('input[name="departure_date"]', '2025-12-07')
                    ->click('button:contains("Next")')
                    ->waitFor('.step-2', 2);
                    
            // Skip to final step
            $browser->script('window.livewire.find(window.livewire.components.all()[0].id).set("currentStep", 4)');
            
            $browser->type('input[name="name"]', 'Spam Bot')
                    ->type('input[name="email"]', '<EMAIL>')
                    ->click('button:contains("Next")')
                    ->waitFor('.step-5', 2)
                    ->click('button:contains("Submit Enquiry")')
                    
                    // Should show spam detection error
                    ->waitFor('.error-message', 5)
                    ->assertSee('spam');
        });
    }
}
