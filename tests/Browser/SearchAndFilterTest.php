<?php

namespace Tests\Browser;

use Illuminate\Foundation\Testing\DatabaseMigrations;
use <PERSON><PERSON>\Dusk\Browser;
use Tests\DuskTestCase;
use App\Models\Accommodation;
use App\Models\Activity;

class SearchAndFilterTest extends DuskTestCase
{
    use DatabaseMigrations;

    /** @test */
    public function user_can_search_accommodations()
    {
        $luxury = Accommodation::factory()->published()->create([
            'name' => 'Luxury Safari Tent',
            'description' => 'A luxurious tent with modern amenities'
        ]);

        $budget = Accommodation::factory()->published()->create([
            'name' => 'Budget Camp',
            'description' => 'Affordable camping experience'
        ]);

        $this->browse(function (Browser $browser) {
            $browser->visit('/accommodation')
                    ->waitFor('.accommodation-grid', 3)
                    ->assertSee('Luxury Safari Tent')
                    ->assertSee('Budget Camp')
                    
                    // Search for luxury
                    ->type('input[name="search"]', 'luxury')
                    ->keys('input[name="search"]', '{enter}')
                    ->waitFor('.search-results', 3)
                    ->assertSee('Luxury Safari Tent')
                    ->assertDontSee('Budget Camp');
        });
    }

    /** @test */
    public function user_can_filter_accommodations_by_capacity()
    {
        $small = Accommodation::factory()->published()->create([
            'name' => 'Cozy Tent',
            'capacity' => 2
        ]);

        $large = Accommodation::factory()->published()->create([
            'name' => 'Family Lodge',
            'capacity' => 6
        ]);

        $this->browse(function (Browser $browser) {
            $browser->visit('/accommodation')
                    ->waitFor('.accommodation-grid', 3)
                    ->assertSee('Cozy Tent')
                    ->assertSee('Family Lodge')
                    
                    // Filter by capacity
                    ->select('select[name="capacity"]', '4+')
                    ->waitFor('.filter-results', 3)
                    ->assertDontSee('Cozy Tent')
                    ->assertSee('Family Lodge');
        });
    }

    /** @test */
    public function user_can_filter_activities_by_difficulty()
    {
        $easy = Activity::factory()->published()->create([
            'name' => 'Nature Walk',
            'difficulty_level' => 'easy'
        ]);

        $challenging = Activity::factory()->published()->create([
            'name' => 'Mountain Hike',
            'difficulty_level' => 'challenging'
        ]);

        $this->browse(function (Browser $browser) {
            $browser->visit('/activities')
                    ->waitFor('.activity-grid', 3)
                    ->assertSee('Nature Walk')
                    ->assertSee('Mountain Hike')
                    
                    // Filter by difficulty
                    ->click('input[value="easy"]')
                    ->waitFor('.filter-results', 3)
                    ->assertSee('Nature Walk')
                    ->assertDontSee('Mountain Hike');
        });
    }

    /** @test */
    public function user_can_sort_accommodations_by_price()
    {
        $expensive = Accommodation::factory()->published()->create([
            'name' => 'Luxury Suite',
            'price' => 500.00
        ]);

        $affordable = Accommodation::factory()->published()->create([
            'name' => 'Standard Tent',
            'price' => 150.00
        ]);

        $this->browse(function (Browser $browser) {
            $browser->visit('/accommodation')
                    ->waitFor('.accommodation-grid', 3)
                    
                    // Sort by price low to high
                    ->select('select[name="sort"]', 'price_asc')
                    ->waitFor('.sort-results', 3);
                    
            // Check that affordable appears first
            $firstResult = $browser->text('.accommodation-card:first-child .accommodation-name');
            $browser->assertEquals('Standard Tent', $firstResult);
        });
    }

    /** @test */
    public function filters_work_together()
    {
        // Create accommodations with different attributes
        $match = Accommodation::factory()->published()->create([
            'name' => 'Perfect Match',
            'capacity' => 4,
            'price' => 250.00,
            'amenities' => ['WiFi', 'AC']
        ]);

        $noMatch = Accommodation::factory()->published()->create([
            'name' => 'No Match',
            'capacity' => 2,
            'price' => 400.00,
            'amenities' => ['Basic']
        ]);

        $this->browse(function (Browser $browser) {
            $browser->visit('/accommodation')
                    ->waitFor('.accommodation-grid', 3)
                    ->assertSee('Perfect Match')
                    ->assertSee('No Match')
                    
                    // Apply multiple filters
                    ->select('select[name="capacity"]', '4')
                    ->select('select[name="price_range"]', '200-300')
                    ->click('input[value="WiFi"]')
                    ->waitFor('.filter-results', 3)
                    ->assertSee('Perfect Match')
                    ->assertDontSee('No Match');
        });
    }

    /** @test */
    public function user_can_clear_filters()
    {
        Accommodation::factory()->published()->count(3)->create();

        $this->browse(function (Browser $browser) {
            $browser->visit('/accommodation')
                    ->waitFor('.accommodation-grid', 3)
                    
                    // Apply some filters
                    ->select('select[name="capacity"]', '4+')
                    ->click('input[value="WiFi"]')
                    ->waitFor('.filter-results', 3)
                    
                    // Clear all filters
                    ->click('button:contains("Clear Filters")')
                    ->waitFor('.accommodation-grid', 3);
                    
            // Should show all accommodations again
            $accommodationCount = $browser->elements('.accommodation-card');
            $browser->assertEquals(3, count($accommodationCount));
        });
    }

    /** @test */
    public function search_shows_no_results_message()
    {
        Accommodation::factory()->published()->create(['name' => 'Safari Tent']);

        $this->browse(function (Browser $browser) {
            $browser->visit('/accommodation')
                    ->waitFor('.accommodation-grid', 3)
                    ->type('input[name="search"]', 'nonexistent')
                    ->keys('input[name="search"]', '{enter}')
                    ->waitFor('.no-results', 3)
                    ->assertSee('No accommodations found')
                    ->assertSee('Try adjusting your search');
        });
    }

    /** @test */
    public function url_parameters_persist_filters()
    {
        Accommodation::factory()->published()->create([
            'name' => 'Luxury Tent',
            'capacity' => 4
        ]);

        $this->browse(function (Browser $browser) {
            $browser->visit('/accommodation?capacity=4&amenities=WiFi')
                    ->waitFor('.accommodation-grid', 3)
                    
                    // Filters should be pre-selected from URL
                    ->assertSelected('select[name="capacity"]', '4')
                    ->assertChecked('input[value="WiFi"]');
        });
    }

    /** @test */
    public function search_is_case_insensitive()
    {
        Accommodation::factory()->published()->create([
            'name' => 'LUXURY Safari Tent'
        ]);

        $this->browse(function (Browser $browser) {
            $browser->visit('/accommodation')
                    ->waitFor('.accommodation-grid', 3)
                    ->type('input[name="search"]', 'luxury')
                    ->keys('input[name="search"]', '{enter}')
                    ->waitFor('.search-results', 3)
                    ->assertSee('LUXURY Safari Tent');
        });
    }

    /** @test */
    public function autocomplete_suggestions_work()
    {
        Accommodation::factory()->published()->create(['name' => 'Luxury Safari Tent']);
        Accommodation::factory()->published()->create(['name' => 'Luxury River Lodge']);

        $this->browse(function (Browser $browser) {
            $browser->visit('/accommodation')
                    ->waitFor('.accommodation-grid', 3)
                    ->type('input[name="search"]', 'lux')
                    ->waitFor('.autocomplete-suggestions', 3)
                    ->assertSee('Luxury Safari Tent')
                    ->assertSee('Luxury River Lodge')
                    ->click('.suggestion:contains("Luxury Safari Tent")')
                    ->waitFor('.search-results', 3)
                    ->assertSee('Luxury Safari Tent')
                    ->assertDontSee('Luxury River Lodge');
        });
    }

    /** @test */
    public function pagination_works_with_filters()
    {
        // Create many accommodations
        Accommodation::factory()->published()->count(25)->create(['capacity' => 4]);

        $this->browse(function (Browser $browser) {
            $browser->visit('/accommodation')
                    ->waitFor('.accommodation-grid', 3)
                    ->select('select[name="capacity"]', '4')
                    ->waitFor('.filter-results', 3)
                    ->assertSee('Next')
                    ->click('a:contains("Next")')
                    ->waitFor('.accommodation-grid', 3)
                    
                    // Should still have filters applied on page 2
                    ->assertSelected('select[name="capacity"]', '4');
        });
    }
}
